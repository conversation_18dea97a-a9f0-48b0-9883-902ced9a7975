import { A } from "@solidjs/router";
import { ChevronRight, Search } from "lucide-solid";
import { Show } from "solid-js";
import { StructuredCategoryData } from "~/types/dto";

export default function CollectionGalleryPageHeader(props: {
	collectionsRoot: {
		text: string;
		slug: string;
	};
	collection?: StructuredCategoryData;
	subCollection?: StructuredCategoryData;
	searchFieldRef?: HTMLInputElement;
	keyword?: string;
}) {
	const linkClassName =
		"inline-flex items-center text-sm text-gray-700 hover:text-secondary dark:text-gray-400 dark:hover:text-white";

	const spanClassName =
		"text-sm text-gray-500 dark:text-gray-600 dark:hover:text-white";

	return (
		<header class="fixed z-1 w-full bg-white px-[1rem] pt-[1rem] pb-[1rem] shadow-md 2xl:px-0">
			<div class="md:container md:flex">
				<div>
					<h4 class="text-primary text-2xl font-bold">Price Lists</h4>
					<nav class="mt-1 mb-1 flex items-center" aria-label="Breadcrumb">
						<ul class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
							<li class="inline-flex items-center">
								<A href="/" class={linkClassName}>
									Dashboard
								</A>
							</li>

							<li class="ml-1">
								<ChevronRight class="h-3 w-3" />
							</li>

							<li class="ml-1 inline-flex items-center">
								<Show
									when={props.collection}
									fallback={
										<span class={spanClassName}>
											{props.collectionsRoot.text}
										</span>
									}
								>
									<A
										href={`/${props.collectionsRoot.slug}/`}
										class={linkClassName}
									>
										{props.collectionsRoot.text}
									</A>
								</Show>
							</li>

							<Show when={props.collection}>
								<li class="ml-1">
									<ChevronRight class="h-3 w-3" />
								</li>

								<li class="ml-1 inline-flex items-center">
									<Show
										when={props.subCollection}
										fallback={
											<span class={spanClassName}>
												{props.collection?.name ?? "Collection"}
											</span>
										}
									>
										<A
											href={`/${props.collectionsRoot.slug}/${props.collection?.categoryId}/`}
											class={linkClassName}
										>
											{props.collection?.name ?? "Collection"}
										</A>
									</Show>
								</li>
							</Show>

							<Show when={props.subCollection}>
								<li class="ml-1">
									<ChevronRight class="h-3 w-3" />
								</li>

								<li class="ml-1 inline-flex items-center">
									<span class={spanClassName}>
										{props.subCollection?.name ?? "Sub Collection"}
									</span>
								</li>
							</Show>
						</ul>
					</nav>
				</div>

				<div class="my-auto ml-auto md:w-1/3">
					<div class="flex items-center rounded-md bg-gray-200 p-2">
						<Search class="text-gray-500" size={16} />
						<input
							ref={props.searchFieldRef}
							type="search"
							value={props.keyword ?? ""}
							class="ml-2 w-full bg-transparent text-sm font-light outline-hidden"
							placeholder="Search collections"
						/>
					</div>
				</div>
			</div>
		</header>
	);
}
