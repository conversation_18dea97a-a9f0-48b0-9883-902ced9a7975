import { Show } from "solid-js";
import CollectionGalleryPriceListTable from "~/components/collections-gallery/CollectionGalleryPriceListTable";
import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { fetchCollectionProducts } from "~/services/http-services/page-service";
import LoadingSpinner from "~/components/LoadingSpinner";
import { ChevronRight } from "lucide-solid";
import CollectionGalleryHeaderNav from "~/components/collections-gallery/CollectionGalleryHeaderNav";
import { recursivelyFindMatchingCategory } from "~/utils/category-util";
import { dbFindCollectionCategory } from "~/services/db-services/collection-db-service";

const getPageData = query(
	async (
		collectionId: string,
		subCollectionId: string,
		lastChildCollectionId: string,
	) => {
		"use server";

		const isLoggedIn = await isAuthenticated();

		if (!isLoggedIn) {
			throw redirect("/login/");
		}

		let errorMessage: string | undefined = undefined;

		const collection = await dbFindCollectionCategory(collectionId);

		const subCollection = collection
			? recursivelyFindMatchingCategory(subCollectionId, collection)
			: undefined;

		const lastChildCollection = collection
			? recursivelyFindMatchingCategory(lastChildCollectionId, collection)
			: undefined;

		const products = lastChildCollection
			? await fetchCollectionProducts({
					existingProducts: [],
					perPage: 100,
					collectionId: lastChildCollectionId,
				})
			: [];

		return {
			errorMessage,
			collection,
			subCollection,
			lastChildCollection,
			products,
		};
	},
	"collectionGalleryLastChildPageData",
);

export const route = {
	preload: ({ params }) =>
		getPageData(params.id, params.sub_id, params.last_child_id),
} satisfies RouteDefinition;

export default function CollectionGalleryLastChildPage(
	props: RouteSectionProps,
) {
	const pageData = createAsync(
		() =>
			getPageData(
				props.params.id,
				props.params.sub_id,
				props.params.last_child_id,
			),
		{
			deferStream: true,
		},
	);

	return (
		<>
			<Show when={!pageData()}>
				<div class="relative flex h-full w-full items-center justify-center">
					<LoadingSpinner class="mt-14" />
				</div>
			</Show>

			<Show when={pageData()}>
				<Show
					when={!pageData()?.errorMessage}
					fallback={pageData()?.errorMessage}
				>
					<div class="relative min-h-screen w-full bg-cover bg-fixed">
						<div
							class="fixed h-screen w-screen bg-cover print:hidden"
							style="background-image: url('/images/CollectionsBG.png');"
						></div>

						<div class="relative mx-auto w-[1280px] px-4 py-12 2xl:w-auto print:w-auto print:min-w-auto print:px-0 print:py-0 print:pt-10">
							<div class="font-display mb-14 text-center text-[#656565]">
								<h2 class="font-base text-center text-5xl">
									<Show when={pageData()?.subCollection}>
										{pageData()?.subCollection?.name}

										<Show when={pageData()?.collection}>
											{` ${pageData()?.collection?.name}`}
										</Show>

										<span class="inline-block px-1 text-stone-600">
											<ChevronRight class="" />
										</span>
									</Show>

									<span>{pageData()?.lastChildCollection?.name}</span>
								</h2>

								<h5 class="mt-4 text-2xl">FOB $ Semarang Pricelist</h5>

								<CollectionGalleryHeaderNav
									collectionsRoot={{
										text: "Collections Gallery",
										slug: "collections-gallery",
									}}
									collection={pageData()?.collection ?? undefined}
									subCollection={pageData()?.subCollection}
									lastChildCollection={pageData()?.lastChildCollection}
								/>
							</div>

							<CollectionGalleryPriceListTable
								category={pageData()?.lastChildCollection!}
								products={pageData()?.products ?? []}
							/>
						</div>

						<img
							src="/images/bungaKiri.png"
							alt=""
							class="absolute top-0 right-0 z-[-1] h-4/5 opacity-30 lg:hidden"
						/>

						<img
							src="/images/bungaKanan.png"
							alt=""
							class="absolute top-0 left-0 z-[-1] h-4/5 opacity-30 lg:hidden"
						/>
					</div>
				</Show>
			</Show>
		</>
	);
}
