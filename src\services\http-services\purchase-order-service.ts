import ky from "ky";
import { inflowConfig, inflowHttpHeader } from "~/configs/server-config";
import { PurchaseOrderData } from "~/types/dto";
import {
	PurchaseOrderListResponse,
	PurchaseOrderResponse,
} from "~/types/response";
import { getErrorMessage } from "~/utils/http-util";

export interface FetchPurchaseOrdersProps {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	keyword?: string;
	filters?: Record<string, any>;
}

export async function fetchPurchaseOrders(
	props?: FetchPurchaseOrdersProps,
): Promise<PurchaseOrderListResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? inflowConfig().loadMorePerPage;
	const include =
		props?.include ??
		"currency,lines,lines.product,lines.product.images,vendor";

	let apiUrl = `${baseApiUrl}/purchase-orders?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.keyword) {
		apiUrl += `&filter[smart]=${props.keyword}`;
	}

	if (props?.filters) {
		for (const [key, value] of Object.entries(props.filters)) {
			const val = Array.isArray(value) ? JSON.stringify(value) : String(value);
			apiUrl += `&filter[${key}]=${val}`;
		}
	}

	try {
		const response = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json<PurchaseOrderData[]>();

		// console.log(
		//   "response on fetchPurchaseOrders method in sales-order-service.ts: ",
		//   response,
		// );

		return {
			success: true,
			message: "Purchase orders fetched successfully",
			data: response,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findPurchaseOrder(
	id: string,
): Promise<PurchaseOrderResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;
	const currentTimestamp = new Date().getTime();

	const include = "currency,lines,lines.product,lines.product.images,vendor";
	const apiUrl = `${baseApiUrl}/purchase-orders/${id}?include=${include}&nocache=${currentTimestamp}`;

	try {
		const purchaseOrder = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json<PurchaseOrderData | undefined>();

		return {
			success: true,
			message: "Purchase order fetched successfully",
			data: purchaseOrder,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
