import ky from "ky";
import { ProductListResponse, ProductResponse } from "~/types/response";

export interface ClientFetchProductsProps {
	url: string;
	before?: string;
	after?: string;
	shortDesc?: boolean;
	keyword?: string;
}

export async function clientFetchProducts(
	props: ClientFetchProductsProps,
): Promise<ProductListResponse> {
	let apiUrl = `${props.url}?includeCount=true`;

	if (props.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props.keyword) {
		apiUrl += `&keyword=${props.keyword}`;
	}

	if (props.shortDesc !== undefined) {
		apiUrl += `&shortDesc=${props.shortDesc}`;
	}

	try {
		const response = await ky.get(apiUrl).json<ProductListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more products",
		};
	}
}

export interface ClientFindProductProps {
	url: string;
	id: string;
}

export async function clientFindProduct(
	props: ClientFindProductProps,
): Promise<ProductResponse> {
	let apiUrl = `${props.url}/${props.id}`;

	try {
		const response = await ky.get(apiUrl).json<ProductResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more products",
		};
	}
}
