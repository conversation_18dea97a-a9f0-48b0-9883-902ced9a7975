import { Show, createSignal } from "solid-js";
import { isServer } from "solid-js/web";
import { FileSpreadsheet, Filter, Mail, Printer } from "lucide-solid";
import PriceListEmailForm from "./PriceListEmailForm";

interface PriceListActionButtonsProps {
	title: string;
	url: string;
	onFilterIconClick?: () => void;
	onXlsxExport?: (
		formRef?: HTMLFormElement,
		dataFieldRef?: HTMLInputElement,
	) => void;
}

export default function PriceListActionButtons(
	props: PriceListActionButtonsProps,
) {
	const [openEmailForm, setOpenEmailForm] = createSignal(false);

	const buttonClass =
		"text-white group-hover:text-yellow-400 transition-colors duration-200 ease-in-out hover:scale-110 mx-auto";

	const handlePrint = () => {
		if (isServer) return;
		window.print();
	};

	function handleEmail() {
		setOpenEmailForm(true);
	}

	let formRef: HTMLFormElement | undefined;
	let dataFieldRef: HTMLInputElement | undefined;

	return (
		<form ref={formRef} method="post" target="_blank">
			<input type="hidden" name="data" ref={dataFieldRef} />
			<div class="fixed right-5 bottom-5 z-10 flex flex-col space-y-2 rounded-full bg-orange-800/75 px-3 py-4 print:hidden">
				<Show when={props.onFilterIconClick}>
					<button
						type="button"
						class="group"
						onClick={props.onFilterIconClick}
						title="Filter"
					>
						<Filter class={buttonClass} size={20} />
					</button>
				</Show>
				<button
					type="button"
					class={`group ${props.onFilterIconClick ? "border-t border-white/50" : ""} pt-1.5`}
					onClick={handlePrint}
					title="Print"
				>
					<Printer class={buttonClass} size={20} />
				</button>
				<button
					type="button"
					class="group border-t border-white/50 pt-1.5"
					title="Send Email"
					onClick={handleEmail}
				>
					<Mail class={buttonClass} size={20} />
				</button>

				<Show when={props.onXlsxExport}>
					<button
						type="button"
						class="group border-t border-white/50 pt-1.5"
						title="Export to Excel"
						onClick={() => props.onXlsxExport?.(formRef, dataFieldRef)}
					>
						<FileSpreadsheet class={buttonClass} />
					</button>
				</Show>
			</div>
			<Show when={openEmailForm()}>
				<div class="fixed inset-0 z-20 flex items-center justify-center bg-black/50">
					<div class="m-auto rounded-sm bg-gray-100">
						<div>
							<PriceListEmailForm
								title={props.title}
								url={props.url}
								onCancel={() => setOpenEmailForm(false)}
								onSend={() => setOpenEmailForm(false)}
							/>
						</div>
					</div>
				</div>
			</Show>
		</form>
	);
}
