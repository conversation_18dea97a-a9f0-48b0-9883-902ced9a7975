import { Show, batch, createSignal } from "solid-js";
import { JobOrderProductImagesData, ProfileData } from "~/types/dto";
import {
	clientCreateJobOrderProductImage,
	clientUpdateJobOrderProductImage,
} from "~/services/client-services/job-order-client-service";
import { allowedEmailsToManageJobOrder } from "~/configs/app-config";
import { Loader2, Save, SquarePen } from "lucide-solid";

export default function ImagesAction(props: {
	currentUser?: ProfileData;
	apiUrl: string;
	salesOrderId: string;
	productId: string;
	Images?: JobOrderProductImagesData;
	afterDataSaved?: (data: JobOrderProductImagesData) => void;
}) {
	const [imagesData, setImagesData] = createSignal<JobOrderProductImagesData>(
		props.Images ?? {
			id: 0,
			images: undefined,
			salesOrderId: props.salesOrderId,
			productId: props.productId,
		},
	);

	const [editMode, setEditMode] = createSignal(false);
	const [isSaving, setIsSaving] = createSignal<boolean>(false);

	// let textareaRef: HTMLTextAreaElement; kan untuk menambahkan type HTMLTextAreaElement nah sekarang yang saya butuhkan adalah untuk file image
	let imageRef: HTMLInputElement | undefined;

	async function handleDataSave() {
		setIsSaving(true);

		const newimagesData: JobOrderProductImagesData = {
			id: imagesData().id,
			salesOrderId: props.salesOrderId,
			productId: props.productId,
			images: imageRef?.src ?? "",
		};

		const response = newimagesData.id
			? await clientUpdateJobOrderProductImage({
					url: props.apiUrl,
					data: newimagesData,
				})
			: await clientCreateJobOrderProductImage({
					url: props.apiUrl,
					data: newimagesData,
				});

		batch(() => {
			setEditMode(false);

			if (response.success) {
				if (response.data) setImagesData(response.data);
			} else {
				alert(response.message);
			}
		});

		if (response.data) props.afterDataSaved?.(response.data);
	}

	function openEditMode() {
		setEditMode(true);
	}

	function closeEditMode() {
		setEditMode(false);
	}

	function canEditImages() {
		return (
			props.currentUser &&
			props.currentUser?.email &&
			allowedEmailsToManageJobOrder.includes(props.currentUser?.email)
		);
	}

	return (
		<Show
			when={editMode()}
			fallback={
				<div class="relative h-full w-full">
					{/* show image dari file yang di upload */}
					<img
						src={imagesData().images}
						class="h-full w-full object-cover"
						alt=""
					/>

					<Show when={canEditImages()}>
						<button
							class="hover:bg-primary absolute top-0 right-0 rounded-full bg-gray-200 p-2 hover:text-white print:hidden"
							onClick={openEditMode}
						>
							<SquarePen size={13} />
						</button>
					</Show>
				</div>
			}
		>
			<div class="relative h-full w-full">
				<input
					ref={imageRef}
					type="text"
					class="relative block w-full resize-none rounded-sm border-2 border-gray-200 p-2 print:hidden"
					value={props.Images?.images ?? ""}
					// accept="image/*"
				/>

				<div class="mt-2 flex flex-wrap items-center justify-between">
					<button
						type="button"
						class="relative inline-flex w-[49%] items-center justify-center rounded-sm bg-gray-200 p-2 text-gray-600 print:hidden"
						onClick={closeEditMode}
					>
						Cancel
					</button>

					<Show
						when={isSaving()}
						fallback={
							<button
								type="button"
								class="bg-primary relative inline-flex w-[49%] items-center justify-center rounded-sm p-2 text-white print:hidden"
								onClick={handleDataSave}
							>
								<Save class="mr-1.5" size={16} />
								Save
							</button>
						}
					>
						<div class="bg-primary relative flex w-[49%] items-center justify-center rounded-sm p-2 text-white print:hidden">
							<Loader2 class="h-4 w-4 animate-spin" />
						</div>
					</Show>
				</div>
			</div>
		</Show>
	);
}
