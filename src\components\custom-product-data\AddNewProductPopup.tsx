import { Show, createSignal } from "solid-js";
import { clientCreateCustomProductData } from "~/services/client-services/custom-product-data-client-service";
import { CustomProductData, CustomerData, ProductData } from "~/types/dto";
import LoadingSpinner from "../LoadingSpinner";
import { X } from "lucide-solid";

export interface AddNewProductPopupProps {
	apiUrl: string;
	customer?: CustomerData;
	product?: ProductData;
	onSelectProductClick?: () => void;
	onCloseButtonClick?: () => void;
	onCancelButtonClick?: () => void;
	afterDataSaved?: (product: CustomProductData) => void;
}

export default function AddNewProductPopup(props: AddNewProductPopupProps) {
	const [isSaving, setIsSaving] = createSignal<boolean>(false);
	const [selectedCustomerProductCode, setSelectedCustomerProductCode] =
		createSignal<string>("");
	const [selectedCustomerProductName, setSelectedCustomerProductName] =
		createSignal<string>("");

	function handleCustomerProductCodeChange(value: string) {
		setSelectedCustomerProductCode(value);
	}

	function handleCustomerProductNameChange(value: string) {
		setSelectedCustomerProductName(value);
	}

	async function handleDataSave() {
		if (!props.product) return;

		setIsSaving(true);

		const newCustomProductData: CustomProductData = {
			id: 0,
			customerId: props.customer?.customerId ?? "",
			customerName: props.customer?.name ?? "",
			productId: props.product?.productId ?? "",
			productName: props.product?.name ?? "",
			productSku: props.product?.sku ?? "",
			customerProductCode: selectedCustomerProductCode(),
			customerProductName: selectedCustomerProductName(),
		};

		const response = await clientCreateCustomProductData({
			url: props.apiUrl,
			data: newCustomProductData,
		});

		setIsSaving(false);

		if (!response.success) {
			alert(response.message);
			return;
		}

		if (response.data) props.afterDataSaved?.(response.data);
	}

	return (
		<section class="fixed top-0 left-0 z-10 flex h-full w-full items-center justify-center bg-black/50">
			<div class="relative rounded-lg bg-white sm:w-1/2 lg:w-1/3">
				<h3 class="relative rounded-tl-lg rounded-tr-lg bg-gray-100 px-4 py-4 text-lg font-medium text-gray-900">
					New custom data
					<button
						class="absolute right-4 cursor-pointer rounded-full bg-black px-2 py-2 text-sm text-white"
						onClick={props.onCloseButtonClick}
					>
						<X />
					</button>
				</h3>
				<div class="px-4 py-4">
					<div class="mb-4">
						<label
							for="product_name"
							class="block text-sm font-medium text-gray-700"
						>
							Product
						</label>
						<input
							type="text"
							name="product_name"
							id="product_name"
							class="mt-1 block w-full cursor-pointer rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
							placeholder="Select a product"
							onClick={props.onSelectProductClick}
							value={props.product?.name ?? ""}
							readOnly
						/>
					</div>
					<div class="mb-4">
						<label
							for="product_sku"
							class="block text-sm font-medium text-gray-700"
						>
							SKU
						</label>
						<input
							type="text"
							name="product_sku"
							id="product_sku"
							class="mt-1 block w-full cursor-pointer rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
							placeholder="product SKU"
							value={props.product?.sku ?? ""}
							readOnly
						/>
					</div>

					<div class="mb-4">
						<label
							for="custom-product-code"
							class="block text-sm font-medium text-gray-700"
						>
							Customer product code
						</label>
						<input
							type="text"
							name="custom-product-code"
							id="custom-product-code"
							class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
							placeholder="Insert customer product code"
							onChange={(e) => {
								handleCustomerProductCodeChange(e.currentTarget.value);
							}}
						/>
					</div>
					<div class="mb-4">
						<label
							for="custom-product-name"
							class="block text-sm font-medium text-gray-700"
						>
							Customer product name
						</label>
						<input
							type="text"
							name="custom-product-name"
							id="custom-product-name"
							class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
							placeholder="Insert customer product name"
							onChange={(e) => {
								handleCustomerProductNameChange(e.currentTarget.value);
							}}
						/>
					</div>
					<div class="flex justify-end">
						<button
							class="mr-2 inline-flex items-center rounded-md border px-3 py-2 text-sm font-medium text-slate-700/50 active:scale-95 sm:py-2"
							onClick={props.onCancelButtonClick}
							disabled={isSaving()}
						>
							<span class="">Cancel</span>
						</button>
						<button
							class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm text-white active:scale-95 sm:py-2"
							onClick={handleDataSave}
							disabled={isSaving()}
						>
							<Show when={isSaving()} fallback={<span class="">Save</span>}>
								<LoadingSpinner colorClass="text-white" iconClass="h-4 w-4" />
							</Show>
						</button>
					</div>
				</div>
			</div>
		</section>
	);
}
