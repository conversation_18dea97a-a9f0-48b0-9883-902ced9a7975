import { APIEvent } from "@solidjs/start/server";
import { URL } from "url";
import { allowedEmailsToManageJobOrder } from "~/configs/app-config";
import { getAuthData, isAuthenticated } from "~/services/http-services/auth-service";
import {
	createJobOrderCustomColumn,
	fetchJobOrderCustomColumns,
} from "~/services/http-services/job-order-service";
import { makeJsonResponse } from "~/utils/http-util";
import { maybeParseJson } from "~/utils/json-utils";

export async function GET({ request, params }: APIEvent) {
	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to load job order custom columns",
			},
			401,
		);
	}

	const urlObject = new URL(request.url);
	const searchParams = urlObject.searchParams;

	const salesOrderId = searchParams.get("sales_order_id");

	if (!salesOrderId) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please provide sales order id",
			},
			400,
		);
	}

	const response = await fetchJobOrderCustomColumns({ salesOrderId });

	return makeJsonResponse(response);
}

export async function POST({ request, params }: APIEvent) {
	const authDataResponse = await getAuthData();

	if (!authDataResponse.success || !authDataResponse.data) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to create job order custom columns",
			},
			401,
		);
	}

	if (!allowedEmailsToManageJobOrder.includes(authDataResponse.data.email)) {
		return makeJsonResponse(
			{
				success: false,
				message: "You are not authorized to create job order custom columns",
			},
			403,
		);
	}

	// Get POST data.
	const body = await request.json();

	// This is the team member <NAME_EMAIL> member in inFlow.
	const teamMemberId = "9e31e1dd-a3ba-4f46-87f1-e155d14f17a5";
	const salesOrderId = body.salesOrderId;
	const name = body.name;
	const values = maybeParseJson(body.values);

	const response = await createJobOrderCustomColumn({
		teamMemberId: teamMemberId,
		salesOrderId: salesOrderId,
		name: name,
		values: values,
	});

	return makeJsonResponse(response);
}
