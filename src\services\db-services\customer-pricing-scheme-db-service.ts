import { CustomerPricingSchemeData } from "~/types/dto";
import {
	dbFetchPricingSchemes,
	dbFindPricingScheme,
	dbFindPricingSchemeBySlug,
} from "./pricing-scheme-db-service";
import {
	dbFetchCustomersByPricingSchemeIds,
	dbFindCustomerByPricingSchemeId,
} from "./customer-db-service";
import { toPricingSchemeSlug } from "~/utils/string-util";
import { getOption } from "./option-db-service";

const optionName = "customer_pricing_schemes";

export async function dbGetCustomerPricingScheme(
	pricingSchemeId: string,
): Promise<CustomerPricingSchemeData | null> {
	const customerPricingSchemes =
		await getOption<CustomerPricingSchemeData[]>(optionName);
	if (!customerPricingSchemes) return null;

	const customerPricingScheme = customerPricingSchemes.find(
		(customerPricingScheme) =>
			customerPricingScheme.pricingSchemeId === pricingSchemeId,
	);

	return customerPricingScheme ?? null;
}

export async function dbGetCustomerPricingSchemeBySlug(
	pricingSchemeSlug: string,
): Promise<CustomerPricingSchemeData | null> {
	const customerPricingSchemes =
		await getOption<CustomerPricingSchemeData[]>(optionName);
	if (!customerPricingSchemes) return null;

	const customerPricingScheme = customerPricingSchemes.find(
		(customerPricingScheme) =>
			customerPricingScheme.pricingSchemeSlug === pricingSchemeSlug,
	);

	return customerPricingScheme ?? null;
}

export async function dbGetCustomerPricingSchemes(): Promise<
	CustomerPricingSchemeData[]
> {
	const customerPricingSchemes =
		await getOption<CustomerPricingSchemeData[]>(optionName);
	if (!customerPricingSchemes) return [];

	// Sort the list based on "name" property of list item. A first, Z last.
	customerPricingSchemes.sort((a, b) => {
		if (a.customerName < b.customerName) {
			return -1;
		}

		if (a.customerName > b.customerName) {
			return 1;
		}

		return 0;
	});

	return customerPricingSchemes;
}

export async function dbFindCustomerPricingScheme(
	pricingSchemeId: string,
): Promise<CustomerPricingSchemeData | null> {
	"use server";

	const pricingScheme = await dbFindPricingScheme(pricingSchemeId);

	if (!pricingScheme) return null;

	const customer = await dbFindCustomerByPricingSchemeId(pricingSchemeId);

	if (!customer) return null;

	return {
		pricingSchemeId: pricingSchemeId,
		pricingSchemeSlug: toPricingSchemeSlug(pricingScheme.name),
		pricingSchemeName: pricingScheme.name,
		customerId: customer.customerId,
		customerName: customer.name,
	};
}

export async function dbFindCustomerPricingSchemeBySlug(
	pricingSchemeSlug: string,
): Promise<CustomerPricingSchemeData | null> {
	"use server";

	const pricingScheme = await dbFindPricingSchemeBySlug(pricingSchemeSlug);

	if (!pricingScheme) return null;

	const customer = await dbFindCustomerByPricingSchemeId(
		pricingScheme.pricingSchemeId,
	);

	if (!customer) return null;

	return {
		pricingSchemeId: pricingScheme.pricingSchemeId,
		pricingSchemeSlug: toPricingSchemeSlug(pricingScheme.name),
		pricingSchemeName: pricingScheme.name,
		customerId: customer.customerId,
		customerName: customer.name,
	};
}

export async function dbFetchCustomerPricingSchemes(): Promise<
	CustomerPricingSchemeData[]
> {
	"use server";

	const pricingSchemes = await dbFetchPricingSchemes();
	if (!pricingSchemes.length) return [];

	const pricingSchemeIds = pricingSchemes.map(
		(pricingScheme) => pricingScheme.pricingSchemeId,
	);

	// console.log(`total pricing schemes found: ${pricingSchemes.length}`);

	const customerPricingSchemes: CustomerPricingSchemeData[] = [];

	const customers = await dbFetchCustomersByPricingSchemeIds(pricingSchemeIds);

	// console.log(`total customers found: ${customers.length}`);

	if (!customers.length) return [];

	for (const pricingScheme of pricingSchemes) {
		const customer = customers.find(
			(customer) => customer.pricingSchemeId === pricingScheme.pricingSchemeId,
		);

		// if (!customer) continue;

		customerPricingSchemes.push({
			pricingSchemeId: pricingScheme.pricingSchemeId,
			pricingSchemeSlug: toPricingSchemeSlug(pricingScheme.name),
			pricingSchemeName: pricingScheme.name,
			customerId: customer?.customerId ?? "",
			customerName: customer?.name ?? "",
		});
	}

	return customerPricingSchemes;
}
