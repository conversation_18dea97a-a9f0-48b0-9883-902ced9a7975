import { A } from "@solidjs/router";

export default function BlockMenu(props: {}) {
	const menuLinkBlockClassName =
		"mb-6 flex h-32 transform items-center rounded-3xl bg-[#F3FBF6] p-6 transition-all duration-300 ease-in-out hover:bg-green-100 active:scale-90 sm:mb-0";

	const menuTextClassName = "mb-3 block text-xl font-bold text-green-600";

	return (
		<div class="relative mb-3 lg:px-32">
			<div class="sm:grid sm:grid-cols-3 sm:gap-6">
				<A href="/sales-orders/" class={`${menuLinkBlockClassName}`}>
					<span class="mx-auto block">
						<span class={`${menuTextClassName}`}>Sales Orders</span>
					</span>
				</A>

				<A href="/purchase-orders/" class={`${menuLinkBlockClassName}`}>
					<span class="mx-auto block">
						<span class={`${menuTextClassName}`}>Purchase Order</span>
					</span>
				</A>

				<A href="/vendors/" class={`${menuLinkBlockClassName}`}>
					<span class="mx-auto block">
						<span class={`${menuTextClassName}`}>Supplier Cards</span>
					</span>
				</A>
			</div>

			<div class="mt-6 sm:grid sm:grid-cols-3 sm:gap-6">
				<A href="/customers/" class={`${menuLinkBlockClassName}`}>
					<span class="mx-auto block">
						<span class={`${menuTextClassName}`}>Custom Codes</span>
					</span>
				</A>

				<A href="/collections/" class={`${menuLinkBlockClassName}`}>
					<span class="mx-auto block">
						<span class={`${menuTextClassName}`}>Collection Price List</span>
					</span>
				</A>

				<A href="/customer-price-list/" class={`${menuLinkBlockClassName}`}>
					<span class="mx-auto block">
						<span class={`${menuTextClassName}`}>Customer Price List</span>
					</span>
				</A>
			</div>
		</div>
	);
}
