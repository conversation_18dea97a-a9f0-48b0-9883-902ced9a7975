import * as v from "valibot";

export const EmailSchema = v.pipe(
	v.string(),
	v.trim(),
	v.nonEmpty("Email is required"),
	v.email("Invalid email address"),
	v.endsWith("multayindonesia.com", "Only internal email is allowed"),
);

export const PasswordSchema = v.pipe(
	v.string(),
	v.trim(),
	v.nonEmpty("Password is required"),
	v.minLength(8, "Password must be at least 8 characters long"),
);

export const LoginFormSchema = v.object({
	email: EmailSchema,
	password: PasswordSchema,
});

export const RegisterFormSchema = v.object({
	email: EmailSchema,
	password: PasswordSchema,
	role: v.pipe(v.string(), v.trim(), v.nonEmpty("Role is required")),
	firstName: v.pipe(v.string(), v.trim(), v.nonEmpty("First name is required")),
	lastName: v.pipe(v.string(), v.trim(), v.nonEmpty("Last name is required")),
	inflowUserId: v.pipe(v.string(), v.trim()),
	phone: v.pipe(v.string(), v.trim()),
	address: v.pipe(v.string(), v.trim()),
	avatarUrl: v.pipe(v.string(), v.trim()),
});
