import { BaseIssue } from "valibot";

export interface ValibotFieldIssuesProps {
	fieldName: string;
	issues: BaseIssue<any>[];
}

export function valibotIssuesHaveField(props: ValibotFieldIssuesProps) {
	return props.issues.some((issue: BaseIssue<any>) => {
		const pathItems = issue.path;

		if (!pathItems) {
			return false;
		}

		let found = false;

		for (const pathItem of pathItems) {
			if (pathItem.key === props.fieldName) {
				found = true;
				break;
			}
		}

		return found;
	});
}

export function valibotIssueMessage(props: ValibotFieldIssuesProps) {
	const issue = props.issues.find((issue: BaseIssue<any>) => {
		const pathItems = issue.path;

		if (!pathItems) {
			return false;
		}

		let found = false;

		for (const pathItem of pathItems) {
			if (pathItem.key === props.fieldName) {
				found = true;
				break;
			}
		}

		return found;
	});

	return issue ? issue.message : "";
}
