import { getOption, updateOption } from "./option-db-service";
import { fetchProducts } from "../http-services/product-service";
import { inflowConfig } from "~/configs/server-config";
import { db } from "~/db";
import { salesProductsTable } from "~/db/schema";
import { eq, inArray } from "drizzle-orm";
import { ProductData } from "~/types/dto";
import { getBeginningOfTodayUTC, getEndOfTodayUTC } from "~/utils/date-util";
import { SetupDataResult } from "~/types/response";
import { toProductData } from "~/utils/dto-util";

export async function dbFindSalesProduct(productId: string) {
	"use server";

	const results = await db
		.select()
		.from(salesProductsTable)
		.where(eq(salesProductsTable.productId, productId))
		.limit(1);

	if (!Array.isArray(results) || results.length === 0) {
		return null;
	}

	const product = toProductData(results[0]);

	return product ?? null;
}

export async function dbFetchSalesProducts(props?: { productIds?: string[] }) {
	"use server";

	const productList: ProductData[] = [];

	if (props) {
		if (props.productIds && props.productIds.length) {
			const results = await db
				.select()
				.from(salesProductsTable)
				.where(inArray(salesProductsTable.productId, props.productIds));

			for (const result of results) {
				const product = toProductData(result);
				if (product) productList.push(product);
			}

			return productList;
		}
	}

	const results = await db.select().from(salesProductsTable);

	for (const result of results) {
		const product = toProductData(result);
		if (product) productList.push(product);
	}

	return productList;
}

/**
 * Insert a sales product to the database.
 */
export async function dbInsertSalesProduct(product: ProductData) {
	"use server";

	const [header] = await db.insert(salesProductsTable).values(product);

	return header.affectedRows > 0 ? product.productId : false;
}

/**
 * Update a sales product in the database.
 */
export async function dbUpdateSalesProduct(
	productId: string,
	data: Partial<Omit<ProductData, "id" | "productId">>,
) {
	"use server";

	const [header] = await db
		.update(salesProductsTable)
		.set(data)
		.where(eq(salesProductsTable.productId, productId));

	return header.affectedRows > 0 ? productId : false;
}

/**
 * Insert sales products to the database.
 */
export async function dbInsertSalesProducts(products: ProductData[]) {
	"use server";

	const [header] = await db.insert(salesProductsTable).values(products);

	return header.affectedRows > 0 ? true : false;
}

/**
 * Update sales products in the database.
 */
export async function dbUpdateSalesProducts(
	list: {
		productId: string;
		data: Partial<Omit<ProductData, "id" | "productId">>;
	}[],
) {
	"use server";

	const results = await Promise.all(
		list.map(async (item) => {
			const [header] = await db
				.update(salesProductsTable)
				.set(item.data)
				.where(eq(salesProductsTable.productId, item.productId));

			return {
				productId: item.productId,
				status: header.affectedRows > 0,
			};
		}),
	);

	return results.some((result) => result.status);
}

export async function dbCollectSalesProducts(props?: {
	after?: string;
}): Promise<SetupDataResult> {
	"use server";

	const lastModified = await getOption<string>("sales_product_last_modified");

	let filters:
		| {
				lastModifiedDateTime: {
					fromDate: string;
					toDate: string;
				};
		  }
		| undefined = undefined;

	if (lastModified) {
		filters = {
			lastModifiedDateTime: {
				fromDate: lastModified,
				toDate: getEndOfTodayUTC().toISOString(),
			},
		};
	}

	const count = 100;

	const remoteResponse = await fetchProducts({
		count: count,
		after: props?.after,
		include: "images,prices,prices.pricingScheme,prices.pricingScheme.currency",
		categoryId: inflowConfig().salesProductCategoryId,
		filters: filters,
	});

	if (!remoteResponse.success || !remoteResponse.data) {
		return {
			success: false,
			message: remoteResponse.message,
			meta: {
				args: {
					after: props?.after,
				},
				errorCode: "remote_fetch_error",
			},
		};
	}

	if (remoteResponse.data.length > 0) {
		const productIds = remoteResponse.data.map((product) => product.productId);

		const existingRecords = await dbFetchSalesProducts({
			productIds: productIds,
		});
		const existingRecordIds = existingRecords.map(
			(existingRecord) => existingRecord.productId,
		);

		const newInsertList = remoteResponse.data.filter(
			(product) => !existingRecordIds.includes(product.productId),
		);
		const newUpdateList = remoteResponse.data.filter((product) =>
			existingRecordIds.includes(product.productId),
		);

		let anySuccess = false;

		if (newInsertList.length) {
			const [header] = await db
				.insert(salesProductsTable)
				.values(newInsertList);

			if (header.affectedRows) anySuccess = true;
		}

		if (newUpdateList.length) {
			const updateResult = await dbUpdateSalesProducts(
				newUpdateList.map((product) => {
					// The `productProps` here should be `ProductData` but without `id` and `productId` properties.
					const productProps: Record<string, any> = { ...product };

					// Now remove `id` (if exists) and `productId` from `productProps` object.
					if ("id" in productProps) delete productProps.id;
					delete productProps.productId;

					return {
						productId: product.productId,
						data: productProps,
					};
				}),
			);

			if (updateResult) anySuccess = true;
		}

		if (!anySuccess) {
			return {
				success: false,
				message: "Failed to insert or update sales products",
				meta: {
					args: {
						after: props?.after,
					},
					hasMore: remoteResponse.data.length < count ? false : true,
					errorCode: "db_insert_error",
					lastId: remoteResponse.data[remoteResponse.data.length - 1].productId,
				},
			};
		}
	}

	const isFinished = remoteResponse.data.length < count;

	if (isFinished) {
		updateOption({
			name: "sales_product_last_modified",
			value: getBeginningOfTodayUTC().toISOString(),
		});
	}

	return {
		success: true,
		message: isFinished
			? "Finished collecting sales products"
			: "Sales products collected successfully",
		meta: {
			args: {
				after: props?.after,
			},
			hasMore: isFinished ? false : true,
			lastId: remoteResponse.data.length
				? remoteResponse.data[remoteResponse.data.length - 1].productId
				: undefined,
		},
	};
}
