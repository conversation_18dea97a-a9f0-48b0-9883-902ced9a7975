import { Barcode, DollarSign, History, X } from "lucide-solid";
import ViewFormButton from "../sales-orders/ViewFormButton";

export interface CustomerViewButtonProps {
	customerId?: string;
	salesOrderId?: string;
	onCloseModal?: () => void;
}

export default function CustomerViewButton(props: CustomerViewButtonProps) {
	function closeModal() {
		if (props.onCloseModal) {
			props.onCloseModal();
		}
	}

	return (
		<div class="bg-primary/50 fixed inset-0 z-50 h-full w-full">
			<div class="relative container flex h-full items-center justify-center">
				<div class="rounded-lg bg-white px-5 py-4 text-center">
					<h3 class="relative mb-4 font-bold">
						View forms
						<button
							class="absolute -top-6 -right-6 cursor-pointer rounded-full bg-black px-1 py-1 text-white"
							onClick={closeModal}
						>
							<X />
						</button>
					</h3>
					<div class="grid items-center justify-between gap-3 md:flex">
						{/* <ViewFormButton
							url={`/customers/${props.salesOrderId}/price-list`}
							labelText="Customer Price List"
							icon={DollarSign}
						/> */}
						<ViewFormButton
							url={`/customers/${props.customerId}/product-code`}
							labelText="Custom Product Code"
							icon={Barcode}
						/>
					</div>
				</div>
			</div>
		</div>
	);
}
