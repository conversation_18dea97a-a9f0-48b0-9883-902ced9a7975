export function maybeParseJson(
	value: string | Record<string, string> | undefined | null,
): Record<string, string> {
	let parsed: Record<string, string> = {};

	if (!value) {
		return parsed;
	}

	if (typeof value === "string") {
		try {
			parsed = JSON.parse(value);
		} catch (e) {
			console.error(
				"Error when parsing returned job order's custom column from Supabase:",
				e,
			);
		}
	} else {
		parsed = value;
	}

	return parsed;
}

export function safeJsonParse<T>(value: unknown): T | undefined {
	if (typeof value !== "string") {
		console.error("Value is not a string, cannot parse as JSON:", value);
		return undefined;
	}

	try {
		return JSON.parse(value);
	} catch (e) {
		console.error("Error when parsing JSON:", e);
		return undefined;
	}
}

export function safeJsonStringify(value: unknown): string | undefined {
	try {
		return JSON.stringify(value);
	} catch (e) {
		console.error("Error when stringifying JSON:", e);
		return undefined;
	}
}
