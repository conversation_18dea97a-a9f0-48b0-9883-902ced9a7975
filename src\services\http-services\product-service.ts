import ky from "ky";
import { inflowConfig, inflowHttpHeader } from "../../configs/server-config";
import { ProductData } from "~/types/dto";
import { ProductListResponse, ProductResponse } from "~/types/response";
import { getErrorMessage } from "~/utils/http-util";
import { toProductData } from "~/utils/dto-util";
import { safeJsonStringify } from "~/utils/json-utils";

export async function fetchProducts(props?: {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	isActive?: boolean;
	categoryId?: string;
	keyword?: string;
	filters?: Record<string, any>;
}): Promise<ProductListResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? inflowConfig().loadMorePerPage;
	const include =
		props?.include ??
		"images,category,prices,prices.pricingScheme,prices.pricingScheme.currency,inventoryLines";

	let apiUrl = `${baseApiUrl}/products?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc !== undefined) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.isActive) {
		apiUrl += `&filter[isActive]=${props.isActive ? "true" : "false"}`;
	}

	if (props?.categoryId) {
		apiUrl += `&filter[categoryId]=${props.categoryId}`;
	}

	if (props?.keyword) {
		apiUrl += `&filter[smart]=${props.keyword}`;
	}

	if (props?.filters) {
		for (const [key, value] of Object.entries(props.filters)) {
			if (value === undefined || value === null) {
				continue;
			}

			const val =
				Array.isArray(value) || typeof value === "object"
					? safeJsonStringify(value)
					: String(value);

			apiUrl += `&filter[${key}]=${val}`;
		}
	}

	try {
		const rawProducts = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json();

		const products: ProductData[] = [];

		if (Array.isArray(rawProducts)) {
			for (const product of rawProducts) {
				const productData = toProductData(product);
				if (productData) products.push(productData);
			}
		}

		return {
			success: true,
			message: "Products fetched successfully",
			data: products,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findProduct(id: string): Promise<ProductResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;
	const include = "images,category,prices,inventoryLines";

	try {
		const product = await ky
			.get(`${baseApiUrl}/products/${id}?include=${include}`, {
				headers: inflowHttpHeader(),
			})
			.json();

		return {
			success: true,
			message: "Product fetched successfully",
			data: toProductData(product),
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
