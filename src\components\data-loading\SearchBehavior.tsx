import { JSXElement, createSignal, onCleanup, onMount } from "solid-js";
import { isServer } from "solid-js/web";

// Number (in milliseconds) that we assume that the user is waiting for search result.
const acceptedWaitingDuration = 750;

// The waitingCounter's interval duration (in milliseconds).
const intervalDuration = 25;

// List of e.key values that will affect the search.
const keyupKeys = [
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"0",
	"1",
	"2",
	"3",
	"4",
	"5",
	"6",
	"7",
	"8",
	"9",
	" ",
	"spacebar",
	"delete",
	"backspace",
	"-",
	"_",
	"@",
	".",
	"/",
];

export interface SearchBehaviorProps {
	searchField?: HTMLInputElement;
	onSearch: (keyword: string) => void;
	children?: JSXElement;
}

export default function SearchBehavior(props: SearchBehaviorProps) {
	// The interval id of waitingCounter.
	let intervalId = 0;

	async function search() {
		if (!props.searchField || isServer) return;

		const keyword = props.searchField.value;
		setSearchValue(keyword);
		props.onSearch(keyword);
	}

	// Number (in milliseconds) of the time after user stops typing.
	const [waitingCounter, setWaitingCounter] = createSignal(0);
	const [searchFieldValue, setSearchValue] = createSignal("");

	function prepareSearch() {
		if (!props.searchField || isServer) return;

		setWaitingCounter(waitingCounter() + intervalDuration);

		if (waitingCounter() >= acceptedWaitingDuration) {
			resetWaitingCounter();

			if (props.searchField.value === searchFieldValue()) {
				return;
			}

			search();
		}
	}

	function resetWaitingCounter() {
		if (isServer) return;
		window.clearInterval(intervalId);
		setWaitingCounter(0);
	}

	function executeInputKeyup() {
		if (isServer) return;
		resetWaitingCounter();
		intervalId = window.setInterval(prepareSearch, intervalDuration);
	}

	function handleInputKeyup(e: KeyboardEvent) {
		if (isServer) return;
		if (e && e.key && -1 === keyupKeys.indexOf(e.key.toLowerCase())) return;
		executeInputKeyup();
	}

	function handleClearIconClick(ev: MouseEvent) {
		if (!props.searchField || isServer) return;
		if (!props.searchField.value) executeInputKeyup();
	}

	function handleOnEscape(e: KeyboardEvent) {
		if (isServer) return;
		if (e.key !== "Escape" && e.key !== "Esc") return;
	}

	onMount(() => {
		if (!props.searchField || isServer) return;

		props.searchField.addEventListener("keyup", handleInputKeyup);
		// document.addEventListener("click", handleDocumentClick);
		document.addEventListener("keydown", handleOnEscape);
	});

	onCleanup(() => {
		if (!props.searchField || isServer) return;

		props.searchField.removeEventListener("keyup", handleInputKeyup);
		// document.removeEventListener("click", handleDocumentClick);
		document.removeEventListener("keydown", handleOnEscape);
	});

	return <>{props.children}</>;
}
