import { APIEvent } from "@solidjs/start/server";
import { allowedEmailsToManageJobOrder } from "~/configs/app-config";
import { getAuthData, isAuthenticated } from "~/services/http-services/auth-service";
import {
	deleteJobOrderProductRemark,
	findJobOrderProductRemarkById,
	updateJobOrderProductRemark,
} from "~/services/http-services/job-order-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function DELETE({ request, params }: APIEvent) {
	const authDataResponse = await getAuthData();

	if (!authDataResponse || !authDataResponse.data) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to delete job order product remarks",
			},
			401,
		);
	}

	if (!allowedEmailsToManageJobOrder.includes(authDataResponse.data.email)) {
		return makeJsonResponse(
			{
				success: false,
				message: "You are not authorized to delete job order product remarks",
			},
			401,
		);
	}

	const id = params.id;

	const response = await deleteJobOrderProductRemark(Number(id));

	return makeJsonResponse(response);
}

export async function GET({ request, params }: APIEvent) {
	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to load job order product remarks",
			},
			401,
		);
	}

	const id = params.id;

	const response = await findJobOrderProductRemarkById(Number(id));

	return makeJsonResponse(response);
}

export async function PUT({ request, params }: APIEvent) {
	const authDataResponse = await getAuthData();

	if (!authDataResponse || !authDataResponse.data) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to update job order product remarks",
			},
			401,
		);
	}

	if (!allowedEmailsToManageJobOrder.includes(authDataResponse.data.email)) {
		return makeJsonResponse(
			{
				success: false,
				message: "You are not authorized to update job order product remarks",
			},
			401,
		);
	}

	// Get PUT data.
	const body = await request.json();

	// This is the team member <NAME_EMAIL> member in inFlow.
	const teamMeberId = "9e31e1dd-a3ba-4f46-87f1-e155d14f17a5";

	const id = params.id;
	const salesOrderId = body.salesOrderId;
	const salesOrderLineId = body.salesOrderLineId;
	const productId = body.productId;
	const remakrs = body.remarks;

	const response = await updateJobOrderProductRemark({
		id: Number(id),
		teamMemberId: teamMeberId,
		salesOrderId: salesOrderId,
		salesOrderLineId: salesOrderLineId,
		productId: productId,
		remarks: remakrs,
	});

	return makeJsonResponse(response);
}
