import { For, Show, batch, createSignal } from "solid-js";
import LoadingSpinner from "~/components/LoadingSpinner";
import SearchBehavior from "../data-loading/SearchBehavior";
import { useSearchParams } from "@solidjs/router";
import UserDataRow from "./UserDataRow";
import { ProfileData } from "~/types/dto";
import { UserListResponse } from "~/types/response";
import { clientFetchUsers } from "~/services/client-services/user-client-service";

export interface UserListProps {
	baseApiUrl: string;
	users: ProfileData[];
	searchField?: HTMLInputElement;
}

export default function UserList(props: UserListProps) {
	const [searchParams, setSearchParams] = useSearchParams();
	const [users, setUsers] = createSignal(props.users);
	const [fetchingData, setFetchingData] = createSignal<boolean>(false);

	async function fetchDataList(keyword?: string): Promise<UserListResponse> {
		return await clientFetchUsers({
			url: `${props.baseApiUrl}/users`,
			keyword: keyword,
		});
	}

	async function handleSearch(keyword: string) {
		batch(() => {
			setFetchingData(true);
			setSearchParams({ keyword: keyword });
			setUsers([]);
		});

		const response = await fetchDataList(keyword);

		handleFetchComplete(response);
	}

	function handleFetchComplete(response: UserListResponse): void {
		batch(() => {
			setFetchingData(false);

			if (!response.success) {
				alert(response.message);
				return;
			}

			if (!response?.data?.length) {
				setUsers([]);
				return;
			}

			setUsers(users().concat(response.data));
		});
	}

	let sectionRef: HTMLElement | undefined;

	return (
		<>
			<section class="text pt-32 text-sm" ref={sectionRef}>
				<SearchBehavior
					searchField={props.searchField}
					onSearch={handleSearch}
				/>

				<Show
					when={fetchingData()}
					fallback={
						<For each={users()}>{(user) => <UserDataRow user={user} />}</For>
					}
				>
					<LoadingSpinner class="mt-14" />
				</Show>
			</section>
		</>
	);
}
