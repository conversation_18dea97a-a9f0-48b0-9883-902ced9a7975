import { For, Show, batch, createSignal, onCleanup, onMount } from "solid-js";
import { SalesOrderData } from "~/types/dto";
import SalesOrderDataRow from "./SalesOrderDataRow";
import ViewFormButtonList from "./ViewFormButtonList";
import { isServer } from "solid-js/web";
import { X } from "lucide-solid";

export interface SalesOrderListProps {
	salesOrders: SalesOrderData[];
	onViewFormsButtonClick?: (salesOrder: SalesOrderData) => void;
}

export default function SalesOrderList(props: SalesOrderListProps) {
	const [salesOrders, setSalesOrders] = createSignal(props.salesOrders);
	const [isModalOpen, setIsModalOpen] = createSignal(false);

	if (!isServer) {
		onMount(() => {
			document.addEventListener("keydown", closeModalByEscKey);
		});

		onCleanup(() => {
			document.removeEventListener("keydown", closeModalByEscKey);
		});
	}

	const [selectedSalesOrder, setSelectedSalesOrder] = createSignal<
		SalesOrderData | undefined
	>();

	function openModal(salesOrder: SalesOrderData) {
		batch(() => {
			setSelectedSalesOrder(salesOrder);
			setIsModalOpen(true);
		});
	}

	function closeModal() {
		batch(() => {
			setSelectedSalesOrder(undefined);
			setIsModalOpen(false);
		});
	}

	function closeModalByEscKey(e: KeyboardEvent) {
		if (e.key !== "Escape" && e.key !== "Esc") return;
		closeModal();
	}

	return (
		<>
			<section class="text pt-28 text-sm md:pt-32">
				<div class="">
					<For each={salesOrders()}>
						{(order) => (
							<SalesOrderDataRow
								salesOrder={order}
								onViewFormsButtonClick={openModal}
							/>
						)}
					</For>
				</div>
			</section>

			<Show when={isModalOpen() && selectedSalesOrder()}>
				<div class="bg-primary/50 fixed inset-0 z-50 h-full w-full">
					<div class="relative container flex h-full items-center justify-center">
						<div class="max-w-xl rounded-lg bg-white px-5 py-4 text-center">
							<h3 class="relative mb-4 font-bold">
								View forms
								<button
									class="absolute -top-6 -right-6 cursor-pointer rounded-full bg-black px-1 py-1 text-white"
									onClick={closeModal}
								>
									<X />
								</button>
							</h3>
							<ViewFormButtonList
								salesOrderId={selectedSalesOrder()?.salesOrderId}
							/>
						</div>
					</div>
				</div>
			</Show>
		</>
	);
}
