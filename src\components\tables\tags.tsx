import { formatCurrency } from "~/utils/formatting-util";

export function MobileColon() {
	return <span class="mr-3 md:hidden">:</span>;
}

export function QuoteTag() {
	return (
		<span class="ml-1 rounded-full border bg-gray-100 px-1 py-0.5 text-xs font-semibold text-gray-600">
			Quote
		</span>
	);
}

export function FulfilledTag() {
	return (
		<span class="bg-primary ml-1 rounded-full px-2 py-0.5 text-xs font-semibold text-white">
			fulfilled
		</span>
	);
}

export function UnFullfiledTag() {
	return (
		<span class="border-primary text-primary ml-1 rounded-full border bg-white px-2 py-0.5 text-xs font-bold">
			unfulfilled
		</span>
	);
}

export function PriceTag(props: {
	amount: string | number;
	symbol?: string;
	percentage?: number;
	thousandsSeparator?: string;
	decimalSeparator?: string;
	decimalDigits?: number;
	gradientClass?: string;
}) {
	function getWrapperClassName() {
		const className =
			props.percentage && props.percentage > 0 && props.percentage < 100
				? "relative flex items-center overflow-hidden rounded-full border border-[#D78829]"
				: props.percentage === 0
					? "relative flex items-center overflow-hidden rounded-full border border-[#D78829] bg-[#D78829]"
					: "";

		return `price-tag ${className}`;
	}

	return (
		<div class={getWrapperClassName()}>
			<div
				class={`relative px-2 text-[12px] font-semibold ${
					props.percentage && props.percentage > 0 && props.percentage < 100
						? "text-[#D78829]"
						: props.percentage === 0
							? "text-white"
							: "text-black"
				}`}
			>
				<div
					class={`absolute h-full ${
						props.percentage === 0 || props.percentage === 100
							? ""
							: "top-0 left-0 z-[-1] bg-linear-to-r from-[#D78829]/10 to-[#D78829]/60"
					}`}
					style={{
						width: `${props.percentage ?? "100"}%`,
					}}
				></div>
				{formatCurrency({
					amount: props.amount,
					symbol: props.symbol,
					thousandsSeparator: props.thousandsSeparator,
					decimalSeparator: props.decimalSeparator,
					decimalDigits: props.decimalDigits,
				})}
			</div>
		</div>
	);
}
