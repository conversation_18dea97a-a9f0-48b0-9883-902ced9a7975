import ky from "ky";
import { UserListResponse } from "~/types/response";

export interface LoadMoreUsersProps {
	url: string;
	page?: number;
	perPage?: number;
	keyword?: string;
}

export async function clientFetchUsers(
	props: LoadMoreUsersProps,
): Promise<UserListResponse> {
	let apiUrl = `${props.url}?random=1`;

	if (props.page && props.perPage) {
		apiUrl += `&page=${props.page}`;
		apiUrl += `&per_page=${props.perPage}`;
	}

	if (props.keyword) {
		apiUrl += `&keyword=${props.keyword}`;
	}

	try {
		return await ky.get(apiUrl).json<UserListResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more users",
		};
	}
}
