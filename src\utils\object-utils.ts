import { SalesOrderOptionData } from "~/types/dto";

/**
 * Check if the provided object is empty.
 *
 * @export
 * @param {Record<any, any>} obj The provided object.
 * @return {boolean}
 */
export function isObjectEmpty(obj: Record<any, any>): boolean {
	return Object.keys.length === 0;
}

export function isSalesOrderOptionData(
	obj: unknown,
): obj is SalesOrderOptionData {
	if (obj && obj instanceof Object && !Array.isArray(obj)) {
		if ("pol" in obj && obj.pol && typeof obj.pol === "string") {
			return true;
		}

		if (
			"stampedForms" in obj &&
			Array.isArray(obj.stampedForms) &&
			obj.stampedForms.length
		) {
			const allAreStringItems = obj.stampedForms.every(
				(item: any) => typeof item === "string",
			);

			if (allAreStringItems) {
				return true;
			}
		}
	}

	return false;
}
