import ky from "ky";
import { inflowConfig, inflowHttpHeader } from "../../configs/server-config";
import { PaymentTermData } from "../../types/dto";
import {
	PaymentTermListResponse,
	PaymentTermResponse,
} from "~/types/response";
import { getErrorMessage } from "~/utils/http-util";

export interface FetchPaymentTermsProps {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
}

export async function fetchPaymentTerms(
	props?: FetchPaymentTermsProps,
): Promise<PaymentTermListResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? inflowConfig().loadMorePerPage;
	const include = props?.include ?? "";

	let apiUrl = `${baseApiUrl}/payment-terms?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	try {
		const paymentTermList = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json<PaymentTermData[]>();

		return {
			success: true,
			message: "Payment terms fetched successfully",
			data: paymentTermList,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findPaymentTerm(
	id: string,
): Promise<PaymentTermResponse> {
	"use server";

	try {
		const baseApiUrl = inflowConfig().baseInflowApiUrl;
		const paymentTerm = await ky
			.get(`${baseApiUrl}/payment-terms/${id}`, {
				headers: inflowHttpHeader(),
			})
			.json<PaymentTermData>();

		return {
			success: true,
			message: "Payment term fetched successfully",
			data: paymentTerm,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
