import CollectionGalleryGridItem from "./CollectionGalleryGridItem";

export default function MasterCollection(props: {
	collectionName: string;
	class?: string;
}) {
	return (
		<div class={`relative container ${props.class ?? ""}`}>
			<h3 class="relative mb-2 flex flex-wrap items-center justify-start sm:mb-0 sm:justify-center">
				<span class="absolute mt-[3px] block h-[1px] w-full border-b border-stone-200"></span>
				<span class="font-display relative inline-block bg-white px-4 text-xl text-stone-600 md:text-4xl">
					{props.collectionName}
				</span>
			</h3>

			<div class="w-full transition-all duration-300 ease-in-out sm:mt-12 sm:flex sm:flex-wrap sm:justify-center sm:gap-3 sm:px-0 lg:gap-5 xl:gap-10">
				<div class="w-full sm:w-1/4 lg:w-2/6 xl:w-1/5">
					<CollectionGalleryGridItem
						collectionName="Legacy Home Collection (PDF)"
						linkUrl="/master-collections/Legacy-Home-All-Collection-Price-List-2024-rev3.pdf"
						wrapperClass=""
						textClass="text-lg lg:text-xl"
						useNativeLink={true}
					/>
				</div>

				<div class="w-full sm:w-1/4 lg:w-2/6 xl:w-1/5">
					<CollectionGalleryGridItem
						collectionName="Modular Bookcase Collection (PDF)"
						linkUrl="/master-collections/Legacy-Home-Bookcase-Collection-Price-List-2024-rev3.pdf"
						wrapperClass=""
						textClass="text-lg lg:text-xl"
						useNativeLink={true}
					/>
				</div>

				<div class="w-full sm:w-1/4 lg:w-2/6 xl:w-1/5">
					<CollectionGalleryGridItem
						collectionName="Multay Traditional Collection (PDF)"
						linkUrl="/master-collections/Multay-Traditional-Collection-Price-List-2024-rev3.pdf"
						wrapperClass="mb-0"
						textClass="text-lg lg:text-xl"
						useNativeLink={true}
					/>
				</div>
			</div>
		</div>
	);
}
