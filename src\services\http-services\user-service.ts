import { UserListResponse, UserResponse } from "~/types/response";
import { getSupabaseServer } from "./supabase-service";
import { ProfileData, ProfileUpdateData } from "~/types/dto";
import { getErrorMessage } from "~/utils/http-util";
import uFuzzy from "@leeoniya/ufuzzy";
import {
	makeSupabaseUpdateAttributes,
	supabaseUserToProfileData,
} from "~/utils/user-util";
import { AdminUserAttributes } from "@supabase/supabase-js";

export interface FetchUsersProps {
	page?: number;
	perPage?: number;
	keyword?: string | null;
}

export async function fetchUsers(
	props?: FetchUsersProps,
): Promise<UserListResponse> {
	"use server";

	try {
		const {
			data: { users },
			error,
		} =
			props?.page && props?.perPage
				? await getSupabaseServer().auth.admin.listUsers({
						page: props.page,
						perPage: props.perPage,
					})
				: await getSupabaseServer().auth.admin.listUsers();

		if (error) {
			return {
				success: false,
				message: error.message,
			};
		}

		const profiles: ProfileData[] = [];

		for (const user of users) {
			profiles.push(supabaseUserToProfileData(user));
		}

		if (props?.keyword) {
			const profilesToSearch: string[] = [];

			for (const profile of profiles) {
				profilesToSearch.push(
					`${profile.firstName} ${profile.lastName}¦${profile.email}¦${profile.phone}`,
				);
			}

			const fuzzySearch = new uFuzzy();
			const searchIndexes = fuzzySearch.filter(profilesToSearch, props.keyword);

			if (!searchIndexes || !searchIndexes.length) {
				profiles.length = 0;
			} else {
				const matchedProfiles = [];

				for (const index of searchIndexes) {
					const profile = profiles[index];
					matchedProfiles.push(profile);
				}

				profiles.length = 0;

				for (const profile of matchedProfiles) {
					profiles.push(profile);
				}

				// Early free up memory.
				profilesToSearch.length = 0;
			}
		}

		return {
			success: true,
			message: "Users fetched successfully",
			data: profiles,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findUser(id: string): Promise<UserResponse> {
	"use server";

	try {
		const {
			data: { user },
			error,
		} = await getSupabaseServer().auth.admin.getUserById(id);

		if (error) {
			return {
				success: false,
				message: error.message,
			};
		}

		if (!user) {
			return {
				success: false,
				message: "User not found",
			};
		}

		return {
			success: true,
			message: "User fetched successfully",
			data: supabaseUserToProfileData(user),
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function updateUser(
	props: ProfileUpdateData,
): Promise<UserResponse> {
	"use server";

	try {
		const userUpdateData = makeSupabaseUpdateAttributes(props);

		const {
			data: { user },
			error,
		} = await getSupabaseServer().auth.admin.updateUserById(
			props.id,
			userUpdateData,
		);

		if (error) {
			return {
				success: false,
				message: error.message,
			};
		}

		if (!user) {
			return {
				success: false,
				message: "User not found",
			};
		}

		return {
			success: true,
			message: "User updated successfully",
			data: supabaseUserToProfileData(user),
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function deleteUser(id: string): Promise<UserResponse> {
	"use server";

	try {
		const {
			data: { user },
			error,
		} = await getSupabaseServer().auth.admin.deleteUser(id);

		if (error) {
			return {
				success: false,
				message: error.message,
			};
		}

		if (!user) {
			return {
				success: false,
				message: "User not found",
			};
		}

		return {
			success: true,
			message: "User deleted successfully",
			data: supabaseUserToProfileData(user),
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
