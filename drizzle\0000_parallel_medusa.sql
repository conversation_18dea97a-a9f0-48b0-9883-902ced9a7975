-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TABLE `categories` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`category_id` varchar(90) NOT NULL,
	`is_default` tinyint(1) NOT NULL DEFAULT 0,
	`name` varchar(70) NOT NULL,
	`parent_category_id` varchar(90),
	`timestamp` varchar(35) NOT NULL DEFAULT '',
	CONSTRAINT `categories_id` PRIMARY KEY(`id`),
	CONSTRAINT `category_id` UNIQUE(`category_id`)
);
--> statement-breakpoint
CREATE TABLE `customers` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`customer_id` varchar(90) NOT NULL,
	`email` varchar(255) NOT NULL,
	`is_active` tinyint(1) NOT NULL DEFAULT 0,
	`name` varchar(255) NOT NULL,
	`contact_name` varchar(100) NOT NULL,
	`phone` varchar(50) NOT NULL DEFAULT '',
	`website` varchar(255) NOT NULL DEFAULT '',
	`default_billing_address_id` varchar(90) NOT NULL,
	`default_carrier` varchar(100) NOT NULL,
	`default_location_id` varchar(90) NOT NULL,
	`default_payment_method` varchar(100) NOT NULL,
	`default_payment_terms_id` varchar(90) NOT NULL,
	`default_sales_rep` varchar(100) NOT NULL,
	`default_sales_rep_team_member_id` varchar(90) NOT NULL,
	`default_shipping_address_id` varchar(90) NOT NULL,
	`fax` varchar(50) NOT NULL DEFAULT '',
	`pricing_scheme_id` varchar(90) NOT NULL,
	`discount` varchar(100) NOT NULL DEFAULT '',
	`taxing_scheme_id` varchar(90) NOT NULL,
	`last_modified_by_id` varchar(90) NOT NULL,
	`remarks` varchar(300) NOT NULL,
	`tax_exempt_number` varchar(100) NOT NULL DEFAULT '',
	`timestamp` varchar(50) NOT NULL DEFAULT '',
	`custom_fields` json,
	`addresses` json,
	`balances` json,
	`credits` json,
	`default_billing_address` json,
	`default_location` json,
	`default_payment_terms` json,
	`default_sales_rep_team_member` json,
	`default_shipping_address` json,
	`last_modified_by` json,
	`order_history` json,
	`pricing_scheme` json,
	`taxing_scheme` json,
	CONSTRAINT `customers_id` PRIMARY KEY(`id`),
	CONSTRAINT `customer_id` UNIQUE(`customer_id`)
);
--> statement-breakpoint
CREATE TABLE `logs` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`date` datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP),
	`type` varchar(7) NOT NULL DEFAULT 'log',
	`content` json,
	CONSTRAINT `logs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `options` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`created_at` datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP),
	`updated_at` datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP),
	`name` varchar(255) NOT NULL,
	`value` json,
	CONSTRAINT `options_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `pricing_schemes` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`pricing_scheme_id` varchar(90) NOT NULL,
	`name` varchar(255) NOT NULL,
	`currency_id` varchar(90) NOT NULL DEFAULT '',
	`is_active` tinyint(1) NOT NULL DEFAULT 0,
	`is_default` tinyint(1) NOT NULL DEFAULT 0,
	`is_tax_inclusive` tinyint(1) NOT NULL DEFAULT 0,
	`timestamp` varchar(50) NOT NULL DEFAULT '',
	`currency` json,
	`product_prices` json,
	CONSTRAINT `pricing_schemes_id` PRIMARY KEY(`id`),
	CONSTRAINT `pricing_scheme_id` UNIQUE(`pricing_scheme_id`)
);
--> statement-breakpoint
CREATE TABLE `sales_products` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`product_id` varchar(90) NOT NULL,
	`sku` varchar(90) NOT NULL,
	`name` varchar(300) NOT NULL,
	`description` varchar(400) NOT NULL,
	`is_active` tinyint(1) NOT NULL DEFAULT 0,
	`width` varchar(15) NOT NULL DEFAULT '',
	`height` varchar(15) NOT NULL DEFAULT '',
	`length` varchar(15) NOT NULL DEFAULT '',
	`weight` varchar(15) NOT NULL DEFAULT '',
	`auto_assembler` tinyint(1) NOT NULL DEFAULT 0,
	`barcode` varchar(30) NOT NULL,
	`category_id` varchar(90) NOT NULL DEFAULT '',
	`default_image_id` varchar(90) NOT NULL DEFAULT '',
	`hs_tariff_number` varchar(150) NOT NULL DEFAULT '',
	`include_quantity_buildable` tinyint(1) NOT NULL DEFAULT 0,
	`is_manufacturable` tinyint(1) NOT NULL DEFAULT 0,
	`item_type` varchar(70) NOT NULL DEFAULT '',
	`last_modified_by_id` varchar(90) NOT NULL DEFAULT '',
	`last_modified_date_time` varchar(50) NOT NULL DEFAULT '',
	`last_vendor_id` varchar(90) NOT NULL DEFAULT '',
	`origin_country` varchar(100) NOT NULL DEFAULT '',
	`remarks` text NOT NULL,
	`standard_uom_name` varchar(255) NOT NULL DEFAULT '',
	`timestamp` varchar(35) NOT NULL DEFAULT '',
	`total_quantity_on_hand` varchar(15) NOT NULL,
	`track_serials` tinyint(1) NOT NULL DEFAULT 0,
	`custom_fields` json,
	`category` json,
	`cost` json,
	`default_image` json,
	`images` json,
	`default_price` json,
	`inventory_lines` json,
	`item_boms` json,
	`last_modified_by` json,
	`last_vendor` json,
	`prices` json,
	`product_custom_field_labels` json,
	`product_operations` json,
	`purchasing_uom` json,
	`reorder_settings` json,
	`sales_uom` json,
	`tax_codes` json,
	`vendor_items` json,
	CONSTRAINT `sales_products_id` PRIMARY KEY(`id`),
	CONSTRAINT `product_id` UNIQUE(`product_id`)
);
--> statement-breakpoint
CREATE INDEX `contact_name` ON `customers` (`contact_name`);--> statement-breakpoint
CREATE INDEX `is_active` ON `customers` (`is_active`);--> statement-breakpoint
CREATE INDEX `name` ON `customers` (`name`);--> statement-breakpoint
CREATE INDEX `date` ON `logs` (`date`);--> statement-breakpoint
CREATE INDEX `type` ON `logs` (`type`);--> statement-breakpoint
CREATE INDEX `is_active` ON `pricing_schemes` (`is_active`);--> statement-breakpoint
CREATE INDEX `name` ON `pricing_schemes` (`name`);--> statement-breakpoint
CREATE INDEX `is_active` ON `sales_products` (`is_active`);--> statement-breakpoint
CREATE INDEX `name` ON `sales_products` (`name`);--> statement-breakpoint
CREATE INDEX `sku` ON `sales_products` (`sku`);
*/