import ky from "ky";
import { CustomProductData } from "~/types/dto";
import {
	CreateCustomProductDataRespose,
	CustomProductDataListResponse,
	CustomProductDataResponse,
	DeleteCustomProductDataResponse,
	UpdateCustomProductDataRespose,
} from "~/types/response";

export interface ClientFetchCustomProductDataProps {
	url: string;
	customerId?: string;
	perPage?: number;
	page?: number;
	keyword?: string;
}

export async function clientFetchCustomProductData(
	props: ClientFetchCustomProductDataProps,
): Promise<CustomProductDataListResponse> {
	let apiUrl = `${props.url}?includeCount=true`;

	if (props.customerId) {
		apiUrl += `&customer_id=${props.customerId}`;
	}

	if (props.page) {
		apiUrl += `&page=${props.page}`;
	}

	if (props.perPage) {
		apiUrl += `&per_page=${props.perPage}`;
	}

	if (props.keyword) {
		apiUrl += `&keyword=${props.keyword}`;
	}

	try {
		const response = await ky.get(apiUrl).json<CustomProductDataListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more custom product data",
		};
	}
}

export interface ClientFindCustomProductDataProps {
	url: string;
	id: number;
}

export async function clientFindCustomProductData(
	props: ClientFindCustomProductDataProps,
): Promise<CustomProductDataResponse> {
	let apiUrl = `${props.url}/${props.id}`;

	try {
		const response = await ky.get(apiUrl).json<CustomProductDataResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to find custom product data",
		};
	}
}

export interface ClientCreateCustomProductDataProps {
	url: string;
	data: CustomProductData;
}

export async function clientCreateCustomProductData(
	props: ClientCreateCustomProductDataProps,
): Promise<CreateCustomProductDataRespose> {
	const apiUrl = props.url;

	try {
		const response = await ky
			.post(apiUrl, {
				json: props.data,
			})
			.json<CreateCustomProductDataRespose>();
		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to create custom product data",
		};
	}
}

export async function clientUpdateCustomProductData(
	props: ClientCreateCustomProductDataProps,
): Promise<CreateCustomProductDataRespose> {
	const apiUrl = `${props.url}/${props.data.id}`;

	try {
		const response = await ky
			.put(apiUrl, {
				json: props.data,
			})
			.json<UpdateCustomProductDataRespose>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to Update custom product data",
		};
	}
}

export interface ClientDeleteCustomProductDataProps {
	url: string;
	id: number;
}

export async function clientDeleteCustomProductData(
	props: ClientDeleteCustomProductDataProps,
): Promise<DeleteCustomProductDataResponse> {
	const apiUrl = `${props.url}/${props.id}`;

	try {
		const response = await ky
			.delete(apiUrl)
			.json<DeleteCustomProductDataResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to delete custom product data",
		};
	}
}
