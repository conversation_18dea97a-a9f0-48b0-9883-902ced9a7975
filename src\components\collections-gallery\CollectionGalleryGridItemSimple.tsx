import { A } from "@solidjs/router";

export default function CollectionGalleryGridItemSimple(props: {
	collectionName: string;
	linkUrl: string;
}) {
	return (
		<A
			href={props.linkUrl}
			class="block w-full border-b border-b-slate-200 px-4 py-2 text-sm text-gray-500 transition-all duration-300 ease-in-out sm:mx-3 sm:my-2 sm:rounded-lg sm:border-none sm:bg-[#F3FBF6] sm:py-4 sm:text-center sm:font-semibold sm:text-black sm:hover:bg-green-100"
		>
			{props.collectionName}
		</A>
	);
}
