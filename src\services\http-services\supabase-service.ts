import { createServerClient } from "@supabase/ssr";
import { cookieConfig, supabaseConfig } from "~/configs/server-config";
import { getSessionManager } from "./session-service";

export function getSupabaseServer() {
	"use server";

	return createServerClient(
		supabaseConfig().projectUrl,
		supabaseConfig().secretKey,
		{
			cookies: getSessionManager(),
			cookieOptions: {
				// We can't use httpOnly cookie because it's not necessary (mentioned in a GitHub issue).
				// httpOnly: true,
				name: cookieConfig().cookieName,
			},
			// auth: {
			// 	flowType: "pkce",
			// 	storageKey: cookieConfig().cookieName,
			// },
		},
	);
}
