import { Show, batch, createSignal } from "solid-js";
import { JobOrderProductRemarksData, ProfileData } from "~/types/dto";
import {
	clientCreateJobOrderProductRemark,
	clientUpdateJobOrderProductRemark,
} from "~/services/client-services/job-order-client-service";
import { allowedEmailsToManageJobOrder } from "~/configs/app-config";
import { Loader2, Save, SquarePen } from "lucide-solid";

export default function RemarksAction(props: {
	currentUser?: ProfileData;
	apiUrl: string;
	salesOrderId: string;
	salesOrderLineId: string;
	productId: string;
	remarks?: JobOrderProductRemarksData;
	afterDataSaved?: (data: JobOrderProductRemarksData) => void;
}) {
	if (
		props.remarks &&
		!props.remarks.salesOrderLineId &&
		props.salesOrderLineId
	) {
		props.remarks.salesOrderLineId = props.salesOrderLineId;
	}

	const [remarksData, setRemarksData] =
		createSignal<JobOrderProductRemarksData>(
			props.remarks ?? {
				id: 0,
				remarks: "",
				salesOrderId: props.salesOrderId,
				salesOrderLineId: props.salesOrderLineId,
				productId: props.productId,
			},
		);

	const [editMode, setEditMode] = createSignal(false);
	const [isSaving, setIsSaving] = createSignal<boolean>(false);

	let textareaRef: HTMLTextAreaElement | undefined;

	async function handleDataSave() {
		setIsSaving(true);

		const newRemarksData: JobOrderProductRemarksData = {
			id: remarksData().id,
			salesOrderId: props.salesOrderId,
			salesOrderLineId: props.salesOrderLineId,
			productId: props.productId,
			remarks: textareaRef?.value ?? "",
		};

		const response = newRemarksData.id
			? await clientUpdateJobOrderProductRemark({
					url: props.apiUrl,
					data: newRemarksData,
				})
			: await clientCreateJobOrderProductRemark({
					url: props.apiUrl,
					data: newRemarksData,
				});

		batch(() => {
			setEditMode(false);

			if (response.success) {
				if (response.data) setRemarksData(response.data);
			} else {
				alert(response.message);
			}
		});

		if (response.data) props.afterDataSaved?.(response.data);
	}

	function openEditMode() {
		setEditMode(true);
	}

	function closeEditMode() {
		setEditMode(false);
	}

	function canEditRemarks() {
		return (
			props.currentUser &&
			props.currentUser?.email &&
			allowedEmailsToManageJobOrder.includes(props.currentUser?.email)
		);
	}

	return (
		<Show
			when={editMode()}
			fallback={
				<div class="relative h-full w-full">
					<div class="text-left">{remarksData().remarks}</div>

					<Show when={canEditRemarks()}>
						<button
							class="hover:bg-primary absolute top-0 right-0 rounded-full bg-gray-200 p-2 hover:text-white print:hidden"
							onClick={openEditMode}
						>
							<SquarePen size={13} />
						</button>
					</Show>
				</div>
			}
		>
			<div class="relative h-full w-full">
				<textarea
					ref={textareaRef}
					class="relative block w-full resize-none rounded-sm border-2 border-gray-200 p-2 print:hidden"
					rows={4}
					value={props.remarks?.remarks ?? ""}
				/>
				<div class="mt-2 flex flex-wrap items-center justify-between">
					<button
						type="button"
						class="relative inline-flex w-[49%] items-center justify-center rounded-sm bg-gray-200 p-2 text-gray-600 print:hidden"
						onClick={closeEditMode}
					>
						Cancel
					</button>

					<Show
						when={isSaving()}
						fallback={
							<button
								type="button"
								class="bg-primary relative inline-flex w-[49%] items-center justify-center rounded-sm p-2 text-white print:hidden"
								onClick={handleDataSave}
							>
								<Save class="mr-1.5" size={16} />
								Save
							</button>
						}
					>
						<div class="bg-primary relative flex w-[49%] items-center justify-center rounded-sm p-2 text-white print:hidden">
							<Loader2 class="h-4 w-4 animate-spin" />
						</div>
					</Show>
				</div>
			</div>
		</Show>
	);
}
