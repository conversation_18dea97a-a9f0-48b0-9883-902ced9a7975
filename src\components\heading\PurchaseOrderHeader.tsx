import { Show } from "solid-js";
import { PurchaseOrderData } from "~/types/dto";
import { formatDate } from "~/utils/formatting-util";

export default function PurchaseOrderHeader(props: {
	purchaseOrder: PurchaseOrderData;
}) {
	function formatShipDate(): string {
		const shipDate = props.purchaseOrder?.requestShipDate ?? "";
		if (!shipDate) return "";

		const shipDateSplits = shipDate.split("T");
		const shipDateSimple = shipDateSplits[0];

		return formatDate(shipDateSimple);
	}

	return (
		<div class="w-full items-center justify-center">
			<div class="text-[10px] font-medium print:text-[8px]">
				<section class="flex">
					<div class="flex w-1/3 items-center print:w-auto">
						<img src="/images/multay-logo-compact.png" alt="" class="w-24" />
						<div class="">
							<h4 class="font-semibold">PT. MULTAY INTERNATIONAL INDONESIA</h4>
							<div class="text-slate-600">
								<p>Jl. Raya Demak - Kudus KM7</p>
								<p>Trenggulli, Wonosalam, Demak 59571</p>
								<p>Central Java, Indonesia</p>
								<p>email <EMAIL></p>
								<p>phone +62291 4284 309</p>
								<p>www.multay.com</p>
							</div>
						</div>
					</div>

					<img
						src="/images/svlk-logo.jpg"
						alt=""
						class="mx-auto h-20 w-20 pt-3"
					/>

					<div class="w-1/3">
						<div class="ml-auto w-1/3 print:w-3/6">
							<p class="text-center text-[13px] font-bold">Purchase Order</p>
							<div class="mt-1 border border-black p-3 print:p-2">
								<div class="flex">
									<p class="font-light">Order #</p>
									<p class="ml-auto font-semibold">
										{props.purchaseOrder.orderNumber}
									</p>
								</div>
								<div class="flex">
									<p class="mr-5 font-light">Date</p>
									<p class="ml-auto font-semibold">
										{formatDate(props.purchaseOrder.orderDate)}
									</p>
								</div>
							</div>
						</div>
					</div>
				</section>
				<section class="mt-7 flex">
					<div class="font-base flex">
						<p class="font-semibold">Vendor Address :</p>
						<div class="ml-5 font-medium text-slate-600">
							<p class="font-bold">
								{props.purchaseOrder.vendor?.name ??
									props.purchaseOrder.contactName}
							</p>
							<p>{props.purchaseOrder.vendorAddress?.address1}</p>
							<p>{props.purchaseOrder.vendorAddress?.address2}</p>
							{/* <p>{props.salesOrder.billingAddress.addressType.value}</p>
              <p>{props.salesOrder.billingAddress.addressType.hasValue}</p> */}
							<p>{props.purchaseOrder.vendorAddress?.city}</p>
							<p>{props.purchaseOrder.vendorAddress?.country}</p>
							<p>{props.purchaseOrder.vendorAddress?.postalCode}</p>
							<p>{props.purchaseOrder.vendorAddress?.remarks}</p>
							<p>{props.purchaseOrder.vendorAddress?.state}</p>
						</div>
					</div>

					<div class="ml-auto flex">
						<p class="font-semibold">Ship To Address :</p>
						<div class="ml-5 text-slate-600">
							<p class="font-bold">{props.purchaseOrder.shipToCompanyName}</p>
							<p>{props.purchaseOrder.shipToAddress?.address1}</p>
							<p>{props.purchaseOrder.shipToAddress?.address2}</p>
							{/* <p>{props.salesOrder.shipToAddress?.addressType.value}</p>
              <p>{props.salesOrder.shipToAddress?.addressType.hasValue}</p> */}
							<p>{props.purchaseOrder.shipToAddress?.city}</p>
							<p>{props.purchaseOrder.shipToAddress?.country}</p>
							<p>{props.purchaseOrder.shipToAddress?.postalCode}</p>
							<p>{props.purchaseOrder.shipToAddress?.remarks}</p>
							<p>{props.purchaseOrder.shipToAddress?.state}</p>
						</div>
					</div>
				</section>
				<section class="mt-7 grid grid-cols-3">
					<div class="flex">
						<div class="mr-2 font-semibold">
							<p>Contact</p>
							<p>Phone</p>
							<p>Email</p>
						</div>
						<div class="ml-12 text-slate-600">
							<p>{props.purchaseOrder.contactName}</p>
							<p>{props.purchaseOrder.phone}</p>
							<p>{props.purchaseOrder.email}</p>
						</div>
					</div>
				</section>
				<section class="mt-7">
					<div class="flex border-t border-l border-black bg-gray-100 text-sm print:text-[8px]">
						<div class="w-1/3 border-r border-black py-1 text-center print:py-0">
							Vendor Order Number
						</div>
						<div class="w-1/3 border-r border-black py-1 text-center print:py-0">
							Requested Ship Date
						</div>
						<div class="w-1/3 border-r border-black py-1 text-center print:py-0">
							Payment Terms
						</div>
					</div>
					<div class="flex border-y border-l border-black text-sm opacity-75 print:text-[9px] print:leading-[10px] print:font-light">
						<div class="flex w-1/3 items-center border-r border-black py-1 text-center">
							<p class="m-auto">{props.purchaseOrder.vendorOrderNumber}</p>
						</div>
						<div class="flex w-1/3 items-center border-r border-black py-1 text-center">
							<p class="m-auto" data-ship-date={formatShipDate()}>
								{formatShipDate()}
							</p>
						</div>
						<div class="flex w-1/3 items-center border-r border-black py-1 text-center">
							<Show when={props.purchaseOrder?.paymentTerms}>
								<p class="m-auto">{props.purchaseOrder.paymentTerms?.name}</p>
							</Show>
						</div>
					</div>
				</section>
			</div>
		</div>
	);
}
