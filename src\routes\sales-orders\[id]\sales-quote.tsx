import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { Show } from "solid-js";
import BankAndAccountDetail from "~/components/BankAndAccountDetail";
import SalesQuoteHeader from "~/components/heading/SalesQuoteHeader";
import SalesQuoteTable from "~/components/tables/SalesQuoteTable";
import { inflowConfig } from "~/configs/server-config";
import { CustomProductData } from "~/types/dto";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { findSomeCustomProductDataList } from "~/services/http-services/custom-product-data-service";
import { findSalesOrder } from "~/services/http-services/sales-order-service";

const getPageData = query(async (salesOrderId: string) => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	// console.log("salesOrderId:", salesOrderId);

	const salesOrderResponse = await findSalesOrder(salesOrderId);

	const salesOrder = salesOrderResponse.data;
	const customProductDataCollection: Record<string, CustomProductData> = {};

	if (salesOrderResponse.success) {
		const productLines = salesOrderResponse.data?.lines ?? [];
		const productIds: string[] = [];

		for (const line of productLines) {
			productIds.push(line.productId);
		}

		const customProductDataListResponse = await findSomeCustomProductDataList({
			customerId: salesOrderResponse.data?.customerId ?? "",
			productIds: productIds,
		});

		if (customProductDataListResponse.success) {
			const customProductDataList = customProductDataListResponse.data ?? [];

			for (const customProductData of customProductDataList) {
				customProductDataCollection[customProductData.productId] =
					customProductData;
			}
		}
	}

	return {
		salesOrder: salesOrder,
		customProductDataCollection: customProductDataCollection,
		incomeCategoryId: inflowConfig().incomeCategoryId,
	};
}, "salesQuotePageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function SalesQuotePage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	return (
		<div class="w-[1280px] p-[1rem] lg:w-auto xl:p-[5rem] print:w-auto print:p-0">
			<Show when={pageData()} fallback="Loading data...">
				<SalesQuoteHeader salesOrder={pageData()?.salesOrder!} />
				<SalesQuoteTable
					salesOrder={pageData()?.salesOrder!}
					customProductDataCollection={pageData()?.customProductDataCollection!}
					incomeCategoryId={pageData()?.incomeCategoryId ?? ""}
				/>
			</Show>
			<BankAndAccountDetail />
		</div>
	);
}
