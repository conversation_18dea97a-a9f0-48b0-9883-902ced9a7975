import { User } from "lucide-solid";
import { CustomerData } from "~/types/dto";

export interface CustomerDataRowProps {
	customer: CustomerData;
	onClick?: (customer: CustomerData) => void;
}

export default function CustomerDataRow(props: CustomerDataRowProps) {
	function openModal() {
		if (props.onClick) {
			props.onClick(props.customer);
		}
	}

	return (
		<button class="w-full" onClick={openModal}>
			<div class="border-b hover:bg-gray-50">
				<div class="container flex items-center px-[1rem] py-2 text-xs md:px-0">
					<User class="text-primary mr-3 h-4 w-4 md:hidden" />
					<div class="text-left md:w-3/12">
						<div class="text-sm font-semibold">{props.customer.name}</div>
						<div class="text-gray-600 italic">{props.customer.contactName}</div>
						<div class="mt-2 flex items-center gap-3 md:hidden">
							<div class="rounded-sm bg-black/10 px-2 py-1">
								<span>
									Balance <span class="mr-0.5 font-semibold">Rp</span>
								</span>
								<span class="font-bold">
									{props.customer &&
									props.customer.balances &&
									props.customer.balances.length > 0 ? (
										<p>{props.customer.balances[0].balance}</p>
									) : (
										"0"
									)}
								</span>
							</div>
							<div class="rounded-sm bg-black/10 px-2 py-1">
								<span>
									Credit <span class="mr-0.5 font-semibold">Rp</span>
								</span>
								<span class="font-bold">
									{props.customer &&
									props.customer.credits &&
									props.customer.credits.length > 0 ? (
										<p>{props.customer.credits[0].balance}</p>
									) : (
										"0"
									)}
								</span>
							</div>
						</div>
					</div>
					<div class="hidden text-left md:block md:w-2/12">
						{props.customer.phone}
					</div>
					<div class="hidden text-left md:block md:w-2/12">
						{props.customer.email}
					</div>
					<div class="hidden text-right md:block md:w-2/12">
						{props.customer &&
						props.customer.addresses &&
						props.customer.addresses.length > 0 ? (
							<div class="flex">
								<p>{props.customer.addresses[0].address.city}</p>
								<p>{props.customer.addresses[0].address.state}</p>
							</div>
						) : (
							""
						)}
					</div>
					<div class="hidden text-right md:block md:w-1/12">
						{props.customer &&
						props.customer.addresses &&
						props.customer.addresses.length > 0 ? (
							<p>{props.customer.addresses[0].address.country}</p>
						) : (
							""
						)}
					</div>
					<div class="hidden text-right md:block md:w-2/12">
						{props.customer.defaultLocation?.name}
					</div>
				</div>
			</div>
		</button>
	);
}
