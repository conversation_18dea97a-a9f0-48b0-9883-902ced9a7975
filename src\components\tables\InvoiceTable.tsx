import { createSignal, For, onCleanup, onMount, Show } from "solid-js";
import {
	CustomProductData,
	PaymentTermData,
	SalesOrderData,
	SalesOrderOptionData,
} from "~/types/dto";
import {
	formatCurrency,
	formatDiscount,
	formatQuantity,
} from "~/utils/formatting-util";
import SalesActionButtons from "../sales-orders/SalesActionButtons";
import { isServer } from "solid-js/web";
import { defaultPOLvalue } from "~/configs/app-config";
import FootNote from "../sales-orders/FootNote";
import { useAction, useSubmission } from "@solidjs/router";
import { savePolAction } from "~/etc/server-actions";
import { PolEditForm } from "./PolEditForm";

export interface InvoiceTableProps {
	salesOrder: SalesOrderData;
	customProductDataCollection: Record<string, CustomProductData>;
	defaultPaymentTerm?: PaymentTermData;
	incomeCategoryId?: string;
	options?: SalesOrderOptionData;
}

export default function InvoiceTable(props: InvoiceTableProps) {
	let totalQuantity = 0;
	let subtotalNettWeight = 0;
	let subtotalGrossWeight = 0;
	let totalNettWeight = 0;
	let totalGrossWeight = 0;
	let totalWoodM3 = 0;

	const [pol, setPol] = createSignal(props.options?.pol ?? defaultPOLvalue);
	const [editingPol, setEditingPol] = createSignal(false);
	const [stamped, setStamped] = createSignal(true);

	const productLines = props.salesOrder.lines ?? [];

	const hasAnyDiscount = productLines.some((line) => {
		const discount: number = Number(line.discount.value);

		if (discount > 0) {
			return true;
		}

		return false;
	});

	const cbmFromCustomField = props.salesOrder.customFields?.custom1 ?? "";

	const nettWeightFromCustomField =
		props.salesOrder.customFields?.custom6 ?? "";

	const grossWeightFromCustomField =
		props.salesOrder.customFields?.custom7 ?? "";

	const containerAndSealID = props.salesOrder.customFields?.custom5 ?? "";
	const containerAndSealIDSplits = containerAndSealID.split("/");

	const sealID =
		containerAndSealIDSplits[containerAndSealIDSplits.length - 1] ?? "";

	const container = containerAndSealID.replace(`/${sealID}`, "");

	const woodM3FromCustomField = props.salesOrder.customFields?.custom8 ?? "";

	const blNumber = props.salesOrder.customFields?.custom2 ?? "";
	const pod = props.salesOrder.customFields?.custom3 ?? "";

	const hasAnyCustomProductData =
		Object.keys(props.customProductDataCollection).length > 0;

	function handleXlsxExport(
		formRef?: HTMLFormElement,
		dataFieldRef?: HTMLInputElement,
	) {
		if (isServer) return;
		if (!formRef || !dataFieldRef) return;
		formRef.action = "/api/xlsx/invoice";

		const postData = {
			salesOrder: props.salesOrder,
			customProductDataCollection: props.customProductDataCollection,
			defaultPaymentTerm: props.defaultPaymentTerm,
		};

		const jsonContent = JSON.stringify(postData);

		dataFieldRef.value = jsonContent;

		setTimeout(() => {
			formRef.submit();
		}, 50);
	}

	function handleStamp() {
		setStamped(!stamped());
	}

	onMount(() => {
		if (isServer) return;
		document.addEventListener("keydown", handleEscKeyPressed);
	});

	onCleanup(() => {
		if (isServer) return;
		document.removeEventListener("keydown", handleEscKeyPressed);
	});

	function handleEscKeyPressed(e: KeyboardEvent) {
		if (e.key !== "Escape" && e.key !== "Esc") return;
		setEditingPol(false);
	}

	function handleEditButtonClick() {
		setEditingPol(true);
	}

	function handlePolInputEvent(e: InputEvent) {
		if (!(e.currentTarget instanceof HTMLInputElement)) return;
		setPol(e.currentTarget.value);
	}

	const savePol = useAction(savePolAction);
	const savingPol = useSubmission(savePolAction);

	async function handleSavePol(e: SubmitEvent) {
		e.preventDefault();

		const result = await savePol({
			salesOrderId: props.salesOrder.salesOrderId,
			options: props.options,
			pol: pol(),
		});

		if (result) setEditingPol(false);
	}

	function editPolFormShouldOpen() {
		if (savingPol.pending || savingPol.result === null) {
			return true;
		}

		return editingPol();
	}

	function getCalculatedPolValue() {
		if (savingPol.result) return savingPol.result;
		return pol();
	}

	function formatPrice(amount: string | number): string {
		return formatCurrency({
			amount: amount,
			symbol: props.salesOrder.currency?.symbol,
			thousandsSeparator: props.salesOrder.currency?.thousandsSeparator,
			decimalSeparator: props.salesOrder.currency?.decimalSeparator,
			decimalDigits: props.salesOrder.currency?.decimalPlaces,
		});
	}

	const firstThSharedClass =
		"flex items-center flex-wrap justify-center border-x border-black p-1 font-medium";
	const thSharedClass =
		"flex items-center flex-wrap justify-center border-r border-black px-1 font-medium";

	const firstTdSharedClass = "border-x border-black p-1 xl:p-2 print:p-0.5 ";
	const tdSharedClass = "border-r border-black p-1 xl:p-2 print:p-0.5 ";

	const remarksValueClassName = "font-light uppercase text-slate-600";

	return (
		<div class="text-xs print:text-[9px]">
			<table class="mt-5 w-full table-fixed border-y border-black">
				<thead class="text-center">
					<tr class="flex w-full flex-wrap items-stretch border-b border-black bg-gray-100">
						<th class={`w-4/12 ${firstThSharedClass}`}>Multay Product</th>
						<Show when={hasAnyCustomProductData}>
							<th class={`w-2/12 py-1 ${thSharedClass}`}>Customer Product</th>
						</Show>
						<th
							class={`${
								hasAnyCustomProductData ? "w-2/12" : "w-4/12"
							} py-1 ${thSharedClass}`}
						>
							Picture *)
						</th>
						<th class={`w-1/12 py-1 ${thSharedClass}`}>Quantity (pcs)</th>
						<th class={`w-1/12 py-1 ${thSharedClass}`}>Unit Price (US $)</th>
						<th class={`w-2/12 ${thSharedClass}`}>
							<Show when={hasAnyDiscount}>
								<span class="flex h-full w-1/3 items-center justify-center border-r border-black text-center">
									Discount
								</span>
							</Show>
							<span class={hasAnyDiscount ? "w-2/3" : "w-full"}>
								Subtotal (US $)
							</span>
						</th>
					</tr>
				</thead>

				<tbody class="text-center">
					<For each={props.salesOrder.lines}>
						{(line) => {
							const isIncomeProduct =
								line.product?.categoryId === props.incomeCategoryId;

							const quantity = line.quantity.standardQuantity;
							const quantityNumber = Number(quantity);
							totalQuantity += isIncomeProduct ? 0 : quantityNumber;
							const discount = line.discount;
							const unitPriceNumber = Number(line.unitPrice);

							const discountNumber = Number(discount.value);
							const discountAmount = discount.isPercent
								? unitPriceNumber * (discountNumber / 100)
								: discountNumber;
							const priceUnitAfterDiscount = unitPriceNumber - discountAmount;
							const priceAfterDiscount =
								priceUnitAfterDiscount * quantityNumber;

							const customFields = line.product?.customFields;

							const nettWeight = customFields?.custom1 ?? "";
							const nettWeightNumber = Number(nettWeight);

							subtotalNettWeight += nettWeightNumber;
							totalNettWeight += subtotalNettWeight;

							const grossWeight = customFields?.custom2 ?? "";
							const grossWeightNumber = Number(grossWeight);

							subtotalGrossWeight += grossWeightNumber;
							totalGrossWeight += subtotalGrossWeight;

							const woodM3 = customFields?.custom7 ?? "";
							const totalWoodM3Number = Number(woodM3);
							totalWoodM3 += totalWoodM3Number;

							const customFieldsLabel = line.product?.productCustomFieldLabels;
							const CBM = customFieldsLabel?.custom1 ?? "";

							const customProductData =
								props.customProductDataCollection[line.productId];

							const images = line.product?.images;
							const imgUrl =
								images && images?.length ? images[0].largeUrl : undefined;

							return (
								<tr class="font-base flex w-full flex-wrap items-stretch border-b border-black text-gray-700 print:font-light">
									<td class={`w-4/12 ${firstTdSharedClass} text-left`}>
										<span class="uppercase">{line.product?.sku}</span>
										<br />
										<span>{line.product?.name}</span>
									</td>
									<Show when={hasAnyCustomProductData}>
										<td class={`w-2/12 ${tdSharedClass} text-left`}>
											<Show when={customProductData}>
												<Show when={customProductData.customerProductCode}>
													<>
														<span>{customProductData.customerProductCode}</span>
														<br />
													</>
												</Show>
												<span>{customProductData.customerProductName}</span>
											</Show>
										</td>
									</Show>
									<td
										class={`${
											hasAnyCustomProductData ? "flex w-2/12" : "flex w-4/12"
										} ${tdSharedClass}`}
										colspan={customProductData ? undefined : "2"}
									>
										<Show when={imgUrl}>
											<img
												src={imgUrl}
												alt="Product image"
												class={`print:max:w-3/4 m-auto max-h-48 print:max-h-20`}
											/>
										</Show>
									</td>
									<td
										class={`w-1/12 ${tdSharedClass} ${imgUrl ? "" : "flex items-center justify-center"}`}
									>
										<Show when={!isIncomeProduct}>
											{formatQuantity(quantity)}
										</Show>
									</td>
									<td
										class={`w-1/12 ${tdSharedClass} ${imgUrl ? "" : "flex items-center justify-center"}`}
									>
										{formatPrice(line.unitPrice)}
									</td>
									<td
										class={`flex w-2/12 ${tdSharedClass} ${imgUrl ? "" : "items-center"}`}
									>
										{/* {formatPrice(line.subTotal)} */}
										<Show when={hasAnyDiscount}>
											<div class={`${tdSharedClass} h-full w-1/3`}>
												<Show when={discount.value !== "0.00000"}>
													{formatDiscount(discount.value ?? "")}

													<Show when={discount.isPercent}>
														<span>%</span>
													</Show>
												</Show>
											</div>
										</Show>
										<span class={hasAnyDiscount ? "w-2/3" : "w-full"}>
											{formatPrice(priceAfterDiscount)}
										</span>
									</td>
								</tr>
							);
						}}
					</For>
				</tbody>

				<tbody class="text-center">
					<tr class="flex w-full flex-wrap items-stretch text-xs print:text-[9px]">
						<td
							class={`w-8/12 border-x border-black p-1 font-medium xl:p-2`}
							colspan={hasAnyCustomProductData ? "3" : "2"}
						>
							<span class="m-auto">Total</span>
						</td>

						<td class="w-1/12 border-r border-black p-1 xl:p-2">
							{totalQuantity}
						</td>
						<td class="w-1/12 border-r border-black p-1 xl:p-2">&nbsp;</td>
						<td class={`flex w-2/12 ${tdSharedClass} items-center`}>
							{/* {formatPrice(props.salesOrder.total)} */}
							<span
								class={
									hasAnyDiscount ? "w-1/3 border-r border-black" : "hidden"
								}
							></span>
							<span class={hasAnyDiscount ? "w-2/3" : "w-full"}>
								{formatPrice(props.salesOrder.total)}
							</span>
						</td>
					</tr>
				</tbody>
			</table>

			<FootNote />

			<section class="mt-4 flex w-full text-xs print:text-[9px] print:leading-[10px]">
				<div class="w-2/5">
					<p class="mb-1">Remarks :</p>
					<div class="border border-black p-1 xl:p-2 print:font-light">
						<table>
							<tbody>
								<tr class="">
									<td class="min-w-[130px]">Container</td>
									<td class={remarksValueClassName}>
										: {container ? container : "-"}
									</td>
								</tr>

								<tr>
									<td>Seal ID</td>
									<td class={remarksValueClassName}>
										: {sealID ? sealID : "-"}
									</td>
								</tr>

								<tr>
									<td>BL Number</td>
									<td class={remarksValueClassName}>
										: {blNumber ? blNumber : "-"}
									</td>
								</tr>

								<tr>
									<td>Nett Weight (kgs)</td>
									<td class={remarksValueClassName}>
										:{" "}
										{nettWeightFromCustomField
											? nettWeightFromCustomField
											: "-"}
									</td>
								</tr>

								<tr>
									<td>Gross Weight (kgs)</td>
									<td class={remarksValueClassName}>
										:{" "}
										{grossWeightFromCustomField
											? grossWeightFromCustomField
											: "-"}
									</td>
								</tr>

								<tr>
									<td>Total CBM</td>
									<td class={remarksValueClassName}>
										: {cbmFromCustomField ? cbmFromCustomField : "-"}
									</td>
								</tr>

								<tr>
									<td>POL</td>
									<td class={remarksValueClassName}>
										:{" "}
										<PolEditForm
											salesOrderId={props.salesOrder.salesOrderId}
											salesOrderOptions={props.options}
											openForm={editPolFormShouldOpen()}
											calculatedValue={getCalculatedPolValue()}
											inputValue={pol()}
											isSaving={savingPol.pending ?? false}
											onEditButtonClick={handleEditButtonClick}
											onInput={handlePolInputEvent}
											onSubmit={handleSavePol}
										/>
									</td>
								</tr>

								<tr>
									<td>POD</td>
									<td class={remarksValueClassName}>: {pod ? pod : "-"}</td>
								</tr>

								<tr>
									<td>Total Wood M3</td>
									<td class={remarksValueClassName}>
										: {woodM3FromCustomField ? woodM3FromCustomField : "-"}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<Show when={stamped()}>
					<div class="flex w-3/5 items-center justify-end">
						<img src="/images/anif-ttd.png" class="w-52" alt="Multay Stamp" />
					</div>
				</Show>
			</section>

			<SalesActionButtons
				onXlsxExport={handleXlsxExport}
				onStamp={handleStamp}
				salesOrder={props.salesOrder}
				form="invoice"
				id={props.salesOrder.salesOrderId}
				formName="Invoice"
			/>
		</div>
	);
}
