import ky from "ky";
import { SendEmailResponse } from "~/types/response";
import { SendEmailProps } from "~/services/http-services/email-service";

export interface ClientSendEmailProps {
	url: string;
	data: SendEmailProps;
}

export async function clientSendEmail(
	props: ClientSendEmailProps,
): Promise<SendEmailResponse> {
	try {
		const response = await ky
			.post(props.url, {
				json: props.data,
			})
			.json<SendEmailResponse>();

		return response;
	} catch (e: unknown) {
		// console.log("Error when sending email from browser:", e);

		return {
			success: false,
			message: "Failed to send email",
		};
	}
}
