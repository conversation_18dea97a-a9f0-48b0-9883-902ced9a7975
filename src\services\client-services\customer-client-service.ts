import ky from "ky";
import { CustomerListResponse } from "~/types/response";

export interface LoadMoreCustomersProps {
	url: string;
	before?: string;
	after?: string;
	keyword?: string;
}

export async function clientFetchCustomers(
	props: LoadMoreCustomersProps,
): Promise<CustomerListResponse> {
	let apiUrl = `${props.url}?includeCount=true`;

	if (props.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props.keyword) {
		apiUrl += `&keyword=${props.keyword}`;
	}

	try {
		const response = await ky.get(apiUrl).json<CustomerListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more customers",
		};
	}
}
