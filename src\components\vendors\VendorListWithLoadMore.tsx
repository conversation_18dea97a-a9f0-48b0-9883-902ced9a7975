import { For, Show, batch, createSignal } from "solid-js";
import LoadMore from "~/components/data-loading/LoadMore";
import LoadingSpinner from "~/components/LoadingSpinner";
import SearchBehavior from "../data-loading/SearchBehavior";
import { useSearchParams } from "@solidjs/router";
import { VendorData } from "~/types/dto";
import { VendorListResponse } from "~/types/response";
import { clientFetchVendors } from "~/services/client-services/vendor-client-service";
import VendorDataRow from "./VendorDataRow";
import { getSearchKeyword } from "~/utils/search-util";

export default function VendorListWithLoadMore(props: {
	vendors: VendorData[];
	searchField?: HTMLInputElement;
}) {
	const [searchParams, setSearchParams] = useSearchParams();
	const [vendors, setVendors] = createSignal(props.vendors);
	const [doingLoadMore, setDoingLoadMore] = createSignal<boolean>(false);
	const [loadMoreFinished, setLoadMoreFinished] = createSignal<boolean>(false);

	async function handleLoadMore() {
		if (doingLoadMore() || loadMoreFinished()) return;

		batch(() => {
			setDoingLoadMore(true);
		});

		const response = await fetchDataList(getSearchKeyword(searchParams));

		handleFetchComplete(response);
	}

	async function fetchDataList(keyword?: string): Promise<VendorListResponse> {
		const lastVendorItem = vendors().length
			? vendors()[vendors().length - 1]
			: undefined;

		return await clientFetchVendors({
			after: lastVendorItem?.vendorId ?? undefined,
			keyword: keyword,
			filters: {
				isActive: true,
			},
		});
	}

	async function handleSearch(keyword: string) {
		batch(() => {
			setLoadMoreFinished(false);
			setDoingLoadMore(true);
			setSearchParams({ keyword: keyword });
			setVendors([]);
		});

		const response = await fetchDataList(keyword);

		handleFetchComplete(response);
	}

	function handleFetchComplete(response: VendorListResponse): void {
		batch(() => {
			setDoingLoadMore(false);

			if (!response.success) {
				alert(response.message);
				setLoadMoreFinished(true);
				return;
			}

			if (!response?.data?.length) {
				setLoadMoreFinished(true);
				return;
			}

			setVendors(vendors().concat(response.data));
		});
	}

	let sectionRef: HTMLElement | undefined;

	return (
		<>
			<section class="text pt-32 text-sm" ref={sectionRef}>
				<SearchBehavior
					searchField={props.searchField}
					onSearch={handleSearch}
				/>
				<LoadMore contentRef={sectionRef} onLoadMore={handleLoadMore}>
					<For each={vendors()}>
						{(vendor) => <VendorDataRow vendor={vendor} />}
					</For>
				</LoadMore>

				<Show when={doingLoadMore()}>
					<LoadingSpinner class="mt-14" />
				</Show>
			</section>
		</>
	);
}
