import { For, Show } from "solid-js";
import CollectionGalleryPriceListTable from "~/components/collections-gallery/CollectionGalleryPriceListTable";
import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
	useSearchParams,
} from "@solidjs/router";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { fetchCollectionProducts } from "~/services/http-services/page-service";
import LoadingSpinner from "~/components/LoadingSpinner";
import CollectionGalleryHeader from "~/components/collections-gallery/CollectionGalleryHeader";
import CollectionGalleryPageHeader from "~/components/collections-gallery/CollectionGalleryPageHeader";
import CollectionGalleryHeaderNav from "~/components/collections-gallery/CollectionGalleryHeaderNav";
import { recursivelyFindMatchingCategory } from "~/utils/category-util";
import CollectionGalleryGridItemSimple from "~/components/collections-gallery/CollectionGalleryGridItemSimple";
import { getSearchKeyword } from "~/utils/search-util";
import { dbFindCollectionCategory } from "~/services/db-services/collection-db-service";

const getPageData = query(
	async (collectionId: string, subCollectionId: string) => {
		"use server";

		const isLoggedIn = await isAuthenticated();

		if (!isLoggedIn) {
			throw redirect("/login/");
		}

		let errorMessage: string | undefined = undefined;

		const collection = await dbFindCollectionCategory(collectionId);

		const subCollection = collection
			? recursivelyFindMatchingCategory(subCollectionId, collection)
			: undefined;

		const isPriceListPage = subCollection
			? subCollection.children && subCollection.children.length === 0
			: false;

		const products = isPriceListPage
			? await fetchCollectionProducts({
					existingProducts: [],
					perPage: 100,
					collectionId: subCollectionId,
				})
			: [];

		return {
			errorMessage,
			collection,
			subCollection,
			isPriceListPage,
			products,
		};
	},
	"collectionGallerySubIdPageData",
);

export const route = {
	preload: ({ params }) => getPageData(params.id, params.sub_id),
} satisfies RouteDefinition;

export default function CollectionGallerySubIdPage(props: RouteSectionProps) {
	const pageData = createAsync(
		() => getPageData(props.params.id, props.params.sub_id),
		{
			deferStream: true,
		},
	);

	let searchFieldRef: HTMLInputElement | undefined;

	const [searchParams, setSearchParams] = useSearchParams();

	return (
		<>
			<Show when={!pageData()}>
				<div class="relative flex h-full w-full items-center justify-center">
					<LoadingSpinner class="mt-14" />
				</div>
			</Show>

			<Show when={pageData() && pageData()?.isPriceListPage}>
				<Show
					when={!pageData()?.errorMessage}
					fallback={pageData()?.errorMessage}
				>
					<div class="relative min-h-screen w-full bg-cover bg-fixed">
						<div
							class="fixed h-screen w-screen bg-cover print:hidden"
							style="background-image: url('/images/CollectionsBG.png');"
						></div>

						<div class="relative mx-auto w-[1280px] px-4 py-12 2xl:w-auto print:w-auto print:min-w-auto print:px-0 print:py-0 print:pt-10">
							<div class="font-display text-center text-[#656565]">
								<h2 class="font-base text-center text-5xl">
									<Show when={pageData()?.subCollection}>
										{pageData()?.subCollection?.name}

										<Show when={pageData()?.collection}>
											{` ${pageData()?.collection?.name}`}
										</Show>
									</Show>
								</h2>

								<h5 class="mt-4 text-2xl">FOB $ Semarang Pricelist</h5>

								<CollectionGalleryHeaderNav
									collectionsRoot={{
										text: "Collections Gallery",
										slug: "collections-gallery",
									}}
									collection={pageData()?.collection ?? undefined}
									lastChildCollection={pageData()?.subCollection}
								/>
							</div>

							<CollectionGalleryPriceListTable
								category={pageData()?.subCollection!}
								products={pageData()?.products ?? []}
							/>
						</div>

						<img
							src="/images/bungaKiri.png"
							alt=""
							class="absolute top-0 right-0 z-[-1] h-4/5 opacity-30 lg:hidden"
						/>

						<img
							src="/images/bungaKanan.png"
							alt=""
							class="absolute top-0 left-0 z-[-1] h-4/5 opacity-30 lg:hidden"
						/>
					</div>
				</Show>
			</Show>

			<Show when={pageData() && !pageData()?.isPriceListPage}>
				<CollectionGalleryPageHeader
					collectionsRoot={{
						text: "Collections Gallery",
						slug: "collections-gallery",
					}}
					collection={pageData()?.collection ?? undefined}
					subCollection={pageData()?.subCollection}
					searchFieldRef={searchFieldRef}
					keyword={getSearchKeyword(searchParams)}
				/>

				<Show
					when={!pageData()?.errorMessage}
					fallback={pageData()?.errorMessage}
				>
					<Show
						when={
							pageData()?.subCollection?.children &&
							pageData()?.subCollection?.children?.length
						}
					>
						<div class="pt-40 pb-8">
							<div class="relative container">
								<CollectionGalleryHeader
									collectionsRoot={{
										text: "Collections Gallery",
										slug: "collections-gallery",
									}}
									category={pageData()?.collection!}
									subCategory={pageData()?.subCollection}
								/>

								<div class="mt-12 grid w-full grid-cols-1 place-items-center gap-5 px-[1rem] sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 2xl:px-0">
									<For each={pageData()?.subCollection?.children ?? []}>
										{(subCollection, index) => (
											<CollectionGalleryGridItemSimple
												collectionName={subCollection.name}
												linkUrl={`/collections-gallery/${props.params.id}/${props.params.sub_id}/${subCollection.categoryId}/`}
											/>
										)}
									</For>
								</div>
							</div>
						</div>
					</Show>
				</Show>
			</Show>
		</>
	);
}
