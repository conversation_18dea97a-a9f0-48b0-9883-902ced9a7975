import { APIEvent } from "@solidjs/start/server";
import { URL } from "url";
import { allowedEmailsToManageJobOrder } from "~/configs/app-config";
import { getAuthData, isAuthenticated } from "~/services/http-services/auth-service";
import {
	createJobOrderProductRemark,
	fetchJobOrderProductRemarks,
	findJobOrderProductRemark,
} from "~/services/http-services/job-order-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function GET({ request, params }: APIEvent) {
	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to load job order product remarks",
			},
			401,
		);
	}

	const urlObject = new URL(request.url);
	const searchParams = urlObject.searchParams;

	const salesOrderId = searchParams.get("sales_order_id");
	const productIdsStr = searchParams.get("product_ids");
	const productId = searchParams.get("product_id");

	if (!salesOrderId) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please provide sales order id",
			},
			400,
		);
	}

	const isValidRoute = productIdsStr || productId;

	if (!isValidRoute) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please provide product ids or product id",
			},
			400,
		);
	}

	const productIds: string[] = [];

	if (productIdsStr) {
		productIds.push(...productIdsStr.split(","));
	}

	const response = productIdsStr
		? await fetchJobOrderProductRemarks({
				salesOrderId: salesOrderId,
				productIds: productIds,
			})
		: await findJobOrderProductRemark(salesOrderId, productId ?? "");

	return makeJsonResponse(response);
}

export async function POST({ request, params }: APIEvent) {
	const authDataResponse = await getAuthData();

	if (!authDataResponse.success || !authDataResponse.data) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to create job order product remarks",
			},
			401,
		);
	}

	if (!allowedEmailsToManageJobOrder.includes(authDataResponse.data.email)) {
		return makeJsonResponse(
			{
				success: false,
				message: "You are not authorized to create job order product remarks",
			},
			403,
		);
	}

	// Get POST data.
	const body = await request.json();

	// This is the team member <NAME_EMAIL> member in inFlow.
	const teamMeberId = "9e31e1dd-a3ba-4f46-87f1-e155d14f17a5";

	const salesOrderId = body.salesOrderId;
	const salesOrderLineId = body.salesOrderLineId;
	const productId = body.productId;
	const remakrs = body.remarks;

	const response = await createJobOrderProductRemark({
		id: 0,
		teamMemberId: teamMeberId,
		salesOrderId: salesOrderId,
		salesOrderLineId: salesOrderLineId,
		productId: productId,
		remarks: remakrs,
	});

	return makeJsonResponse(response);
}
