import { inflowConfig } from "~/configs/server-config";
import { PriceData, ProductData } from "~/types/dto";
import { CustomerPriceListPageDataResponse } from "~/types/response";
import { fetchProducts } from "./product-service";
import {
	dbFindDefaultPricingScheme,
	dbFindPricingSchemeBySlug,
} from "../db-services/pricing-scheme-db-service";
import { findPricingScheme } from "./pricing-scheme-service";
import {
	dbGetSalesProductStructuredCategories,
	isSalesProductSubCategory,
} from "../db-services/category-db-service";
import { toProperNumberType } from "~/utils/string-util";
import { dbFindSalesProduct } from "../db-services/product-db-service";
import { dbGetCustomerPriceListOption } from "../db-services/customer-price-list-db-service";
import { dbGetCustomerPricingScheme } from "../db-services/customer-pricing-scheme-db-service";

export async function getCustomerPriceListPageData(
	pricingSchemeSlug: string,
): Promise<CustomerPriceListPageDataResponse> {
	("use server");

	const dbPricingScheme = await dbFindPricingSchemeBySlug(pricingSchemeSlug);

	if (!dbPricingScheme) {
		return {
			success: false,
			message: "Failed to find customer pricing scheme",
		};
	}

	const customerPricingScheme = await dbGetCustomerPricingScheme(
		dbPricingScheme.pricingSchemeId,
	);

	if (!customerPricingScheme) {
		return {
			success: false,
			message: "Failed to find customer pricing scheme",
		};
	}

	const pricingSchemeId = dbPricingScheme.pricingSchemeId;

	const pricingSchemeResponse = await findPricingScheme(
		pricingSchemeId,
		"productPrices,productPrices.product",
	);

	if (!pricingSchemeResponse.success || !pricingSchemeResponse.data) {
		return {
			success: false,
			message: pricingSchemeResponse.message,
		};
	}

	const pricingScheme = pricingSchemeResponse.data;

	if (!pricingScheme.productPrices) {
		return {
			success: false,
			message: "Product prices is null",
		};
	}

	/**
	 * If success, the `productPrices` property of `$fobPricingScheme` here is already null.
	 * No need to setting it to null manually.
	 */
	const fobPricingScheme = await dbFindDefaultPricingScheme();

	if (!fobPricingScheme) {
		return {
			success: false,
			message: "Default pricing scheme (FOB $ Semarang) not found.",
		};
	}

	const productPrices = pricingScheme.productPrices;

	// Set the `productPrices` property to null to reduce response size.
	pricingScheme.productPrices = null;

	const salesProductCategory = await dbGetSalesProductStructuredCategories();

	if (!salesProductCategory) {
		return {
			success: false,
			message: "Failed to find sales product category",
		};
	}

	const prices: PriceData[] = [];

	const skuList: string[] = [];

	for (const productPrice of productPrices) {
		const product = productPrice.product;

		if (!product) continue;

		const categoryId = product.categoryId;

		const isExpectedSubCategory = await isSalesProductSubCategory(
			categoryId,
			salesProductCategory,
		);

		if (!isExpectedSubCategory) continue;

		if (!product.isActive) continue;

		if (!productPrice.unitPrice) continue;

		const floatUnitPrice = toProperNumberType(productPrice.unitPrice);

		if (floatUnitPrice <= 0) continue;

		const customFields = product.customFields;

		if (!customFields) continue;

		const cbm = customFields.custom3;

		if (!cbm) continue;

		prices.push(productPrice);
		skuList.push(product.sku);
	}

	// Sort `$skuList` alphabetically.
	skuList.sort();

	const sortedPrices: PriceData[] = [];

	const productsFobPrice: Record<string, string> = {};

	for (const sku of skuList) {
		for (const price of prices) {
			const product = price.product;

			if (!product) continue;

			if (product.sku !== sku) continue;

			sortedPrices.push(price);

			const dbProduct = await dbFindSalesProduct(price.productId);

			if (!dbProduct) continue;

			const dbProductPrices: PriceData[] | null =
				"prices" in dbProduct && Array.isArray(dbProduct.prices)
					? dbProduct.prices
					: null;

			if (dbProductPrices) {
				for (const dbProductPrice of dbProductPrices) {
					if (
						dbProductPrice.pricingSchemeId ===
						inflowConfig().defaultPricingSchemeId
					) {
						productsFobPrice[dbProductPrice.productId] =
							dbProductPrice.unitPrice;

						break;
					}
				}
			}
		}
	}

	// Early cleanup, clear prices.
	prices.length = 0;

	const customerPriceListOption =
		await dbGetCustomerPriceListOption(pricingSchemeSlug);

	const issuedDate = customerPriceListOption?.issuedDate ?? "";

	return {
		success: true,
		message: "Customer price list page data fetched successfully",
		data: {
			pricingScheme: pricingScheme,
			fobPricingScheme: fobPricingScheme,
			customerPricingScheme: customerPricingScheme,
			prices: sortedPrices,
			productsFobPrice: productsFobPrice,
			issuedDate: issuedDate,
			customProductDataCollection: {},
		},
	};
}

export async function fetchCollectionProducts(props: {
	existingProducts: ProductData[];
	perPage: number;
	collectionId: string;
	lastProductId?: string;
}): Promise<ProductData[]> {
	"use server";

	let products: ProductData[] = props.existingProducts;

	const productListResponse = await fetchProducts({
		count: props.perPage,
		after: props.lastProductId,
		isActive: true,
		categoryId: props.collectionId,
	});

	if (
		productListResponse.success &&
		productListResponse.data &&
		productListResponse.data.length > 0
	) {
		products.push(...productListResponse.data);

		if (productListResponse.data.length === props.perPage) {
			const lastProduct =
				productListResponse.data[productListResponse.data.length - 1];
			const lastProductId = lastProduct.productId;

			// Recursively fetch the next page of products.
			products = await fetchCollectionProducts({
				existingProducts: products,
				perPage: props.perPage,
				collectionId: props.collectionId,
				lastProductId: lastProductId,
			});
		}
	}

	return products;
}
