import { For, batch, createSignal } from "solid-js";
import { CustomerData } from "~/types/dto";
import CustomerDataRow from "./CustomerDataRow";

export interface CustomerListProps {
	customers: CustomerData[];
	onViewFormsButtonClick?: (customer: CustomerData) => void;
}

export default function CustomerList(props: CustomerListProps) {
	const [customers, setCustomers] = createSignal(props.customers);
	const [isModalOpen, setIsModalOpen] = createSignal(false);
	const [selectedcustomer, setSelectedcustomer] = createSignal<CustomerData>();

	function openModal(customer: CustomerData) {
		batch(() => {
			setSelectedcustomer(customer);
			setIsModalOpen(true);
		});
	}

	return (
		<>
			<div class="text pt-32 text-sm">
				<For each={customers()}>
					{(customer) => (
						<CustomerDataRow customer={customer} onClick={openModal} />
					)}
				</For>
			</div>
		</>
	);
}
