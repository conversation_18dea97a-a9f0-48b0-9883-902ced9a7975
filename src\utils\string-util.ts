import slugify from "@sindresorhus/slugify";

export function toProperNumberType(char: string | number): number {
	if (!char) {
		return 0;
	}

	if (typeof char !== "string") {
		char = String(char);
	}

	const numValue = Number(char);
	const numStr = String(numValue);

	return numStr.includes(".") ? numValue : Math.trunc(numValue);
}

export function toPricingSchemeSlug(name: string): string {
	return slugify(name.trim().replace(/\s\$/g, ""));
}
