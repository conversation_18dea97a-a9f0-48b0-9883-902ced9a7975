import ky from "ky";
import { PurchaseOrderListResponse } from "~/types/response";

export interface LoadMorePurchaseOrdersProps {
	url: string;
	before?: string;
	after?: string;
	keyword?: string;
}

export async function clientFetchPurchaseOrders(
	props: LoadMorePurchaseOrdersProps,
): Promise<PurchaseOrderListResponse> {
	let apiUrl = `${props.url}?includeCount=true`;

	if (props.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props.keyword) {
		apiUrl += `&keyword=${props.keyword}`;
	}

	try {
		const response = await ky.get(apiUrl).json<PurchaseOrderListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more purchase orders",
		};
	}
}
