import ky from "ky";
import { SalesOrderListResponse } from "~/types/response";

export interface LoadMoreSalesOrdersProps {
	url: string;
	before?: string;
	after?: string;
	keyword?: string;
}

export async function clientFetchSalesOrders(
	props: LoadMoreSalesOrdersProps,
): Promise<SalesOrderListResponse> {
	let apiUrl = `${props.url}?includeCount=true`;

	if (props.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props.keyword) {
		apiUrl += `&keyword=${props.keyword}`;
	}

	try {
		const response = await ky.get(apiUrl).json<SalesOrderListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more sales orders",
		};
	}
}
