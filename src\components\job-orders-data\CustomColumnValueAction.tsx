import { Show, batch, createSignal } from "solid-js";
import { JobOrderCustomColumnData, ProfileData } from "~/types/dto";
import {
	clientCreateJobOrderCustomColumn,
	clientUpdateJobOrderCustomColumn,
} from "~/services/client-services/job-order-client-service";
import { allowedEmailsToManageJobOrder } from "~/configs/app-config";
import { Loader2, Save, SquarePen } from "lucide-solid";

export default function CustomColumnValueAction(props: {
	currentUser?: ProfileData;
	productId: string;
	customColumn: JobOrderCustomColumnData;
	afterDataSaved?: (data: JobOrderCustomColumnData) => void;
}) {
	const [editMode, setEditMode] = createSignal(false);
	const [isSaving, setIsSaving] = createSignal<boolean>(false);

	let textareaRef: HTMLTextAreaElement | undefined;

	async function handleDataSave() {
		setIsSaving(true);

		const values = props.customColumn.values;
		values[props.productId] = textareaRef?.value ?? "";

		const newCustomColumn: JobOrderCustomColumnData = {
			id: props.customColumn.id,
			salesOrderId: props.customColumn.salesOrderId,
			name: props.customColumn.name,
			values: values,
		};

		const response = newCustomColumn.id
			? await clientUpdateJobOrderCustomColumn(newCustomColumn)
			: await clientCreateJobOrderCustomColumn(newCustomColumn);

		batch(() => {
			setEditMode(false);
			setIsSaving(false);

			if (response.success) {
				if (response.data) {
					props.afterDataSaved?.(response.data);
				}
			} else {
				alert(response.message);
			}
		});
	}

	function openEditMode() {
		setEditMode(true);
	}

	function closeEditMode() {
		setEditMode(false);
	}

	function canEditData() {
		return (
			props.currentUser &&
			props.currentUser?.email &&
			allowedEmailsToManageJobOrder.includes(props.currentUser?.email)
		);
	}

	return (
		<Show
			when={editMode()}
			fallback={
				<div class="relative flex h-full w-full items-center justify-between px-2">
					<div class="text-left">
						{props.customColumn.values[props.productId] ?? ""}
					</div>

					<Show when={canEditData()}>
						<button
							class="hover:bg-primary absolute top-0 right-0 rounded-full bg-gray-200 p-2 hover:text-white print:hidden"
							onClick={openEditMode}
						>
							<SquarePen size={13} />
						</button>
					</Show>
				</div>
			}
		>
			<div class="relative h-full w-full">
				<textarea
					ref={textareaRef}
					class="relative block w-full resize-none rounded-sm border-2 border-gray-200 p-2 print:hidden"
					rows={4}
					value={props.customColumn.values[props.productId] ?? ""}
				/>
				<div class="mt-2 flex flex-wrap items-center justify-between">
					<button
						type="button"
						class="relative inline-flex w-[49%] items-center justify-center rounded-sm bg-gray-200 p-2 text-gray-600 print:hidden"
						onClick={closeEditMode}
					>
						Cancel
					</button>

					<Show
						when={isSaving()}
						fallback={
							<button
								type="button"
								class="bg-primary relative inline-flex w-[49%] items-center justify-center rounded-sm p-2 text-white print:hidden"
								onClick={handleDataSave}
							>
								<Save class="mr-1.5" size={16} />
								Save
							</button>
						}
					>
						<div class="bg-primary relative flex w-[49%] items-center justify-center rounded-sm p-2 text-white print:hidden">
							<Loader2 class="h-4 w-4 animate-spin" />
						</div>
					</Show>
				</div>
			</div>
		</Show>
	);
}
