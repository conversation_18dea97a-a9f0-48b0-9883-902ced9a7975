import ky from "ky";
import { inflowConfig, inflowHttpHeader } from "../../configs/server-config";
import { PricingSchemeData } from "~/types/dto";
import { PriceListResponse, PriceResponse } from "~/types/response";
import { getErrorMessage } from "~/utils/http-util";
import { toPricingSchemeData } from "~/utils/dto-util";

export async function fetchPricingSchemes(props?: {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	keyword?: string;
}): Promise<PriceListResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? inflowConfig().loadMorePerPage;
	const include = props?.include ?? "currency";

	let apiUrl = `${baseApiUrl}/pricing-schemes?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc !== undefined) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.keyword) {
		apiUrl += `&filter[smart]=${props.keyword}`;
	}

	try {
		const rawPricingSchemes = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json();

		const pricingSchemes: PricingSchemeData[] = [];

		if (Array.isArray(rawPricingSchemes)) {
			for (const rawPricingScheme of rawPricingSchemes) {
				const pricingSchemeData = toPricingSchemeData(rawPricingScheme);
				if (pricingSchemeData) pricingSchemes.push(pricingSchemeData);
			}
		}

		return {
			success: true,
			message: `${pricingSchemes.length} pricing schemes fetched successfully`,
			data: pricingSchemes,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findPricingScheme(
	id: string,
	include?: string,
): Promise<PriceResponse> {
	"use server";

	if (!id) {
		return {
			success: false,
			message: "Please provide pricing scheme id",
		};
	}
	``;
	const baseApiUrl = inflowConfig().baseInflowApiUrl;
	const apiUrl = `${baseApiUrl}/pricing-schemes/${id}?include=${include ?? "currency"}`;

	try {
		const rawPricingScheme = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json();

		return {
			success: true,
			message: "Pricing scheme retrieved successfully",
			data: toPricingSchemeData(rawPricingScheme),
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
