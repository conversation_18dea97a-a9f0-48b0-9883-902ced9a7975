import Dashboard from "~/components/Dashboard";
import { RouteDefinition, createAsync, query, redirect } from "@solidjs/router";
import { getAuthData } from "~/services/http-services/auth-service";

const getPageData = query(async () => {
	"use server";

	const authDataResponse = await getAuthData();

	if (!authDataResponse.success || !authDataResponse.data) {
		throw redirect("/login/");
	}

	return authDataResponse.data;
}, "authData");

export const route = {
	preload: () => getPageData(),
} satisfies RouteDefinition;

export default function HomePage() {
	const authData = createAsync(() => getPageData(), { deferStream: true });

	return <Dashboard profile={authData()} />;
}
