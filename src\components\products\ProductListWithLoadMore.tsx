import { For, Show, batch, createSignal } from "solid-js";
import { ProductData } from "~/types/dto";
import { ProductListResponse } from "~/types/response";
import { clientFetchProducts } from "~/services/client-services/product-client-service";
import LoadMore from "~/components/data-loading/LoadMore";
import LoadingSpinner from "~/components/LoadingSpinner";
import SearchBehavior from "../data-loading/SearchBehavior";
import ProductDataRow from "./ProductDataRow";
import { useSearchParams } from "@solidjs/router";
import { getSearchKeyword } from "~/utils/search-util";

export interface ProductListWithLoadMoreProps {
	baseApiUrl: string;
	product: ProductData[];
	searchField?: HTMLInputElement;
}

export default function ProductListWithLoadMore(
	props: ProductListWithLoadMoreProps,
) {
	const [searchParams, setSearchParams] = useSearchParams();
	const [product, setProducts] = createSignal(props.product);

	const [doingLoadMore, setDoingLoadMore] = createSignal<boolean>(false);
	const [loadMoreFinished, setLoadMoreFinished] = createSignal<boolean>(false);

	async function handleLoadMore() {
		if (doingLoadMore() || loadMoreFinished()) return;

		batch(() => {
			setDoingLoadMore(true);
		});

		const response = await fetchDataList(getSearchKeyword(searchParams));

		handleFetchComplete(response);
	}

	async function fetchDataList(keyword?: string): Promise<ProductListResponse> {
		const lastProductItem = product().length
			? product()[product().length - 1]
			: undefined;

		return await clientFetchProducts({
			url: `${props.baseApiUrl}/products`,
			after: lastProductItem?.productId ?? undefined,
			keyword: keyword,
		});
	}

	async function handleSearch(keyword: string) {
		batch(() => {
			setLoadMoreFinished(false);
			setDoingLoadMore(true);
			setSearchParams({ keyword: keyword });
			setProducts([]);
		});

		const response = await fetchDataList(keyword);

		handleFetchComplete(response);
	}

	function handleFetchComplete(response: ProductListResponse): void {
		batch(() => {
			setDoingLoadMore(false);

			if (!response.success) {
				alert(response.message);
				setLoadMoreFinished(true);
				return;
			}

			if (!response?.data?.length) {
				setLoadMoreFinished(true);
				return;
			}

			setProducts(product().concat(response.data));
		});
	}

	let sectionRef: HTMLElement | undefined;

	return (
		<>
			<section class="text pt-32 text-sm" ref={sectionRef}>
				<SearchBehavior
					searchField={props.searchField}
					onSearch={handleSearch}
				/>
				<LoadMore contentRef={sectionRef} onLoadMore={handleLoadMore}>
					<For each={product()}>
						{(customer) => <ProductDataRow product={customer} />}
					</For>
				</LoadMore>

				<Show when={doingLoadMore()}>
					<LoadingSpinner class="mt-14" />
				</Show>
			</section>
		</>
	);
}
