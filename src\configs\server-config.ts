import "dotenv/config";

export function dbConfig() {
	"use server";

	return {
		host: process.env.DB_HOST ?? "127.0.0.1",
		port: process.env.DB_PORT ? Number(process.env.DB_PORT) : 3306,
		user: process.env.DB_USER ?? "",
		password: process.env.DB_PASSWORD ?? "",
		database: process.env.DB_NAME ?? "",
	};
}

export function inflowConfig() {
	"use server";

	const inflowUrl = "https://cloudapi.inflowinventory.com";
	const companyId = process.env.INFLOW_COMPANY_ID ?? "";
	const baseInflowApiUrl = `${inflowUrl}/${companyId}`;

	return {
		inflowUrl: inflowUrl,
		baseInflowApiUrl: baseInflowApiUrl,
		defaultPricingSchemeId: process.env.INFLOW_DEFAULT_PRICING_SCHEME_ID ?? "",
		defaultInvoicePaymentTermsId:
			process.env.INFLOW_DEFAULT_INVOICE_PAYMENT_TERMS_ID ?? "",
		inventoryCategoryId: process.env.INFLOW_INVENTORY_CATEGORY_ID ?? "",
		salesProductCategoryId: process.env.INFLOW_SALES_PRODUCT_CATEGORY_ID ?? "",
		incomeCategoryId: process.env.INFLOW_INCOME_CATEGORY_ID ?? "",
		loadMorePerPage: 30,
	};
}

export function jsonHttpHeader() {
	"use server";

	return {
		"Content-Type": "application/json",
	};
}

export function inflowHttpHeader() {
	"use server";

	const apiKey = process.env.INFLOW_API_KEY ?? "";

	return {
		"Content-Type": "application/json",
		Accept: "application/json;version=2024-03-12",
		Authorization: `Bearer ${apiKey}`,
	};
}

export function cookieConfig() {
	"use server";

	return {
		cookieName: process.env.COOKIE_NAME ?? "multay-token",
	};
}

export function sessionConfig() {
	"use server";

	return {
		sessionSecret:
			process.env.SESSION_SECRET ??
			"lkpszMBA8wNJXoRFpj6G6BBupVREDb7dAzdP8rAyvV8m6Bo3nhDyehtfxdnn0nXn",
	};
}

export function supabaseConfig() {
	"use server";

	return {
		projectUrl: process.env.SUPABASE_PROJECT_URL ?? "",
		publicKey: process.env.SUPABASE_PROJECT_PUBLIC_KEY ?? "",
		secretKey: process.env.SUPABASE_PROJECT_SECRET_KEY ?? "",
		table: {
			jobOrderProductRemarks: "job_order_product_remarks",
			jobOrderCustomColumns: "job_order_custom_column",
			productAttributes: "product_attributes",
			productCollections: "product_collections",
			productCollectionRelationships: "product_collection_relationships",
			productCustomData: "product_custom_data",
			productMeta: "product_meta",
		},
	};
}

export function mailjetConfig() {
	"use server";

	return {
		publicApiKey: process.env.MAILJET_APIKEY_PUBLIC,
		secretApiKey: process.env.MAILJET_APIKEY_PRIVATE,
	};
}

export function capabilitiesRolesRelationships() {
	"use server";

	return {
		// User operations.
		createUser: ["administrator", "team-member"],
		editUser: ["administrator", "team-member"],
		deleteUser: ["administrator", "team-member"],

		// Customer support ticket operations.
		createSupportTicket: ["administrator", "team-member", "customer"],
		editSupportTicket: ["administrator", "team-member"],
		deleteSupportTicket: ["administrator", "team-member"],
	};
}
