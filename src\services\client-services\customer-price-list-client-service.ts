import ky from "ky";
import { SetupDataResult } from "~/types/response";
import { getErrorMessage } from "~/utils/http-util";

export async function clientSyncPriceListProducts(props: {
	after?: string;
}): Promise<SetupDataResult> {
	const apiUrl = `/api/products-sync`;

	const jsonParams: Record<string, any> = {};

	if (props.after) {
		jsonParams.after = props.after;
	}

	try {
		const response = await ky
			.post(apiUrl, {
				json: Object.keys(jsonParams).length > 0 ? jsonParams : undefined,
			})
			.json<SetupDataResult>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
			meta: {
				args: {
					after: props.after,
				},
			},
		};
	}
}
