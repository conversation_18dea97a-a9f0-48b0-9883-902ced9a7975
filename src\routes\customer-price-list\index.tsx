import {
	A,
	RouteDefinition,
	createAsync,
	query,
	redirect,
	useSearchParams,
} from "@solidjs/router";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { getRequestURL } from "vinxi/http";
import { AppUrlData } from "~/types/misc";
import { Search } from "lucide-solid";
import { For, Show } from "solid-js";
import LoadingSpinner from "~/components/LoadingSpinner";
import ErrorMessage from "~/components/error/ErrorMessage";
import ProductsSyncActionButtons from "~/components/collections/ProductsSyncActionButtons";
import { dbGetCustomerPricingSchemes } from "~/services/db-services/customer-pricing-scheme-db-service";

const getPageData = query(async () => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const urlObject = getRequestURL();
	const baseUrl = urlObject.origin;

	const urls: AppUrlData = {
		baseUrl: baseUrl,
		baseApiUrl: `${baseUrl}/api`,
		currentUrl: urlObject.href,
	};

	const customerPricingSchemes = await dbGetCustomerPricingSchemes();

	return {
		urls,
		customerPricingSchemes,
	};
}, "customerPriceListIndexPageData");

export const route = {
	preload: () => getPageData(),
} satisfies RouteDefinition;

export default function CustomerPriceListIndex() {
	const pageData = createAsync(() => getPageData(), { deferStream: true });
	let searchFieldRef: HTMLInputElement | undefined;

	const [searchParams, setSearchParams] = useSearchParams();

	return (
		<>
			<header class="fixed z-1 w-full bg-white px-[1rem] pt-[1rem] pb-[1rem] shadow-md 2xl:px-0">
				<div class="md:container md:flex">
					<div>
						<h4 class="text-primary text-2xl font-bold">
							Customers Price List
						</h4>
						<nav class="mt-1 mb-1 flex items-center" aria-label="Breadcrumb">
							<ul class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
								<li class="inline-flex items-center">
									<A
										href="/"
										class="hover:text-primary inline-flex items-center text-sm text-gray-700 dark:text-gray-400 dark:hover:text-white"
									>
										Dashboard
									</A>
								</li>
								<li>
									<div class="flex items-center">
										<svg
											class="h-2.5 w-2.5 text-gray-400 rtl:rotate-180"
											aria-hidden="true"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 6 10"
										>
											<path
												stroke="currentColor"
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="m1 9 4-4-4-4"
											/>
										</svg>
										<span class="ms-1 text-sm text-gray-700 md:ms-2 dark:text-gray-400 dark:hover:text-white">
											Customers price list
										</span>
									</div>
								</li>
							</ul>
						</nav>
					</div>

					<div class="my-auto ml-auto md:w-1/3">
						<div class="flex items-center rounded-md bg-gray-200 p-2">
							<Search class="text-gray-500" size={16} />
							<input
								ref={searchFieldRef}
								type="search"
								value={searchParams.keyword ?? ""}
								class="ml-2 w-full bg-transparent text-sm font-light outline-hidden"
								placeholder="Search customers price list..."
							/>
						</div>
					</div>
				</div>
			</header>

			<div class="container pt-36">
				<Show when={!pageData()}>
					<div class="relative flex h-full w-full items-center justify-center">
						<LoadingSpinner class="mt-14" />
					</div>
				</Show>

				<Show when={pageData()}>
					<Show
						when={pageData()?.customerPricingSchemes}
						fallback={
							<ErrorMessage
								title="Ooops!"
								message={
									"Failed to load customers price lists, please try again later."
								}
								useButtons={{
									backButton: true,
									reloadButton: true,
								}}
							/>
						}
					>
						<div class="sm:grid sm:grid-cols-2 sm:gap-3 md:grid-cols-3">
							<For each={pageData()?.customerPricingSchemes}>
								{(customerPricingScheme) => {
									return (
										<A
											href={`/customer-price-list/${customerPricingScheme.pricingSchemeSlug}`}
											class="mx-3 my-2 block rounded-lg bg-[#F3FBF6] p-4 text-sm font-semibold transition-all duration-300 ease-in-out hover:bg-green-100"
										>
											{customerPricingScheme.customerName}
										</A>
									);
								}}
							</For>
						</div>
					</Show>
				</Show>
			</div>

			<footer class="site-footer pt-12"></footer>
			<ProductsSyncActionButtons title="Sync FOB products" />
		</>
	);
}
