import {
	A,
	RouteDefinition,
	RouteSectionProps,
	action,
	createAsync,
	query,
	redirect,
	useSubmission,
} from "@solidjs/router";
import { ChevronRight } from "lucide-solid";
import { Show } from "solid-js";
import { getRequestURL } from "vinxi/http";
import LoadingSpinner from "~/components/LoadingSpinner";
import { getAuthData } from "~/services/http-services/auth-service";
import { findUser, updateUser } from "~/services/http-services/user-service";
import { canEditUser } from "~/utils/capability-util";
import { getAppUrl } from "~/utils/url-util";
import { makeUserUpdateData } from "~/utils/user-util";

const updateUserAction = action(async (formData: FormData) => {
	"use server";

	const result = await updateUser(makeUserUpdateData(formData));

	if (!result.success) {
		return result.message;
	}

	throw redirect("/users/");
});

const getPageData = query(async (userId: string | undefined) => {
	"use server";

	const authDataResponse = await getAuthData();

	if (!authDataResponse.data) {
		throw redirect("/login/");
	}

	if (!canEditUser(authDataResponse.data.role)) {
		throw redirect("/login/");
	}

	const profileResponse = await findUser(userId ?? "");

	return {
		urls: getAppUrl(getRequestURL()),
		profileResponse,
	};
}, "userEditPageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function EditUserPage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	const saving = useSubmission(updateUserAction);

	return (
		<>
			<header class="fixed z-10 w-full bg-white py-2 shadow-[rgba(50,50,105,0.15)_0px_2px_5px_0px,rgba(0,0,0,0.05)_0px_1px_1px_0px]">
				<div class="md:container">
					<nav class="flex items-center" aria-label="Breadcrumb">
						<ul class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
							<li class="inline-flex items-center">
								<A
									href="/"
									class="hover:text-secondary inline-flex items-center text-sm text-gray-700 dark:text-gray-400 dark:hover:text-white"
								>
									Dashboard
								</A>
							</li>

							<li class="ml-1">
								<ChevronRight class="h-3 w-3" />
							</li>

							<li class="ml-1 inline-flex items-center">
								<A
									href="/users/"
									class="hover:text-secondary inline-flex items-center text-sm text-gray-700 dark:text-gray-400 dark:hover:text-white"
								>
									Users
								</A>
							</li>

							<li class="ml-1">
								<ChevronRight class="h-3 w-3" />
							</li>

							<li class="ml-1 inline-flex items-center">
								<span class="text-sm text-gray-500 dark:text-gray-600 dark:hover:text-white">
									<Show
										when={
											pageData()?.profileResponse.data?.firstName &&
											pageData()?.profileResponse.data?.lastName
										}
										fallback="Edit user"
									>
										{pageData()?.profileResponse?.data?.firstName}{" "}
										{pageData()?.profileResponse?.data?.lastName}
									</Show>
								</span>
							</li>
						</ul>
					</nav>
				</div>
			</header>

			<div class="relative top-0 left-0 flex h-screen w-full items-center justify-center px-2 py-2">
				<div class="relative mx-auto w-full max-w-3xl rounded-lg px-6 py-6 shadow-[rgba(50,50,105,0.15)_0px_2px_5px_0px,rgba(0,0,0,0.05)_0px_1px_1px_0px]">
					<Show when={pageData()} fallback="Loading data...">
						<Show
							when={
								pageData()?.profileResponse && pageData()?.profileResponse.data
							}
							fallback={pageData()?.profileResponse?.message}
						>
							<form action={updateUserAction} method="post">
								<h3 class="mb-4 text-xl font-semibold text-gray-900">
									Edit user
								</h3>

								<input
									type="hidden"
									name="id"
									value={pageData()?.profileResponse?.data?.id}
								/>

								<div class="mb-6 grid grid-cols-2 gap-6">
									<div class="">
										<label
											for="email-field"
											class="block text-sm font-medium text-gray-700"
										>
											Email
										</label>
										<input
											type="email"
											name="email"
											id="email-field"
											value={pageData()?.profileResponse?.data?.email}
											placeholder="Enter email"
											class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
										/>
									</div>

									<div class="">
										<label
											for="phone-field"
											class="block text-sm font-medium text-gray-700"
										>
											Phone
										</label>
										<input
											type="text"
											name="phone"
											id="phone-field"
											value={pageData()?.profileResponse?.data?.phone ?? ""}
											placeholder="Enter phone number"
											class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
										/>
									</div>
								</div>

								<div class="mb-6 grid grid-cols-2 gap-6">
									<div class="">
										<label
											for="first_name-field"
											class="block text-sm font-medium text-gray-700"
										>
											First name
										</label>
										<input
											type="text"
											name="first_name"
											id="first_name-field"
											value={pageData()?.profileResponse?.data?.firstName}
											placeholder="Enter first name"
											class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
										/>
									</div>

									<div class="">
										<label
											for="last_name-field"
											class="block text-sm font-medium text-gray-700"
										>
											Last name
										</label>
										<input
											type="text"
											name="last_name"
											id="last_name-field"
											value={pageData()?.profileResponse?.data?.lastName}
											placeholder="Enter last name"
											class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
										/>
									</div>
								</div>

								<div class="mb-6 grid grid-cols-2 gap-6">
									<div class="">
										<label
											for="role-field"
											class="block text-sm font-medium text-gray-700"
										>
											Role
										</label>
										<select
											name="role"
											id="role-field"
											class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
										>
											<option value="">Select role</option>
											<option value="customer">Customer</option>
											<option value="team-member">Team member</option>
											<option value="administrator">Administrator</option>
										</select>
									</div>
								</div>

								<Show when={saving.result}>
									<div class="border-w mb-6 rounded-md border-2 border-red-500 bg-red-50 px-3 py-2 text-sm font-semibold text-red-500">
										{saving.result}
									</div>
								</Show>

								<div class="flex items-center justify-end">
									<A
										href="/users/"
										class="inline-block text-sm font-medium text-gray-700"
									>
										Cancel
									</A>

									<button
										type="submit"
										class={`sm:py-2s relative ml-4 inline-block rounded-md border bg-blue-500 px-3 py-2 text-center text-sm font-medium text-white hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-hidden ${
											saving.pending
												? "pointer-events-none cursor-not-allowed"
												: ""
										}`}
										disabled={saving.pending}
									>
										<span class={saving.pending ? "opacity-0" : ""}>
											Update
										</span>

										<Show when={saving.pending}>
											<LoadingSpinner
												class="absolute top-0 left-0 inline-flex h-full w-full items-center justify-center"
												colorClass="text-white"
												iconClass="h-4 w-4"
											/>
										</Show>
									</button>
								</div>
							</form>
						</Show>
					</Show>
				</div>
			</div>
		</>
	);
}
