import { A } from "@solidjs/router";
import { Bookmark, ShoppingBag, ShoppingCart, User } from "lucide-solid";

export default function Sidebar() {
	return (
		<nav class="sidebar relative h-screen w-1/5">
			<div class="absolute h-full border-r py-4 pl-14 pr-4">
				<div class="mb-12 mt-6 pr-6">
					<A href="/">
						<img
							src="/images/multay-logo.png"
							alt="Multay Furniture Logo"
							class=""
						/>
					</A>
				</div>

				<ul class="mt-10">
					<li class="mb-8">
						<A href="/sales-orders" class="hover:text-blue flex font-medium">
							<ShoppingCart class="my-auto" />
							<span class="text-graydark hover:text-blue pl-4">
								Sales Orders
							</span>
						</A>
					</li>

					<li class="mb-8">
						<A href="/customers" class="hover:text-blue flex font-medium">
							<User class="my-auto" />
							<span class="text-graydark hover:text-blue pl-4">Customers</span>
						</A>
					</li>

					<li class="mb-8">
						<A href="/categories" class="hover:text-blue flex font-medium">
							<Bookmark class="my-auto" />
							<span class="text-graydark hover:text-blue pl-4">Categories</span>
						</A>
					</li>

					<li class="mb-8">
						<A href="/products" class="hover:text-blue flex font-medium">
							<ShoppingBag class="my-auto" />
							<span class="text-graydark hover:text-blue pl-4">Products</span>
						</A>
					</li>
				</ul>
			</div>
		</nav>
	);
}
