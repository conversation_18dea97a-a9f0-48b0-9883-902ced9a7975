import { db } from "~/db";
import { customersTable } from "~/db/schema";
import { eq, inArray, or, type SQL } from "drizzle-orm";
import { CustomerData } from "~/types/dto";
import { SetupDataResult } from "~/types/response";
import { fetchCustomers } from "../http-services/customer-service";
import { toCustomerData } from "~/utils/dto-util";

export async function dbFindCustomer(customerId: string) {
	"use server";

	const results = await db
		.select()
		.from(customersTable)
		.where(eq(customersTable.customerId, customerId))
		.limit(1);

	if (!Array.isArray(results) || results.length === 0) {
		return null;
	}

	const customer = toCustomerData(results[0]);

	return customer ?? null;
}

export async function dbFindCustomerByPricingSchemeId(pricingSchemeId: string) {
	"use server";

	const results = await db
		.select()
		.from(customersTable)
		.where(eq(customersTable.pricingSchemeId, pricingSchemeId))
		.limit(1);

	if (!Array.isArray(results) || results.length === 0) {
		return null;
	}

	const customer = toCustomerData(results[0]);

	return customer ?? null;
}

export async function dbFetchCustomersByPricingSchemeIds(
	pricingSchemeIds: string[],
) {
	"use server";

	const results = await db
		.select()
		.from(customersTable)
		.where(inArray(customersTable.pricingSchemeId, pricingSchemeIds));

	// Process results
	const customerList: CustomerData[] = [];

	for (const result of results) {
		const customer = toCustomerData(result);
		if (customer) customerList.push(customer);
	}

	return customerList;
}

export async function dbFetchCustomers(props?: { customerIds?: string[] }) {
	"use server";

	// Build query conditions dynamically
	const conditions: SQL[] = [];

	if (props?.customerIds?.length) {
		conditions.push(inArray(customersTable.customerId, props.customerIds));
	}

	// Execute query with combined conditions using OR
	const results =
		conditions.length > 0
			? await db
					.select()
					.from(customersTable)
					.where(or(...conditions))
			: await db.select().from(customersTable);

	// Process results
	const customerList: CustomerData[] = [];

	for (const result of results) {
		const customer = toCustomerData(result);
		if (customer) customerList.push(customer);
	}

	return customerList;
}

/**
 * Insert a customer to the database.
 */
export async function dbInsertCustomer(customer: CustomerData) {
	"use server";

	const [header] = await db.insert(customersTable).values(customer);

	return header.affectedRows > 0 ? customer.customerId : false;
}

/**
 * Update a customer in the database.
 */
export async function dbUpdateCustomer(
	customerId: string,
	data: Partial<Omit<CustomerData, "id" | "customerId">>,
) {
	"use server";

	const [header] = await db
		.update(customersTable)
		.set(data)
		.where(eq(customersTable.customerId, customerId));

	return header.affectedRows > 0 ? customerId : false;
}

/**
 * Insert customers to the database.
 */
export async function dbInsertCustomers(customers: CustomerData[]) {
	"use server";

	const [header] = await db.insert(customersTable).values(customers);

	return header.affectedRows > 0 ? true : false;
}

/**
 * Update customers in the database.
 */
export async function dbUpdateCustomers(
	list: {
		customerId: string;
		data: Partial<Omit<CustomerData, "id" | "customerId">>;
	}[],
) {
	"use server";

	const results = await Promise.all(
		list.map(async (item) => {
			const [header] = await db
				.update(customersTable)
				.set(item.data)
				.where(eq(customersTable.customerId, item.customerId));

			return {
				customerId: item.customerId,
				status: header.affectedRows > 0,
			};
		}),
	);

	return results.some((result) => result.status);
}

export async function dbCollectCustomers(props?: {
	after?: string;
}): Promise<SetupDataResult> {
	"use server";

	const count = 100;

	const remoteResponse = await fetchCustomers({
		count: count,
		include:
			"addresses,balances,defaultBillingAddress,defaultShippingAddress,defaultLocation,defaultPaymentTerms,defaultSalesRepTeamMember,pricingScheme",
		after: props?.after,
	});

	if (!remoteResponse.success || !remoteResponse.data) {
		return {
			success: false,
			message: remoteResponse.message,
			meta: {
				args: {
					after: props?.after,
				},
				errorCode: "remote_fetch_error",
			},
		};
	}

	if (remoteResponse.data.length > 0) {
		const customerIds = remoteResponse.data.map(
			(customer) => customer.customerId,
		);

		const existingRecords = await dbFetchCustomers({
			customerIds: customerIds,
		});
		const existingRecordIds = existingRecords.map(
			(existingRecord) => existingRecord.customerId,
		);

		const newInsertList = remoteResponse.data.filter(
			(customer) => !existingRecordIds.includes(customer.customerId),
		);
		const newUpdateList = remoteResponse.data.filter((customer) =>
			existingRecordIds.includes(customer.customerId),
		);

		let anySuccess = false;

		if (newInsertList.length) {
			const [header] = await db.insert(customersTable).values(newInsertList);

			if (header.affectedRows) anySuccess = true;
		}

		if (newUpdateList.length) {
			const updateResult = await dbUpdateCustomers(
				newUpdateList.map((customer) => {
					// The `customerProps` here should be `CustomerData` but without `id` and `customerId` properties.
					const customerProps: Record<string, any> = { ...customer };

					// Now remove `id` (if exists) and `customerId` from `customerProps` object.
					if ("id" in customerProps) delete customerProps.id;
					delete customerProps.customerId;

					return {
						customerId: customer.customerId,
						data: customerProps,
					};
				}),
			);

			if (updateResult) anySuccess = true;
		}

		if (!anySuccess) {
			return {
				success: false,
				message: "Failed to insert or update customers",
				meta: {
					args: {
						after: props?.after,
					},
					hasMore: remoteResponse.data.length < count ? false : true,
					errorCode: "db_insert_error",
					lastId:
						remoteResponse.data[remoteResponse.data.length - 1].customerId,
				},
			};
		}
	}

	const isFinished = remoteResponse.data.length < count;

	return {
		success: true,
		message: isFinished
			? "Finished collecting customers"
			: "Customers collected successfully",
		meta: {
			args: {
				after: props?.after,
			},
			hasMore: isFinished ? false : true,
			lastId: remoteResponse.data.length
				? remoteResponse.data[remoteResponse.data.length - 1].customerId
				: undefined,
		},
	};
}
