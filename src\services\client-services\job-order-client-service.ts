import ky from "ky";
import {
	JobOrderCustomColumnData,
	JobOrderProductImagesData,
	JobOrderProductRemarksData,
} from "~/types/dto";
import {
	DeleteCustomProductDataResponse,
	JobOrderCustomColumnListResponse,
	JobOrderCustomColumnResponse,
	JobOrderProductImageResponse,
	JobOrderProductImagesListResponse,
	JobOrderProductRemarkListResponse,
	JobOrderProductRemarkResponse,
} from "~/types/response";
import { BaseResponse } from "~/types/response";

export async function clientFetchJobOrderProductRemarks(props: {
	url: string;
	salesOrderId?: string;
	productIds?: string[];
}): Promise<JobOrderProductRemarkListResponse> {
	let apiUrl = `${props.url}?sales_order_id=${props.salesOrderId}`;

	if (props.productIds) {
		const productIdsStr = props.productIds.join(",");
		apiUrl += `&product_ids=${productIdsStr}`;
	}

	try {
		const response = await ky
			.get(apiUrl)
			.json<JobOrderProductRemarkListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more job order product remarks.",
		};
	}
}

export async function clientFindJobOrderProductRemark(props: {
	url: string;
	salesOrderId: string;
	productId: string;
}): Promise<JobOrderProductRemarkResponse> {
	let apiUrl = `${props.url}/${props.salesOrderId}`;

	if (props.productId) {
		apiUrl += `&product_id=${props.productId}`;
	}

	try {
		const response = await ky.get(apiUrl).json<JobOrderProductRemarkResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to find job order product remark.",
		};
	}
}

export async function clientFindJobOrderProductRemarkById(props: {
	url: string;
	id: number;
}): Promise<JobOrderProductRemarkResponse> {
	let apiUrl = `${props.url}/${props.id}`;

	try {
		const response = await ky.get(apiUrl).json<JobOrderProductRemarkResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to find job order product remark.",
		};
	}
}

export async function clientCreateJobOrderProductRemark(props: {
	url: string;
	data: JobOrderProductRemarksData;
}): Promise<JobOrderProductRemarkResponse> {
	const apiUrl = props.url;

	try {
		const response = await ky
			.post(apiUrl, {
				json: props.data,
			})
			.json<JobOrderProductRemarkResponse>();
		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to create job order product remark.",
		};
	}
}

export async function clientUpdateJobOrderProductRemark(props: {
	url: string;
	data: JobOrderProductRemarksData;
}): Promise<JobOrderProductRemarkResponse> {
	const apiUrl = `${props.url}/${props.data.id}`;

	try {
		const response = await ky
			.put(apiUrl, {
				json: props.data,
			})
			.json<JobOrderProductRemarkResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to Update job order product remark.",
		};
	}
}

export async function clientDeleteJobOrderProductRemark(props: {
	url: string;
	id: number;
}): Promise<DeleteCustomProductDataResponse> {
	const apiUrl = `${props.url}/${props.id}`;

	try {
		const response = await ky
			.delete(apiUrl)
			.json<DeleteCustomProductDataResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to delete job order product remark.",
		};
	}
}

export async function clientFetchJobOrderProductImages(props: {
	url: string;
	salesOrderId?: string;
	productIds?: string[];
}): Promise<JobOrderProductImagesListResponse> {
	let apiUrl = `${props.url}?sales_order_id=${props.salesOrderId}`;

	if (props.productIds) {
		const productIdsStr = props.productIds.join(",");
		apiUrl += `&product_ids=${productIdsStr}`;
	}

	try {
		const response = await ky
			.get(apiUrl)
			.json<JobOrderProductImagesListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more job order product images.",
		};
	}
}

export async function clientFindJobOrderProductImages(props: {
	url: string;
	salesOrderId: string;
	productId: string;
}): Promise<JobOrderProductImageResponse> {
	let apiUrl = `${props.url}/${props.salesOrderId}`;

	if (props.productId) {
		apiUrl += `&product_id=${props.productId}`;
	}

	try {
		const response = await ky.get(apiUrl).json<JobOrderProductImageResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to find job order product image.",
		};
	}
}

export async function clientFindJobOrderProductImageById(props: {
	url: string;
	id: number;
}): Promise<JobOrderProductImageResponse> {
	let apiUrl = `${props.url}/${props.id}`;

	try {
		const response = await ky.get(apiUrl).json<JobOrderProductImageResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to find job order product image.",
		};
	}
}

export async function clientCreateJobOrderProductImage(props: {
	url: string;
	data: JobOrderProductImagesData;
}): Promise<JobOrderProductImageResponse> {
	const apiUrl = props.url;

	try {
		const response = await ky
			.post(apiUrl, {
				json: props.data,
			})
			.json<JobOrderProductImageResponse>();
		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to create job order product image.",
		};
	}
}

export async function clientUpdateJobOrderProductImage(props: {
	url: string;
	data: JobOrderProductImagesData;
}): Promise<JobOrderProductImageResponse> {
	const apiUrl = `${props.url}/${props.data.id}`;

	try {
		const response = await ky
			.put(apiUrl, {
				json: props.data,
			})
			.json<JobOrderProductImageResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to Update job order product image.",
		};
	}
}

export async function clientDeleteJobOrderProductImage(props: {
	url: string;
	id: number;
}): Promise<DeleteCustomProductDataResponse> {
	const apiUrl = `${props.url}/${props.id}`;

	try {
		const response = await ky
			.delete(apiUrl)
			.json<DeleteCustomProductDataResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to delete job order product image.",
		};
	}
}

export async function clientUploadImage(props: {
	url: string;
	file: File;
}): Promise<string> {
	const apiUrl = props.url;

	const formData = new FormData();
	formData.append("file", props.file);

	try {
		const response = await ky.post(apiUrl, {
			body: formData,
		});

		return response.text();
	} catch (e: unknown) {
		return "";
	}
}

export async function clientFetchJobOrderCustomColumns(props: {
	url: string;
	salesOrderId?: string;
}): Promise<JobOrderCustomColumnListResponse> {
	let apiUrl = `/api/job-order-custom-columns/?sales_order_id=${props.salesOrderId}`;

	try {
		const response = await ky
			.get(apiUrl)
			.json<JobOrderCustomColumnListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more job order product remarks.",
		};
	}
}

export async function clientCreateJobOrderCustomColumn(postData: {
	teamMemberId?: string;
	salesOrderId: string;
	name: string;
	values: Record<string, string>;
}): Promise<JobOrderCustomColumnResponse> {
	const apiUrl = `/api/job-order-custom-columns`;

	try {
		const response = await ky
			.post(apiUrl, {
				json: postData,
			})
			.json<JobOrderCustomColumnResponse>();
		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to create job order custom column.",
		};
	}
}

export async function clientUpdateJobOrderCustomColumn(
	data: JobOrderCustomColumnData,
): Promise<JobOrderCustomColumnResponse> {
	const apiUrl = `/api/job-order-custom-columns/${data.id}`;

	try {
		const response = await ky
			.put(apiUrl, {
				json: data,
			})
			.json<JobOrderCustomColumnResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to Update job order custom column.",
		};
	}
}

export async function clientDeleteJobOrderCustomColumn(
	id: number,
): Promise<BaseResponse> {
	const apiUrl = `/api/job-order-custom-columns/${id}`;

	try {
		const response = await ky.delete(apiUrl).json<BaseResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to delete job order custom column.",
		};
	}
}
