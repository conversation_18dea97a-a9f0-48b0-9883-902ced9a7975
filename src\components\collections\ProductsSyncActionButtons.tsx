import { createSignal } from "solid-js";
import { LucideRefreshCcw } from "lucide-solid";
import { isServer } from "solid-js/web";
import { clientSyncPriceListProducts } from "~/services/client-services/customer-price-list-client-service";

async function recursiveSyncPriceListProducts(after?: string) {
	const response = await clientSyncPriceListProducts({
		after: after,
	});

	if (!response.success) {
		if (!isServer) {
			window.alert(response.message);
		}

		return;
	}

	if (response.meta.hasMore) {
		await recursiveSyncPriceListProducts(response.meta.lastId);
	} else {
		if (!isServer) {
			window.alert("Sync completed successfully");
		}
	}
}

export default function ProductsSyncActionButtons(props: { title: string }) {
	const [doingSync, setDoingSync] = createSignal(false);

	async function handleSyncButtonClick() {
		setDoingSync(true);
		await recursiveSyncPriceListProducts();
		setDoingSync(false);
	}

	let formRef: HTMLFormElement | undefined;

	return (
		<form ref={formRef} method="post" target="_blank">
			<div class="fixed right-5 bottom-5 z-10 flex flex-col space-y-2 rounded-full bg-orange-800/75 px-3 py-4 print:hidden">
				<button
					type="button"
					class="group"
					title="Sync FOB products"
					onClick={handleSyncButtonClick}
				>
					<LucideRefreshCcw
						class={`mx-auto text-white transition-colors duration-200 ease-in-out group-hover:text-yellow-400 ${doingSync() ? "animate-spin" : ""}`}
						size={20}
					/>
				</button>
			</div>
		</form>
	);
}
