import { createSignal, For, Show } from "solid-js";
import {
	CustomerPricingSchemeData,
	CustomProductData,
	PriceData,
} from "~/types/dto";
import {
	formatCurrency,
	formatDimension,
	formatQuantity,
} from "~/utils/formatting-util";
import PriceListActionButtons from "../collections/PriceListActionButtons";
import { PenBox } from "lucide-solid";
import { action, useAction, useSubmission } from "@solidjs/router";
import { updateOption } from "~/services/db-services/option-db-service";

const updateCustomerPriceListAction = action(
	async (slug: string, issuedDate: string) => {
		"use server";

		const updatedRows = await updateOption({
			name: `customer_price_list_${slug}`,
			value: { issued_date: issuedDate },
		});

		return {
			success: updatedRows !== false,
			message:
				updatedRows === false
					? "Failed to update price list data."
					: "Price list data has been updated.",
		};
	},
);

export default function CustomerPriceListTable(props: {
	customerPricingScheme: CustomerPricingSchemeData | undefined;
	customProductDataCollection: Record<string, CustomProductData>;
	prices: PriceData[];
	productsFobPrice: Record<string, string>;
	issuedDate: string;
}) {
	const firstName =
		props.customerPricingScheme?.customerName.split(" ")[0] ?? "Customer";

	const pricingSchemeName =
		props.customerPricingScheme?.pricingSchemeName.replace(" $", "") ??
		firstName;

	const hasAnyCustomProductData =
		Object.keys(props.customProductDataCollection).length > 0;

	const thSharedClass = "p-1 font-bold text-center uppercase";
	const tdSharedClass = "p-1 print:p-0.5 xl:p-1.5";

	function getProductFobPrice(productId: string) {
		return props.productsFobPrice[productId] ?? "0.00";
	}

	const [issuedDateEditMode, setIssuedDateEditMode] = createSignal(false);
	const [issuedDate, setIssuedDate] = createSignal<string>(props.issuedDate);

	const doingSubmission = useSubmission(updateCustomerPriceListAction);
	const submitUpdate = useAction(updateCustomerPriceListAction);

	async function handleSubmission(slug: string, issuedDate: string) {
		const result = await submitUpdate(slug, issuedDate);

		if (!result.success) {
			alert(result.message);
			return;
		}

		setIssuedDateEditMode(false);
	}

	function renderIssuedDateText() {
		if (!issuedDate()) {
			return "-";
		}

		return issuedDate();
	}

	return (
		<div class="text-md mt-10 text-stone-800 print:text-[10px]">
			<table class="font-table table w-full table-fixed">
				<thead class="border-b border-stone-200 text-center uppercase">
					<tr class="w-full text-center print:text-sm">
						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[16%]" : "w-[24%]"} px-1`}
							rowSpan={2}
						>
							Legacy Home Code
						</th>
						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[18%]" : "w-[30%]"} px-1`}
							rowSpan={2}
						>
							Legacy Home Description
						</th>

						<Show when={hasAnyCustomProductData}>
							<th class={`${thSharedClass} w-[16%] px-1`} rowSpan={2}>
								{pricingSchemeName} Code
							</th>
							<th class={`${thSharedClass} w-[18%] px-1`} rowSpan={2}>
								{pricingSchemeName} Description
							</th>
						</Show>

						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[12%]" : "w-[16%]"} px-3`}
							colspan={3}
						>
							Dimensions (cm)
						</th>

						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[6%]" : "w-[8%]"}`}
							rowSpan={2}
						>
							M3
						</th>

						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[7%]" : "w-[11%]"}`}
							rowSpan={2}
						>
							Legacy Home
							<br /> FOB Price USD
							<br />
							FEB 2024
						</th>

						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[7%]" : "w-[11%]"}`}
							rowSpan={2}
						>
							{pricingSchemeName} Existing
							<br />
							Price Dated
							<Show when={!issuedDateEditMode()}>
								<div class="group relative flex items-center justify-center">
									<span>{renderIssuedDateText()}</span>
									<button
										type="button"
										class="group-hover:bg-primary absolute right-0 rounded-full bg-gray-200 px-2 py-1 transition-all duration-300 ease-in-out group-hover:text-white print:hidden"
										onClick={() => {
											setIssuedDateEditMode(true);
										}}
									>
										<PenBox class="h-3 w-3 cursor-pointer" />
									</button>
								</div>
							</Show>
							<Show when={issuedDateEditMode()}>
								<div class="rounded-md bg-gray-100 p-2">
									<input
										type="text"
										class="w-full rounded-md border border-gray-300 px-2 py-1 font-normal focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-hidden"
										value={issuedDate()}
										disabled={doingSubmission.pending}
										onInput={(e) => {
											setIssuedDate(e.currentTarget.value);
										}}
									/>
									<div class="mt-2 flex items-center justify-end text-sm font-normal">
										<button
											type="button"
											class="rounded-full px-3 py-1 transition-all hover:bg-gray-200"
											disabled={doingSubmission.pending}
											onClick={() => setIssuedDateEditMode(false)}
										>
											<span class="">Cancel</span>
										</button>
										<button
											type="button"
											class="ml-1.5 inline-flex items-center rounded-full bg-blue-600 px-3 py-1 text-white active:scale-95 disabled:bg-gray-200 disabled:text-gray-500"
											disabled={doingSubmission.pending}
											onClick={() => {
												handleSubmission(
													props.customerPricingScheme?.pricingSchemeSlug ?? "",
													issuedDate(),
												);
											}}
										>
											Save
										</button>
									</div>
								</div>
							</Show>
						</th>
					</tr>

					<tr class="w-full font-bold print:text-sm">
						<th class={`${thSharedClass} pl-3`}>H</th>
						<th class={`${thSharedClass} px-1.5`}>W</th>
						<th class={`${thSharedClass} pr-3`}>D</th>
					</tr>
				</thead>

				<tbody class="align-baseline">
					<For each={props.prices}>
						{(price, groupIndex) => {
							const product = price.product;

							if (!product) {
								return <></>;
							}

							const height = product.height;

							// Intentionally swapped by Multay team based on Mr. Jeremy's suggestion.
							const width = product.length;

							// Intentionally swapped by Multay team based on Mr. Jeremy's suggestion.
							const length = product.width;

							const customFields = product.customFields;

							const cbm = customFields?.custom3;

							if (!cbm) {
								return <></>;
							}

							const customProductData: CustomProductData | null =
								props.customProductDataCollection[product.productId] ?? null;

							return (
								<tr class="group relative">
									<td class={`${tdSharedClass}`}>{product.sku}</td>

									<td class={`${tdSharedClass}`}>{product.description}</td>

									<Show when={hasAnyCustomProductData}>
										<td class={`${tdSharedClass}`}>
											{customProductData?.customerProductCode ?? ""}
										</td>

										<td class={`${tdSharedClass}`}>
											{customProductData?.customerProductName ?? ""}
										</td>
									</Show>

									<td class={`${tdSharedClass} text-center`}>
										{formatQuantity(height!)}
									</td>

									<td class={`${tdSharedClass} text-center`}>
										{formatQuantity(width!)}
									</td>

									<td class={`${tdSharedClass} text-center`}>
										{formatQuantity(length!)}
									</td>

									<td class={`${tdSharedClass} text-center`}>
										{formatDimension(cbm)}
									</td>

									<td class={`${tdSharedClass} text-center`}>
										{formatCurrency({
											amount: getProductFobPrice(product.productId),
											symbol: price?.pricingScheme?.currency?.symbol ?? "$",
										})}
									</td>

									<td class={`${tdSharedClass} text-center`}>
										{formatCurrency({
											amount: price?.unitPrice ?? 0.0,
											symbol: price?.pricingScheme?.currency?.symbol ?? "$",
										})}
									</td>
								</tr>
							);
						}}
					</For>
				</tbody>
			</table>

			<PriceListActionButtons
				title="Price List"
				url={`/customer-price-list/${props.customerPricingScheme?.pricingSchemeSlug ?? ""}`}
			/>
		</div>
	);
}
