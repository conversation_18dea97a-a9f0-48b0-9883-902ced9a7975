import { For, Show } from "solid-js";
import {
	PriceData,
	ProductData,
	StructuredCategoryData,
} from "~/types/dto";
import {
	formatCurrency,
	formatDimension,
	formatQuantity,
} from "~/utils/formatting-util";

export default function CollectionGalleryPriceListTable(props: {
	category: StructuredCategoryData;
	products: ProductData[];
}) {
	// const firstThSharedClass =
	// 	"flex items-center flex-wrap justify-center border-x border-black p-1 font-medium";

	// const thSharedClass =
	// 	"flex items-center flex-wrap justify-center border-r border-black p-1 font-medium";

	const thSharedClass = "p-1 font-semibold text-center";

	// const firstTdSharedClass = "border-x border-black p-1 print:p-0.5 xl:p-2";
	// const tdSharedClass = "border-r border-black p-1 print:p-0.5 xl:p-2";

	const tdSharedClass = "p-1 print:p-0.5 xl:p-2";

	return (
		<div class="text-md mt-10 text-stone-800 print:text-[10px]">
			<table class="font-table table w-full table-fixed">
				<thead class="text-center">
					<tr class="w-full text-center text-xl font-bold print:text-sm">
						<th class={`${thSharedClass} w-[15%] px-1`} rowSpan={2}>
							Product Code
						</th>
						<th class={`${thSharedClass} w-[20%] px-1`} rowSpan={2}>
							Description
						</th>
						<th class={`${thSharedClass} w-[20%] px-1`} rowSpan={2}>
							Photo
						</th>
						<th class={`${thSharedClass} w-[15%] px-3`} colspan={3}>
							Dimensions (cm)
						</th>
						<th class={`${thSharedClass} w-[5%]`} rowSpan={2}>
							CBM
						</th>
						<th class={`${thSharedClass} w-[15%]`} rowSpan={2}>
							USD
						</th>
						<th class={`${thSharedClass} w-[10%] leading-[13px]`} rowSpan={2}>
							<h5 class="text-center">MOQ</h5>
							<span class="mt-2 text-[12px] font-medium print:mt-0 print:text-[8px] print:leading-[8px] print:font-semibold">
								(Minimum Order Quantity)
							</span>
						</th>
					</tr>

					<tr class="w-full font-bold print:text-sm">
						<th class={`${thSharedClass} pl-3`}>H</th>
						<th class={`${thSharedClass} px-1.5`}>W</th>
						<th class={`${thSharedClass} pr-3`}>D</th>
					</tr>
				</thead>

				<tbody>
					<For each={props.products}>
						{(product) => {
							const height = product.height;
							const width = product.width;
							const length = product.length;
							const customFieldsLabel = product.productCustomFieldLabels;
							const customFields = product.customFields;
							const cbm = customFields?.custom3 ?? "";

							let price: PriceData | undefined = undefined;

							for (const productPrice of product.prices) {
								if (productPrice.pricingScheme?.isDefault) {
									price = productPrice;
									break;
								}
							}

							return (
								<tr class="group relative font-medium">
									<td class={`${tdSharedClass} text-center font-bold`}>
										{product.sku}
									</td>

									<td class={`${tdSharedClass}`}>{product.description}</td>

									<td
										class={`${tdSharedClass} flex items-center justify-center p-2.5`}
									>
										<Show
											when={
												product && product.images && product.images.length > 0
											}
										>
											<img
												src={product.images![0]!.largeUrl}
												alt="Product image"
												class={`m-auto max-h-40 w-3/4 print:max-h-20`}
											/>
										</Show>
									</td>
									<td class={`${tdSharedClass} text-center`}>
										{formatQuantity(height!)}
									</td>
									<td class={`${tdSharedClass} text-center`}>
										{formatQuantity(width!)}
									</td>
									<td class={`${tdSharedClass} text-center`}>
										{formatQuantity(length!)}
									</td>
									<td class={`${tdSharedClass} text-center`}>
										{formatDimension(cbm)}
									</td>
									<td class={`${tdSharedClass} text-center`}>
										{formatCurrency({
											amount: price?.unitPrice ?? 0.0,
											symbol: price?.pricingScheme?.currency.symbol ?? "$",
										})}
									</td>
									<td class={`${tdSharedClass} text-center`}>
										{" "}
										{formatQuantity(product.totalQuantityOnHand)}
									</td>
								</tr>
							);
						}}
					</For>
				</tbody>
			</table>
		</div>
	);
}
