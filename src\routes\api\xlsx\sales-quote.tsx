import {
  formatCurrency,
  formatStringForXlsx,
  formatWeight,
} from "~/utils/formatting-util";
import xlsx, { WorkSheetOptions } from "node-xlsx";
import { CustomProductData, SalesOrderData } from "~/types/dto";
import { ColInfo, Range } from "xlsx";
import { APIEvent } from "@solidjs/start/server";
import { makeJsonResponse } from "~/utils/http-util";

export async function POST({ request, params }: APIEvent) {
  const rawBody = await request.text();
  let body = rawBody.replace("data=", "");
  body = decodeURIComponent(body);

  let data;

  // if istriMarah = true, tambahkan class "hidden"
  if (body.includes("istriMarah=true")) {
    body = body.replace("istriMarah=true", "istriMarah=true hidden");
  }

  try {
    data = JSON.parse(body);
  } catch (e) {
    // console.error(e);
    return makeJsonResponse("Invalid JSON data.", 400);
  }

  const salesOrder = data.salesOrder as SalesOrderData;
  const customProductDataCollection =
    data.customProductDataCollection as Record<string, CustomProductData>;

  const salesRepName = salesOrder.salesRepTeamMember?.name ?? "";
  const poNumber = salesOrder.poNumber;
  const paymentTermsName = formatStringForXlsx(
    salesOrder.paymentTerms?.name ?? "",
  );

  const hasAnyCustomProductData =
    Object.keys(customProductDataCollection).length > 0;

  let totalQuantity = 0;
  let totalPackage = 0;
  let subtotalNettWeight = 0;
  let subtotalGrossWeight = 0;
  let totalNettWeight = 0;
  let totalGrossWeight = 0;
  let subTotalM3 = 0;
  let totalM3 = 0;
  let allSubTotal = 0;

  const cbm = salesOrder.customFields?.custom1 ?? "";
  const pod = salesOrder.customFields?.custom3 ?? "";
  const blNumber = salesOrder.customFields?.custom2 ?? "";

  const containerAndSealID = salesOrder.customFields?.custom5 ?? "";
  const containerAndSealIDSplits = containerAndSealID.split("/");
  const container = containerAndSealIDSplits[0] ?? "";
  const sealID = containerAndSealIDSplits[1] ?? "";

  const headerCellStyle = {
    font: {
      bold: true,
      sz: 21,
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
    },
    fill: {
      fgColor: {
        rgb: "f3f4f6",
      },
    },
    border: {
      bottom: {
        style: "thin",
        color: {
          rgb: "000000",
        },
      },
    },
  };

  const currencySymbol = salesOrder.currency?.symbol ?? "$";

  const topTableHeaderRow = [];
  const topTableBodyRow = [];

  topTableHeaderRow.push({
    t: "s",
    v: "PO Number",
    s: headerCellStyle,
  });

  topTableBodyRow.push(poNumber);

  if (hasAnyCustomProductData) {
    topTableHeaderRow.push({
      t: "s",
      v: "Sales Rep",
      s: headerCellStyle,
    });
    topTableHeaderRow.push("");

    topTableBodyRow.push(salesRepName);
    topTableBodyRow.push("");
  } else {
    topTableHeaderRow.push([
      {
        t: "s",
        v: "Sales Rep",
        s: headerCellStyle,
      },
    ]);

    topTableBodyRow.push(salesRepName);
  }

  topTableHeaderRow.push({
    t: "s",
    v: "Shipment Terms",
    s: headerCellStyle,
  });

  topTableBodyRow.push(paymentTermsName);

  const totalTopTableBottomGapRows = 2;
  const topTableBottomGapRows = [];

  for (let i = 0; i < totalTopTableBottomGapRows; i++) {
    if (hasAnyCustomProductData) {
      topTableBottomGapRows.push(["", ""]);
    } else {
      topTableBottomGapRows.push([""]);
    }
  }

  const mainTableHeaderRow = [];

  mainTableHeaderRow.push({
    t: "s",
    v: "Multay Product",
    s: headerCellStyle,
  });

  if (hasAnyCustomProductData) {
    mainTableHeaderRow.push({
      t: "s",
      v: "Customer Product",
      s: headerCellStyle,
    });
  }

  mainTableHeaderRow.push({
    t: "s",
    v: "Picture",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "Quantity",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "H",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "W",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "D",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "M3",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "Total m3",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "Unit Price (US $)",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "Sub Total (US $)",
    s: headerCellStyle,
  });

  const mainTableBodyRows = [];

  const salesOrderLines = salesOrder.lines ?? [];

  for (const line of salesOrderLines) {
    const quantity = line.quantity.standardQuantity;
    const quantityNumber = Number(quantity);
    totalQuantity += quantityNumber;

    const packages = line.product?.customFields?.custom6 ?? "";
    const packagesNumber = Number(packages);
    totalPackage += packagesNumber;

    const customFields = line.product?.customFields;

    const nettWeight = customFields?.custom1 ?? "";
    const nettWeightNumber = Number(nettWeight);

    subtotalNettWeight += nettWeightNumber;
    totalNettWeight += subtotalNettWeight;

    const grossWeight = customFields?.custom2 ?? "";
    const grossWeightNumber = Number(grossWeight);

    subtotalGrossWeight += grossWeightNumber;
    totalGrossWeight += subtotalGrossWeight;

    const customProductData = customProductDataCollection[line.productId];

    const mainTableBodyRow = [];

    const sku = formatStringForXlsx(line.product?.sku ?? "");
    const productName = formatStringForXlsx(line.product?.name ?? "");

    mainTableBodyRow.push(`${sku}\n${productName}`);

    if (customProductData) {
      const customerProductCode = formatStringForXlsx(
        customProductData.customerProductCode,
      );
      const customerProductName = formatStringForXlsx(
        customProductData.customerProductName,
      );

      mainTableBodyRow.push(`${customerProductCode}\n${customerProductName}`);
    }

    const imageUrl = line.product?.images![0]?.largeUrl ?? "";

    mainTableBodyRow.push({
      t: "s",
      v: imageUrl,
      f: `IMAGE("${imageUrl}")`,
    });

    mainTableBodyRow.push({
      t: "n",
      v: quantityNumber,
      z: "0",
    });

    const height = line.product?.height;
    const heightNumber = Number(height);

    /**
     * Dari Multay memang sudah terlanjut terbalik.
     * Di inFlow, walau dia width, tapi dianggap length.
     *
     * Jadi tim Multay sendiri kalau di inFlow ngisi width, itu maksudnya ngisi length.
     * Karena kalau mau dikoreksi, sudah terlalu banyak untuk diubah.
     */
    const width = line.product?.length;
    const widthNumber = Number(width);

    /**
     * Dari Multay memang sudah terlanjut terbalik.
     * Di inFlow, walau dia length, tapi dianggap width.
     *
     * Jadi tim Multay sendiri kalau di inFlow ngisi length, itu maksudnya ngisi width.
     * Karena kalau mau dikoreksi, sudah terlalu banyak untuk diubah.
     */
    const length = line.product?.width;
    const lengthNumber = Number(length);

    mainTableBodyRow.push({
      t: "n",
      v: height,
      z: "0",
    });

    mainTableBodyRow.push({
      t: "n",
      v: width,
      z: "0",
    });

    mainTableBodyRow.push({
      t: "n",
      v: length,
      z: "0",
    });

    // m3 is custom field 7 is number.
    const m3 = line.product?.customFields?.custom3 ?? "";
    const m3Number = Number(m3);
    subTotalM3 = m3Number * quantityNumber;
    totalM3 += subTotalM3;

    mainTableBodyRow.push({
      t: "n",
      v: m3,
      w: formatWeight(m3),
    });

    mainTableBodyRow.push({
      t: "n",
      v: subTotalM3,
      w: formatWeight(subTotalM3),
    });

    // unit price
    const unitPrice = line.unitPrice;
    const unitPriceNumber = Number(unitPrice);
    const subTotal = unitPriceNumber * quantityNumber;
    allSubTotal += subTotal;
    mainTableBodyRow.push({
      t: "n",
      v: unitPrice,
      z: `${currencySymbol}0.00`,
      w: formatCurrency({
        amount: unitPrice,
        symbol: currencySymbol,
      }),
    });

    mainTableBodyRow.push({
      t: "n",
      v: subTotal,
      z: `${currencySymbol}0.00`,
      w: formatCurrency({
        amount: subTotal,
        symbol: currencySymbol,
      }),
    });

    mainTableBodyRows.push(mainTableBodyRow);
  }
  const totalRow = [
    {
      t: "s",
      v: "Total",
      s: {
        font: {
          bold: true,
          sz: 5,
        },
        alignment: {
          horizontal: "center",
          vertical: "center",
        },
        fill: {
          fgColor: {
            rgb: "f3f4f6",
          },
        },
        border: {
          bottom: {
            style: "thin",
            color: {
              rgb: "000000",
            },
          },
        },
      },
    },
    "",
    "",

    // total all quantity
    {
      t: "n",
      v: totalQuantity,
      z: "0",
      s: {
        font: {
          bold: true,
          sz: 12,
        },
        alignment: {
          horizontal: "center",
          vertical: "center",
        },
        fill: {
          fgColor: {
            rgb: "f3f4f6",
          },
        },
        border: {
          bottom: {
            style: "thin",
            color: {
              rgb: "000000",
            },
          },
        },
      },
    },
    "",
    "",
    "",
    "",
    // total all totalM3
    {
      t: "n",
      v: totalM3,
      z: "0.000",
      s: {
        font: {
          bold: true,
          sz: 12,
        },
        alignment: {
          horizontal: "center",
          vertical: "center",
        },
        fill: {
          fgColor: {
            rgb: "f3f4f6",
          },
        },
        border: {
          bottom: {
            style: "thin",
            color: {
              rgb: "000000",
            },
          },
        },
      },
    },
    "",
    // total all subTotal unit price
    {
      t: "n",
      v: allSubTotal,
      z: `${currencySymbol}0.00`,
      w: formatCurrency({
        amount: allSubTotal,
        symbol: currencySymbol,
      }),
    },
  ];

  mainTableBodyRows.push(totalRow);

  const mainTableBottomGapRows = 3;

  // Create 3 empty rows as whitespace gap.
  for (let i = 0; i < mainTableBottomGapRows; i++) {
    mainTableBodyRows.push([]);
  }

  // Remarks row.
  const remarksRow = [
    {
      t: "s",
      v: `
        Remarks:\n
        ${salesOrder.orderRemarks ?? "-"}\n
      `,
    },
    "",
    "",
    "",
  ];

  mainTableBodyRows.push(remarksRow);

  let sheetName = `sales-quote-${salesOrder.orderNumber}`;

  // The sheetName can't be more than 31 characters.
  sheetName = sheetName.substring(0, 26) + ".xlsx";

  const totalTopTableRows = 2;
  const totalMainTableHeaderRows = 1;
  const totalProductLines = salesOrder.lines?.length ?? 0;
  const totalPriceRowPosIndex =
    totalTopTableRows +
    totalTopTableBottomGapRows +
    totalMainTableHeaderRows +
    totalProductLines;

  const totalPriceColsToMerge = hasAnyCustomProductData ? 2 : 1;

  /**
   * In topTable (before mainTable), we have 2 rows.
   *
   * For its header (row 1)
   * salesRepColsMerge: if hasAnyCustomProductData, cols 2nd and 3rd columns merged. If not, then undefined.
   * paymentTermsColsMerge: if hasAnyCustomProductData, cols 4th, 5th,and 6th merged. If not, then 3rd, 4th, 5th.
   *
   * For its value (row 2)
   * salesRepColsMerge: if hasAnyCustomProductData, cols 2nd and 3rd columns merged. If not, then undefined.
   * paymentTermsColsMerge: if hasAnyCustomProductData, cols 4th, 5th,and 6th merged. If not, then 3rd, 4th, 5th.
   */

  // In rows #totalPriceRowPosIndex, we have "Total" row. That row should have its 5 first columns merged.
  const totalPriceRowColsMerge = {
    s: { r: totalPriceRowPosIndex, c: 0 },
    e: { r: totalPriceRowPosIndex, c: totalPriceColsToMerge },
  };

  const salesRepHeaderColsMerge: Range | undefined = hasAnyCustomProductData
    ? {
        s: { r: 0, c: 1 },
        e: { r: 0, c: 2 },
      }
    : undefined;

  const salesRepBodyColsMerge: Range | undefined = hasAnyCustomProductData
    ? {
        s: { r: 0, c: 0 },
        e: { r: 0, c: 0 },
      }
    : undefined;

  const shipDateHeaderColsMerge: Range = {
    s: { r: 0, c: hasAnyCustomProductData ? 1 : 1 },
    e: { r: 0, c: hasAnyCustomProductData ? 2 : 1 },
  };

  const shipDateBodyColsMerge: Range = {
    s: { r: 1, c: hasAnyCustomProductData ? 1 : 1 },
    e: { r: 1, c: hasAnyCustomProductData ? 2 : 1 },
  };

  const paymentTermsHeaderColsMerge: Range = {
    s: { r: 0, c: hasAnyCustomProductData ? 3 : 2 },
    e: { r: 0, c: hasAnyCustomProductData ? 11 : 11 },
  };

  const paymentTermsBodyColsMerge: Range = {
    s: { r: 1, c: hasAnyCustomProductData ? 3 : 2 },
    e: { r: 1, c: hasAnyCustomProductData ? 11 : 11 },
  };

  const mergedCols: Range[] = [];

  if (salesRepHeaderColsMerge && salesRepBodyColsMerge) {
    mergedCols.push(salesRepHeaderColsMerge);
    mergedCols.push(salesRepBodyColsMerge);
  }

  if (shipDateHeaderColsMerge && shipDateBodyColsMerge) {
    mergedCols.push(shipDateHeaderColsMerge);
    mergedCols.push(shipDateBodyColsMerge);
  }

  if (paymentTermsHeaderColsMerge && paymentTermsBodyColsMerge) {
    mergedCols.push(paymentTermsHeaderColsMerge);
    mergedCols.push(paymentTermsBodyColsMerge);
  }

  mergedCols.push(totalPriceRowColsMerge);

  const colsWidth: ColInfo[] = [];

  colsWidth.push({ wch: 80 });

  if (hasAnyCustomProductData) {
    colsWidth.push({ wch: 80 });
  }

  colsWidth.push({ wch: 20 });
  colsWidth.push({ wch: 8 });
  colsWidth.push({ wch: 12 });
  colsWidth.push({ wch: 12 });
  colsWidth.push({ wch: 12 });
  colsWidth.push({ wch: 12 });
  colsWidth.push({ wch: 12 });
  colsWidth.push({ wch: 12 });
  colsWidth.push({ wch: 15 });

  const sheetOptions: WorkSheetOptions = {
    "!cols": colsWidth,
    "!merges": mergedCols,
  };

  const buffer = xlsx.build([
    {
      name: sheetName,
      data: [
        topTableHeaderRow,
        topTableBodyRow,
        ...topTableBottomGapRows,
        mainTableHeaderRow,
        ...mainTableBodyRows,
      ],
      options: sheetOptions,
    },
  ]);

  return new Response(buffer, {
    headers: {
      "Content-Type":
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "Content-Disposition": `attachment; filename=${sheetName}`,
    },
  });
}
