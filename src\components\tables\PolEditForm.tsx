import { Loader2, Save, SquarePen } from "lucide-solid";
import { Show } from "solid-js";
import { SalesOrderOptionData } from "~/types/dto";

export function PolEditForm(props: {
	salesOrderId: string;
	salesOrderOptions?: SalesOrderOptionData;
	openForm: boolean;
	calculatedValue: string;
	inputValue: string;
	isSaving: boolean;
	onEditButtonClick: () => void;
	onInput: (e: InputEvent) => void;
	onSubmit: (e: SubmitEvent) => void;
}) {
	return (
		<div class="inline-flex items-center">
			<Show
				when={props.openForm}
				fallback={
					<>
						<span>{props.calculatedValue}</span>
						<button
							type="button"
							class="hover:text-companycolor relative ml-2 cursor-pointer print:hidden"
							title="Edit POL"
							onClick={props.onEditButtonClick}
						>
							<SquarePen size={11} />
						</button>
					</>
				}
			>
				<form action="" method="post" onSubmit={props.onSubmit}>
					<input
						type="text"
						class="pol-input inline-block max-w-60 rounded-sm border border-gray-300 px-2 py-1 md:w-72 lg:w-80 xl:w-96 print:hidden"
						placeholder="Enter POL"
						value={props.inputValue}
						onInput={props.onInput}
					/>
					<button
						type="submit"
						class="bg-companycolor hover:bg-primary/50 relative ml-2 inline-flex items-center justify-center rounded-md p-1.5 text-white print:hidden"
						title="Save POL"
					>
						<Show when={props.isSaving} fallback={<Save size={11} />}>
							<Loader2 size={11} class={`mx-auto animate-spin`} />
						</Show>
					</button>
				</form>
			</Show>
		</div>
	);
}
