<div align="center">

<img src="public/images/multay-logo-compact.png" alt="Multay Logo" width="300"/>

# Multay One

**Internal Inventory and Sales Management System**

[Features](#key-features) • [Installation](#installation--setup) • [Development](#development) • [Architecture](#architecture-notes) • [Status](#project-status)

![SolidJS](https://img.shields.io/badge/SolidJS-2C4F7C?style=for-the-badge&logo=solid&logoColor=white)
![TailwindCSS](https://img.shields.io/badge/TailwindCSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)
![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)
![Supabase](https://img.shields.io/badge/Supabase-3ECF8E?style=for-the-badge&logo=supabase&logoColor=white)

</div>

## 📋 Overview

Multay One is an internal application for Multay Indonesia that automates sales forms creation and integrates with inFlow Inventory. It provides a streamlined workflow for managing products, collections, sales orders, purchase orders, and customer data.

## 📚 Table of Contents

- [Multay One](#multay-one)
	- [📋 Overview](#-overview)
	- [📚 Table of Contents](#-table-of-contents)
	- [🔧 Prerequisites](#-prerequisites)
	- [💻 Tech Stack](#-tech-stack)
	- [✨ Key Features](#-key-features)
	- [📁 Project Structure](#-project-structure)
	- [🚀 Installation \& Setup](#-installation--setup)
	- [🛠️ Development](#️-development)
	- [📦 Deployment](#-deployment)
		- [Production Deployment](#production-deployment)
		- [Hosting Requirements](#hosting-requirements)
	- [🏗️ Architecture Notes](#️-architecture-notes)
	- [🔐 Environment Variables](#-environment-variables)
	- [👥 Contribution Guidelines](#-contribution-guidelines)
		- [Code Style](#code-style)
		- [Branch Naming Convention](#branch-naming-convention)
		- [Pull Request Process](#pull-request-process)
		- [Issue Reporting](#issue-reporting)
	- [📊 Project Status](#-project-status)
		- [Known Issues](#known-issues)
	- [🔍 Troubleshooting](#-troubleshooting)
	- [📄 License](#-license)

## 🔧 Prerequisites

| Requirement                     | Version | Description               |
| ------------------------------- | ------- | ------------------------- |
| **Node.js**                     | v22.x+  | JavaScript runtime        |
| **PNPM**                        | v10.x+  | Preferred package manager |
| **Git**                         | Latest  | Version control           |
| **Supabase Account**            | -       | Database & authentication |
| **inFlow Inventory API Access** | -       | Required API credentials  |

## 💻 Tech Stack

- **Frontend**: SolidJS with SolidStart (SSR)
- **Styling**: TailwindCSS v4
- **Database & Authentication**: Supabase (via supabase-ssr package)
- **API Integration**: inFlow Inventory API
- **Data Formats**: JSON, XLSX
- **Email Service**: Mailjet

## ✨ Key Features

- **Authentication & Database**: Secure login and data storage via Supabase
- **Products Management**: View and manage product data from inFlow
- **Collections Gallery**: Browse product collections with pricing
- **Sales Orders**: Create and manage sales orders
- **Purchase Orders**: View and manage purchase orders
- **Customer Management**: Track customer-specific product codes
- **Email Notifications**: Automated emails for order updates

## 📁 Project Structure

```
/src
├── /components         # Reusable UI components
├── /configs            # Configuration files
├── /data-schema        # Validation schemas and data structures
├── /interfaces         # TypeScript interfaces and types
├── /routes             # Application routes (file-based routing)
├── /services           # Service layer for API interactions
│   ├── /client         # Client-side services
│   └── /server         # Server-side services (marked with "use server")
└── /utils              # Utility functions
```

## 🚀 Installation & Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/multayindonesia/multay-one.git
   cd multay-one
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   - Copy `.env.example` to `.env` (create if not exists)
   - Fill in the required environment variables (see [Environment Variables](#-environment-variables) section)

## 🛠️ Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start
```

## 📦 Deployment

### Production Deployment

1. Build the application:
   ```bash
   pnpm build
   ```

2. Start the production server:
   ```bash
   pnpm start
   ```

### Hosting Requirements

- Node.js runtime environment
- Environment variables properly configured
- Network access to Supabase and inFlow API endpoints

## 🏗️ Architecture Notes

- **Server/Client Separation**: Clear separation between server and client code
- **Server-Side Rendering**: Improves performance and SEO
- **API Integration**: Server-side communication with inFlow Inventory API
- **Supabase Integration**: All Supabase connections (auth & database) happen server-side only

<details>
<summary><strong>Data Flow</strong></summary>

1. Client requests data via SolidStart routes
2. Server components handle API calls to inFlow
3. Data is processed and returned to the client
4. Client renders the data using SolidJS components
</details>

## 🔐 Environment Variables

<details>
<summary><strong>Supabase Configuration (Auth & Database)</strong></summary>

- `PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `PRIVATE_SUPABASE_SERVICE_ROLE_KEY`: Service role key for admin operations
</details>

<details>
<summary><strong>inFlow API Configuration</strong></summary>

- `INFLOW_API_URL`: Base URL for inFlow API
- `INFLOW_API_KEY`: API key for inFlow
- `INFLOW_COMPANY_ID`: Your inFlow company ID
</details>

<details>
<summary><strong>Email Service (Mailjet)</strong></summary>

- `MAILJET_API_KEY`: Mailjet API key
- `MAILJET_SECRET_KEY`: Mailjet secret key
- `EMAIL_FROM`: Sender email address
- `EMAIL_FROM_NAME`: Sender name
</details>

<details>
<summary><strong>Application Settings</strong></summary>

- `NODE_ENV`: Environment (development, production)
- `PORT`: Port for the server (default: 3000)
</details>

Create a `.env` file in the root directory with these variables. For development, you can copy the `.env.example` file and fill in your values.

## 👥 Contribution Guidelines

### Code Style
- Use tabs for indentation
- Follow the existing code structure
- Use TypeScript for type safety
- Document functions and components

### Branch Naming Convention
- Feature: `feature/feature-name`
- Bugfix: `bugfix/issue-description`
- Hotfix: `hotfix/issue-description`

### Pull Request Process
1. Create a branch from `main`
2. Implement your changes
3. Write or update tests as needed
4. Submit a pull request to `main`
5. Request review from maintainers

### Issue Reporting
When reporting issues, please include:
- Description of the issue
- Steps to reproduce
- Expected vs. actual behavior
- Screenshots if applicable
- Environment details

## 📊 Project Status

Current status: **Active Development**

### Known Issues
- See the [Issues](https://github.com/multayindonesia/multay-one/issues) page for current known issues

## 🔍 Troubleshooting

<details>
<summary><strong>Supabase Connection Problems</strong></summary>

- Verify Supabase credentials in `.env`
- Check browser console for errors
- Ensure cookies are enabled in your browser
</details>

<details>
<summary><strong>API Connection Issues</strong></summary>

- Verify inFlow API credentials
- Check network connectivity to inFlow servers
- Verify API endpoint URLs
</details>

<details>
<summary><strong>Build Failures</strong></summary>

- Clear node_modules and reinstall dependencies
- Ensure Node.js version is compatible
- Check for TypeScript errors
</details>

## 📄 License

© 2025 Multay Indonesia. All rights reserved.

This is proprietary software. Unauthorized copying, modification, distribution, or use of this software is strictly prohibited.
