import { For, Show, batch, createSignal } from "solid-js";
import { CustomerData } from "~/types/dto";
import CustomerDataRow from "./CustomerDataRow";
import { CustomerListResponse } from "~/types/response";
import { clientFetchCustomers } from "~/services/client-services/customer-client-service";
import LoadMore from "~/components/data-loading/LoadMore";
import LoadingSpinner from "~/components/LoadingSpinner";
import SearchBehavior from "../data-loading/SearchBehavior";
import CustomerViewButton from "./CustomerViewButton";
import { useSearchParams } from "@solidjs/router";
import { getSearchKeyword } from "~/utils/search-util";

export interface CustomerListWithLoadMoreProps {
	baseApiUrl: string;
	customers: CustomerData[];
	searchField?: HTMLInputElement;
}

export default function CustomerListWithLoadMore(
	props: CustomerListWithLoadMoreProps,
) {
	const [searchParams, setSearchParams] = useSearchParams();
	const [customers, setCustomers] = createSignal(props.customers);
	const [selectedCustomer, setSelectedCustomer] = createSignal<
		CustomerData | undefined
	>();
	const [isModalOpen, setIsModalOpen] = createSignal(false);
	const [doingLoadMore, setDoingLoadMore] = createSignal<boolean>(false);
	const [loadMoreFinished, setLoadMoreFinished] = createSignal<boolean>(false);

	async function handleLoadMore() {
		if (doingLoadMore() || loadMoreFinished()) return;

		batch(() => {
			setDoingLoadMore(true);
		});

		const response = await fetchDataList(getSearchKeyword(searchParams));

		handleFetchComplete(response);
	}

	async function fetchDataList(
		keyword?: string,
	): Promise<CustomerListResponse> {
		const lastCustomerItem = customers().length
			? customers()[customers().length - 1]
			: undefined;

		return await clientFetchCustomers({
			url: `${props.baseApiUrl}/customers`,
			after: lastCustomerItem?.customerId ?? undefined,
			keyword: keyword,
		});
	}

	async function handleSearch(keyword: string) {
		batch(() => {
			setLoadMoreFinished(false);
			setDoingLoadMore(true);
			setSearchParams({ keyword: keyword });
			setCustomers([]);
		});

		const response = await fetchDataList(keyword);

		handleFetchComplete(response);
	}

	function handleFetchComplete(response: CustomerListResponse): void {
		batch(() => {
			setDoingLoadMore(false);

			if (!response.success) {
				alert(response.message);
				setLoadMoreFinished(true);
				return;
			}

			if (!response?.data?.length) {
				setLoadMoreFinished(true);
				return;
			}

			setCustomers(customers().concat(response.data));
		});
	}

	function openModal(customer: CustomerData) {
		batch(() => {
			setSelectedCustomer(customer);
			setIsModalOpen(true);
		});
	}

	let sectionRef: HTMLElement | undefined;

	return (
		<>
			<section class="text pt-32 text-sm" ref={sectionRef}>
				<SearchBehavior
					searchField={props.searchField}
					onSearch={handleSearch}
				/>
				<LoadMore contentRef={sectionRef} onLoadMore={handleLoadMore}>
					<For each={customers()}>
						{(customer) => (
							<CustomerDataRow customer={customer} onClick={openModal} />
						)}
					</For>
				</LoadMore>

				<Show when={doingLoadMore()}>
					<LoadingSpinner class="mt-14" />
				</Show>
			</section>
			<Show when={isModalOpen() && selectedCustomer()}>
				<div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
					<CustomerViewButton
						customerId={selectedCustomer()?.customerId}
						onCloseModal={() => setIsModalOpen(false)}
						salesOrderId="d5cb0485-2a63-43e9-b9ea-2029452f6fa5"
					/>
				</div>
			</Show>
		</>
	);
}
