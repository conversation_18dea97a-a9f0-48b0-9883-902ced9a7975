{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "5", "dialect": "mysql", "tables": {"categories": {"name": "categories", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "category_id": {"autoincrement": false, "name": "category_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "is_default": {"default": 0, "autoincrement": false, "name": "is_default", "type": "tinyint(1)", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(70)", "primaryKey": false, "notNull": true}, "parent_category_id": {"autoincrement": false, "name": "parent_category_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": false}, "timestamp": {"default": "''", "autoincrement": false, "name": "timestamp", "type": "<PERSON><PERSON><PERSON>(35)", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"categories_id": {"name": "categories_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {"category_id": {"name": "category_id", "columns": ["category_id"]}}, "checkConstraint": {}}, "customers": {"name": "customers", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "customer_id": {"autoincrement": false, "name": "customer_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "email": {"autoincrement": false, "name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_active": {"default": 0, "autoincrement": false, "name": "is_active", "type": "tinyint(1)", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "contact_name": {"autoincrement": false, "name": "contact_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "phone": {"default": "''", "autoincrement": false, "name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "website": {"default": "''", "autoincrement": false, "name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "default_billing_address_id": {"autoincrement": false, "name": "default_billing_address_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "default_carrier": {"autoincrement": false, "name": "default_carrier", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "default_location_id": {"autoincrement": false, "name": "default_location_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "default_payment_method": {"autoincrement": false, "name": "default_payment_method", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "default_payment_terms_id": {"autoincrement": false, "name": "default_payment_terms_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "default_sales_rep": {"autoincrement": false, "name": "default_sales_rep", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "default_sales_rep_team_member_id": {"autoincrement": false, "name": "default_sales_rep_team_member_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "default_shipping_address_id": {"autoincrement": false, "name": "default_shipping_address_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "fax": {"default": "''", "autoincrement": false, "name": "fax", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "pricing_scheme_id": {"autoincrement": false, "name": "pricing_scheme_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "discount": {"default": "''", "autoincrement": false, "name": "discount", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "taxing_scheme_id": {"autoincrement": false, "name": "taxing_scheme_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "last_modified_by_id": {"autoincrement": false, "name": "last_modified_by_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "remarks": {"autoincrement": false, "name": "remarks", "type": "<PERSON><PERSON><PERSON>(300)", "primaryKey": false, "notNull": true}, "tax_exempt_number": {"default": "''", "autoincrement": false, "name": "tax_exempt_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "timestamp": {"default": "''", "autoincrement": false, "name": "timestamp", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "custom_fields": {"autoincrement": false, "name": "custom_fields", "type": "json", "primaryKey": false, "notNull": false}, "addresses": {"autoincrement": false, "name": "addresses", "type": "json", "primaryKey": false, "notNull": false}, "balances": {"autoincrement": false, "name": "balances", "type": "json", "primaryKey": false, "notNull": false}, "credits": {"autoincrement": false, "name": "credits", "type": "json", "primaryKey": false, "notNull": false}, "default_billing_address": {"autoincrement": false, "name": "default_billing_address", "type": "json", "primaryKey": false, "notNull": false}, "default_location": {"autoincrement": false, "name": "default_location", "type": "json", "primaryKey": false, "notNull": false}, "default_payment_terms": {"autoincrement": false, "name": "default_payment_terms", "type": "json", "primaryKey": false, "notNull": false}, "default_sales_rep_team_member": {"autoincrement": false, "name": "default_sales_rep_team_member", "type": "json", "primaryKey": false, "notNull": false}, "default_shipping_address": {"autoincrement": false, "name": "default_shipping_address", "type": "json", "primaryKey": false, "notNull": false}, "last_modified_by": {"autoincrement": false, "name": "last_modified_by", "type": "json", "primaryKey": false, "notNull": false}, "order_history": {"autoincrement": false, "name": "order_history", "type": "json", "primaryKey": false, "notNull": false}, "pricing_scheme": {"autoincrement": false, "name": "pricing_scheme", "type": "json", "primaryKey": false, "notNull": false}, "taxing_scheme": {"autoincrement": false, "name": "taxing_scheme", "type": "json", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"customers_id": {"name": "customers_id", "columns": ["id"]}}, "indexes": {"contact_name": {"name": "contact_name", "columns": ["contact_name"], "isUnique": false}, "is_active": {"name": "is_active", "columns": ["is_active"], "isUnique": false}, "name": {"name": "name", "columns": ["name"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {"customer_id": {"name": "customer_id", "columns": ["customer_id"]}}, "checkConstraint": {}}, "logs": {"name": "logs", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "date": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "date", "type": "datetime", "primaryKey": false, "notNull": true}, "type": {"default": "'log'", "autoincrement": false, "name": "type", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": true}, "content": {"autoincrement": false, "name": "content", "type": "json", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"logs_id": {"name": "logs_id", "columns": ["id"]}}, "indexes": {"date": {"name": "date", "columns": ["date"], "isUnique": false}, "type": {"name": "type", "columns": ["type"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraint": {}}, "options": {"name": "options", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "datetime", "primaryKey": false, "notNull": true}, "updated_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"autoincrement": false, "name": "value", "type": "json", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"options_id": {"name": "options_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraint": {}}, "pricing_schemes": {"name": "pricing_schemes", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "pricing_scheme_id": {"autoincrement": false, "name": "pricing_scheme_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "currency_id": {"default": "''", "autoincrement": false, "name": "currency_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "is_active": {"default": 0, "autoincrement": false, "name": "is_active", "type": "tinyint(1)", "primaryKey": false, "notNull": true}, "is_default": {"default": 0, "autoincrement": false, "name": "is_default", "type": "tinyint(1)", "primaryKey": false, "notNull": true}, "is_tax_inclusive": {"default": 0, "autoincrement": false, "name": "is_tax_inclusive", "type": "tinyint(1)", "primaryKey": false, "notNull": true}, "timestamp": {"default": "''", "autoincrement": false, "name": "timestamp", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "currency": {"autoincrement": false, "name": "currency", "type": "json", "primaryKey": false, "notNull": false}, "product_prices": {"autoincrement": false, "name": "product_prices", "type": "json", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"pricing_schemes_id": {"name": "pricing_schemes_id", "columns": ["id"]}}, "indexes": {"is_active": {"name": "is_active", "columns": ["is_active"], "isUnique": false}, "name": {"name": "name", "columns": ["name"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {"pricing_scheme_id": {"name": "pricing_scheme_id", "columns": ["pricing_scheme_id"]}}, "checkConstraint": {}}, "sales_products": {"name": "sales_products", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "product_id": {"autoincrement": false, "name": "product_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "sku": {"autoincrement": false, "name": "sku", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(300)", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "<PERSON><PERSON><PERSON>(400)", "primaryKey": false, "notNull": true}, "is_active": {"default": 0, "autoincrement": false, "name": "is_active", "type": "tinyint(1)", "primaryKey": false, "notNull": true}, "width": {"default": "''", "autoincrement": false, "name": "width", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "height": {"default": "''", "autoincrement": false, "name": "height", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "length": {"default": "''", "autoincrement": false, "name": "length", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "weight": {"default": "''", "autoincrement": false, "name": "weight", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "auto_assembler": {"default": 0, "autoincrement": false, "name": "auto_assembler", "type": "tinyint(1)", "primaryKey": false, "notNull": true}, "barcode": {"autoincrement": false, "name": "barcode", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "category_id": {"default": "''", "autoincrement": false, "name": "category_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "default_image_id": {"default": "''", "autoincrement": false, "name": "default_image_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "hs_tariff_number": {"default": "''", "autoincrement": false, "name": "hs_tariff_number", "type": "<PERSON><PERSON><PERSON>(150)", "primaryKey": false, "notNull": true}, "include_quantity_buildable": {"default": 0, "autoincrement": false, "name": "include_quantity_buildable", "type": "tinyint(1)", "primaryKey": false, "notNull": true}, "is_manufacturable": {"default": 0, "autoincrement": false, "name": "is_manufacturable", "type": "tinyint(1)", "primaryKey": false, "notNull": true}, "item_type": {"default": "''", "autoincrement": false, "name": "item_type", "type": "<PERSON><PERSON><PERSON>(70)", "primaryKey": false, "notNull": true}, "last_modified_by_id": {"default": "''", "autoincrement": false, "name": "last_modified_by_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "last_modified_date_time": {"default": "''", "autoincrement": false, "name": "last_modified_date_time", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "last_vendor_id": {"default": "''", "autoincrement": false, "name": "last_vendor_id", "type": "<PERSON><PERSON><PERSON>(90)", "primaryKey": false, "notNull": true}, "origin_country": {"default": "''", "autoincrement": false, "name": "origin_country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "remarks": {"autoincrement": false, "name": "remarks", "type": "text", "primaryKey": false, "notNull": true}, "standard_uom_name": {"default": "''", "autoincrement": false, "name": "standard_uom_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "timestamp": {"default": "''", "autoincrement": false, "name": "timestamp", "type": "<PERSON><PERSON><PERSON>(35)", "primaryKey": false, "notNull": true}, "total_quantity_on_hand": {"autoincrement": false, "name": "total_quantity_on_hand", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": true}, "track_serials": {"default": 0, "autoincrement": false, "name": "track_serials", "type": "tinyint(1)", "primaryKey": false, "notNull": true}, "custom_fields": {"autoincrement": false, "name": "custom_fields", "type": "json", "primaryKey": false, "notNull": false}, "category": {"autoincrement": false, "name": "category", "type": "json", "primaryKey": false, "notNull": false}, "cost": {"autoincrement": false, "name": "cost", "type": "json", "primaryKey": false, "notNull": false}, "default_image": {"autoincrement": false, "name": "default_image", "type": "json", "primaryKey": false, "notNull": false}, "images": {"autoincrement": false, "name": "images", "type": "json", "primaryKey": false, "notNull": false}, "default_price": {"autoincrement": false, "name": "default_price", "type": "json", "primaryKey": false, "notNull": false}, "inventory_lines": {"autoincrement": false, "name": "inventory_lines", "type": "json", "primaryKey": false, "notNull": false}, "item_boms": {"autoincrement": false, "name": "item_boms", "type": "json", "primaryKey": false, "notNull": false}, "last_modified_by": {"autoincrement": false, "name": "last_modified_by", "type": "json", "primaryKey": false, "notNull": false}, "last_vendor": {"autoincrement": false, "name": "last_vendor", "type": "json", "primaryKey": false, "notNull": false}, "prices": {"autoincrement": false, "name": "prices", "type": "json", "primaryKey": false, "notNull": false}, "product_custom_field_labels": {"autoincrement": false, "name": "product_custom_field_labels", "type": "json", "primaryKey": false, "notNull": false}, "product_operations": {"autoincrement": false, "name": "product_operations", "type": "json", "primaryKey": false, "notNull": false}, "purchasing_uom": {"autoincrement": false, "name": "purchasing_uom", "type": "json", "primaryKey": false, "notNull": false}, "reorder_settings": {"autoincrement": false, "name": "reorder_settings", "type": "json", "primaryKey": false, "notNull": false}, "sales_uom": {"autoincrement": false, "name": "sales_uom", "type": "json", "primaryKey": false, "notNull": false}, "tax_codes": {"autoincrement": false, "name": "tax_codes", "type": "json", "primaryKey": false, "notNull": false}, "vendor_items": {"autoincrement": false, "name": "vendor_items", "type": "json", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"sales_products_id": {"name": "sales_products_id", "columns": ["id"]}}, "indexes": {"is_active": {"name": "is_active", "columns": ["is_active"], "isUnique": false}, "name": {"name": "name", "columns": ["name"], "isUnique": false}, "sku": {"name": "sku", "columns": ["sku"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {"product_id": {"name": "product_id", "columns": ["product_id"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {"logs": {"columns": {"date": {"isDefaultAnExpression": true}}}, "options": {"columns": {"created_at": {"isDefaultAnExpression": true}, "updated_at": {"isDefaultAnExpression": true}}}}, "indexes": {}}}