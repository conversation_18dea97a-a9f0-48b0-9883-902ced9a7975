import Mailjet from "node-mailjet";
import { EmailFromOrToData } from "~/types/dto";
import { SendEmailResponse } from "~/types/response";
import { mailjetConfig } from "~/configs/server-config";

export const mailjet = Mailjet.apiConnect(
	mailjetConfig().publicApiKey ?? "",
	mailjetConfig().secretApiKey ?? "",
	{
		config: {},
		options: {},
	},
);

export interface SendEmailProps {
	from: EmailFromOrToData;
	to: EmailFromOrToData;
	replyTo: EmailFromOrToData;
	cc?: string;
	bcc?: string;
	subject: string;
	body: string;
	contentType?: string;
	button?: {
		text: string;
		url: string;
	};
}

export async function sendEmail(
	props: SendEmailProps,
): Promise<SendEmailResponse> {
	"use server";

	const msgItem: Record<string, any> = {};

	msgItem.From = {
		Email: props.from.email,
		Name: props.from.name,
	};

	msgItem.To = [
		{
			Email: props.to.email,
			Name: props.to.name,
		},
	];

	msgItem.ReplyTo = {
		Email: props.replyTo.email,
		Name: props.replyTo.name,
	};

	if (props.cc) {
		msgItem.Cc = [
			{
				Email: props.cc,
				Name: props.cc,
			},
		];
	}

	if (props.bcc) {
		msgItem.Bcc = [
			{
				Email: props.bcc,
				Name: props.bcc,
			},
		];
	}

	msgItem.Subject = props.subject;
	msgItem.TextPart = props.body;
	msgItem.HTMLPart = props.body;

	try {
		const response = await mailjet.post("send", { version: "v3.1" }).request({
			Messages: [msgItem],
		});

		return {
			success: true,
			message: "Email sent successfully.",
			data: response.body,
		};
	} catch (error) {
		// console.log("Error when sending email from server: ", error);

		return {
			success: false,
			message: "Failed to send email.",
		};
	}
}
