import { User } from "lucide-solid";
import { VendorData } from "~/types/dto";

export default function VendorDataRow(props: { vendor: VendorData }) {
	return (
		<a
			href={`/vendors/${props.vendor.vendorId}/unfulfilled-po`}
			class="block w-full"
			target="_blank"
		>
			<div class="border-b hover:bg-gray-50">
				<div class="container flex items-center px-[1rem] py-2 text-xs md:px-0">
					<User class="text-primary mr-3 h-4 w-4 md:hidden" />
					<div class="text-left md:w-2/6">
						<div class="text-sm font-semibold">{props.vendor.name}</div>
						<div class="text-gray-600 italic">{props.vendor.contactName}</div>
						<div class="mt-2 flex items-center gap-3 md:hidden">
							<div class="rounded-sm bg-black/10 px-2 py-1">
								<span>
									Balance <span class="mr-0.5 font-semibold">Rp</span>
								</span>
								<span class="font-bold">
									{props.vendor &&
									props.vendor.balances &&
									props.vendor.balances.length > 0 ? (
										<p>{props.vendor.balances[0].balance}</p>
									) : (
										"0"
									)}
								</span>
							</div>
							<div class="rounded-sm bg-black/10 px-2 py-1">
								<span>
									Credit <span class="mr-0.5 font-semibold">Rp</span>
								</span>
								<span class="font-bold">
									{props.vendor &&
									props.vendor.credits &&
									props.vendor.credits.length > 0 ? (
										<p>{props.vendor.credits[0].balance}</p>
									) : (
										"0"
									)}
								</span>
							</div>
						</div>
					</div>
					<div class="hidden text-left md:block md:w-1/6">
						{props.vendor.phone}
					</div>
					<div class="hidden text-left md:block md:w-1/6">
						{props.vendor.email}
					</div>
					<div class="hidden text-right md:block md:w-1/6">
						{props.vendor &&
						props.vendor.addresses &&
						props.vendor.addresses.length > 0 ? (
							<div class="flex">
								<p>{props.vendor.addresses[0].address.city}</p>
								<p>{props.vendor.addresses[0].address.state}</p>
							</div>
						) : (
							""
						)}
					</div>
					<div class="hidden text-right md:block md:w-1/6">
						{props.vendor &&
						props.vendor.addresses &&
						props.vendor.addresses.length > 0 ? (
							<p>{props.vendor.addresses[0].address.country}</p>
						) : (
							""
						)}
					</div>
				</div>
			</div>
		</a>
	);
}
