import { eq, inArray } from "drizzle-orm";
import { db } from "~/db";
import { categoriesTable } from "~/db/schema";
import { CategoryData, StructuredCategoryData } from "~/types/dto";
import { fetchCategories } from "../http-services/category-http-service";
import { findOption, updateOption } from "./option-db-service";
import { SetupDataResult, SimpleResponse } from "~/types/response";
import { isStructuredCategoryData } from "~/utils/category-util";
import { inflowConfig } from "~/configs/server-config";

/**
 * Find a category in the database by categoryId.
 */
export async function dbFindCategory(categoryId: string) {
	"use server";

	const results = await db
		.select()
		.from(categoriesTable)
		.where(eq(categoriesTable.categoryId, categoryId))
		.limit(1);

	if (!Array.isArray(results) || results.length === 0) {
		return null;
	}

	return results[0];
}

/**
 * Fetch categories from the database by list of category ids.
 */
export async function dbFetchCategories(props?: { ids?: string[] }) {
	"use server";

	if (props) {
		if (props?.ids && props.ids.length) {
			return await db
				.select()
				.from(categoriesTable)
				.where(inArray(categoriesTable.categoryId, props.ids));
		}
	}

	return await db.select().from(categoriesTable);
}

/**
 * Insert a category to the database.
 */
export async function dbInsertCategory(category: CategoryData) {
	"use server";

	const [header] = await db.insert(categoriesTable).values(category);

	return header.affectedRows > 0 ? category.categoryId : false;
}

/**
 * Update a category in the database.
 */
export async function dbUpdateCategory(
	categoryId: string,
	data: Partial<Omit<CategoryData, "categoryId" | "parentCategory">>,
) {
	"use server";

	const [header] = await db
		.update(categoriesTable)
		.set(data)
		.where(eq(categoriesTable.categoryId, categoryId));

	return header.affectedRows > 0 ? categoryId : false;
}

/**
 * Insert categories to the database.
 */
export async function dbInsertCategories(categories: CategoryData[]) {
	"use server";

	const [header] = await db.insert(categoriesTable).values(categories);

	return header.affectedRows > 0 ? true : false;
}

/**
 * Update categories in the database.
 */
export async function dbUpdateCategories(
	list: {
		categoryId: string;
		data: Partial<Omit<CategoryData, "categoryId" | "parentCategory">>;
	}[],
) {
	"use server";

	const results = await Promise.all(
		list.map(async (item) => {
			const [header] = await db
				.update(categoriesTable)
				.set(item.data)
				.where(eq(categoriesTable.categoryId, item.categoryId));

			return {
				categoryId: item.categoryId,
				status: header.affectedRows > 0,
			};
		}),
	);

	return results.some((result) => result.status);
}

/**
 * Collect categories from Inflow API and insert/udate to the database.
 */
export async function dbCollectCategories(props: {
	after?: string;
}): Promise<SetupDataResult> {
	"use server";

	const count = 100;

	const remoteResponse = await fetchCategories({
		count: count,
		after: props.after,
	});

	if (!remoteResponse.success || !remoteResponse.data) {
		return {
			success: false,
			message: remoteResponse.message,
			meta: {
				args: {
					after: props.after,
				},
				errorCode: "remote_fetch_error",
			},
		};
	}

	if (remoteResponse.data.length > 0) {
		const categoryIds = remoteResponse.data.map(
			(category) => category.categoryId,
		);

		const existingRecords = await dbFetchCategories({ ids: categoryIds });
		const existingRecordIds = existingRecords.map(
			(existingRecord) => existingRecord.categoryId,
		);

		const newInsertList = remoteResponse.data.filter(
			(category) => !existingRecordIds.includes(category.categoryId),
		);
		const newUpdateList = remoteResponse.data.filter((category) =>
			existingRecordIds.includes(category.categoryId),
		);

		let anySuccess = false;

		if (newInsertList.length) {
			const [header] = await db.insert(categoriesTable).values(newInsertList);
			if (header.affectedRows) anySuccess = true;
		}

		if (newUpdateList.length) {
			const updateResult = await dbUpdateCategories(
				newUpdateList.map((item) => {
					return {
						categoryId: item.categoryId,
						data: {
							isDefault: item.isDefault,
							name: item.name,
							parentCategoryId: item.parentCategoryId,
							timestamp: item.timestamp,
						},
					};
				}),
			);

			if (updateResult) anySuccess = true;
		}

		if (!anySuccess) {
			return {
				success: false,
				message: "Failed to insert or update categories",
				meta: {
					args: {
						after: props.after,
					},
					hasMore: remoteResponse.data.length < count ? false : true,
					errorCode: "db_insert_error",
					lastId:
						remoteResponse.data[remoteResponse.data.length - 1].categoryId,
				},
			};
		}
	}

	return {
		success: true,
		message:
			remoteResponse.data.length < count
				? "Finished collecting categories"
				: "Categories collected successfully",
		meta: {
			args: {
				after: props.after,
			},
			hasMore: remoteResponse.data.length < count ? false : true,
			lastId: remoteResponse.data.length
				? remoteResponse.data[remoteResponse.data.length - 1].categoryId
				: undefined,
		},
	};
}

/**
 * Make structured categories from flat categories.
 */
export async function dbMakeStructuredCategories(): Promise<SimpleResponse> {
	"use server";

	const flatCategories = await dbFetchCategories();

	const topLevelCategories: StructuredCategoryData[] = [];

	for (const category of flatCategories) {
		if (!category.parentCategoryId) {
			topLevelCategories.push(category);
		}
	}

	for (const topLevelCategory of topLevelCategories) {
		topLevelCategory.children = dbCollectChildrenCategories(
			topLevelCategory.categoryId,
			flatCategories,
		);
	}

	const result = await updateOption({
		name: "structured_categories",
		value: topLevelCategories,
	});

	return {
		success: result ? true : false,
		message: result
			? "Structured categories created successfully"
			: "Failed to create structured categories",
	};
}

/**
 * Recursively collect children categories of a given parent category.
 */
function dbCollectChildrenCategories(
	parentCategoryId: string,
	flatCategories: CategoryData[],
) {
	"use server";

	const childrenCategories: StructuredCategoryData[] = [];

	for (const flatCategory of flatCategories) {
		if (flatCategory.parentCategoryId !== parentCategoryId) {
			continue;
		}

		const childCategory: StructuredCategoryData = {
			...flatCategory,
			children: dbCollectChildrenCategories(
				flatCategory.categoryId,
				flatCategories,
			),
		};

		childrenCategories.push(childCategory);
	}

	return childrenCategories;
}

export async function dbGetStructuredCategories() {
	"use server";

	const option = await findOption("structured_categories");
	if (!option || !Array.isArray(option.value)) return null;

	const structuredCategories: StructuredCategoryData[] = [];

	for (const item of option.value) {
		if (isStructuredCategoryData(item)) {
			structuredCategories.push(item);
		}
	}

	return structuredCategories;
}

export async function dbGetSalesProductStructuredCategories() {
	"use server";

	const structuredCategories = await dbGetStructuredCategories();
	if (!structuredCategories) return null;

	for (const structuredCategory of structuredCategories) {
		if (
			structuredCategory.categoryId !== inflowConfig().inventoryCategoryId ||
			!structuredCategory.children ||
			!structuredCategory.children.length
		) {
			continue;
		}

		/**
		 * At this point, the structuredCategory is the "Inventory" category.
		 * Let's loop its children and check against sales order category id.
		 */
		for (const childCategory of structuredCategory.children) {
			if (childCategory.categoryId === inflowConfig().salesProductCategoryId) {
				return childCategory;
			}
		}
	}

	return null;
}

export async function isSalesProductSubCategory(
	categoryId: string,
	salesProductCategory?: StructuredCategoryData | null,
) {
	"use server";

	if (!salesProductCategory) {
		salesProductCategory = await dbGetSalesProductStructuredCategories();
		if (!salesProductCategory) return false;
	}

	if (categoryId === salesProductCategory.categoryId) {
		return true;
	}

	const children = salesProductCategory.children ?? [];

	for (const salesProductSubCategory of children) {
		if (salesProductSubCategory.categoryId === categoryId) {
			return true;
		}

		if (
			!salesProductSubCategory.children ||
			!salesProductSubCategory.children.length
		) {
			continue;
		}

		if (await isSalesProductSubCategory(categoryId, salesProductSubCategory)) {
			return true;
		}
	}

	return false;
}
