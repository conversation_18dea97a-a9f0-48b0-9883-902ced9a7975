import { AlignJustify, ChevronRight, Loader } from "lucide-solid";
import { Show, createSignal } from "solid-js";
import { ProductData } from "~/types/dto";
import { formatCurrency, formatQuantity } from "~/utils/formatting-util";

export interface ProductDataRowProps {
	product: ProductData;
	onViewFormsButtonClick?: (product: ProductData) => void;
}

export default function ProductDataRow(props: ProductDataRowProps) {
	const [onClicked, setOnClicked] = createSignal(false);

	return (
		<div>
			<button
				onClick={() => setOnClicked(!onClicked())}
				class="w-full border-b px-[1rem] py-1 hover:bg-stone-100 active:scale-75 md:px-0"
			>
				<a
					class="flex items-center md:container"
					href={`/products/${props.product.productId}/`}
				>
					<div class="flex w-full items-center md:w-2/6">
						<div class="w-[20%] md:w-14">
							{props.product &&
							props.product.images &&
							props.product.images.length > 0 ? (
								<img
									src={props.product.images[0].largeUrl}
									alt=""
									class="-z-10 rounded-sm"
								/>
							) : (
								<img
									src="https://demofree.sirv.com/nope-not-here.jpg"
									alt=""
									class="-z-10 rounded-sm"
								/>
							)}
						</div>
						<div class="w-[70%] pl-5 text-left md:w-full">
							<p
								class="text-sm font-semibold"
								// text ellipsis ...
								style="display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;"
							>
								{props.product.name}
							</p>
							<i class="text-xs uppercase">{props.product.sku}</i>
							<div class="flex">
								<div class="rounded-sm bg-gray-200 px-8 md:hidden">
									{/* when none return "0" */}
									{props.product &&
									props.product.inventoryLines &&
									props.product.inventoryLines.length > 0 ? (
										<p>
											{formatQuantity(
												props.product.inventoryLines[0].quantityOnHand,
											)}
										</p>
									) : (
										"0"
									)}
								</div>
							</div>
						</div>
					</div>
					<div class="hidden w-full items-center md:flex md:w-1/6">
						<div class="mr-2 hidden items-center rounded-full bg-blue-600/10 px-2 font-medium text-blue-600 md:flex">
							<AlignJustify class="" />
							{props.product.category && (
								<i
									class="uppercasel ml-1 text-xs"
									style="display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;"
								>
									{props.product.category.name}
								</i>
							)}
						</div>
					</div>
					<div class="hidden w-1/6 items-center md:flex">
						<div class="">
							<p>{props.product.sku}</p>
						</div>
					</div>
					<div class="hidden items-center md:flex">
						<div class="rounded-sm bg-gray-200 px-8">
							{/* when none return "0" */}
							{props.product &&
							props.product.inventoryLines &&
							props.product.inventoryLines.length > 0 ? (
								<p>
									{formatQuantity(
										props.product.inventoryLines[0].quantityOnHand,
									)}
								</p>
							) : (
								"0"
							)}
						</div>
					</div>
					<div class="ml-auto text-right font-bold">
						<ChevronRight class="text-x mb-5 ml-auto md:hidden" />
						{props.product &&
						props.product.prices &&
						props.product.prices.length > 0 ? (
							<p>
								{formatCurrency({
									amount: props.product.prices[0].unitPrice,
									symbol:
										props.product.prices[0].pricingScheme?.currency.symbol ??
										"$",
								})}
							</p>
						) : (
							"0"
						)}
					</div>
				</a>
			</button>
			<Show when={onClicked()}>
				<div class="fixed top-0 left-0 z-50 flex h-screen w-screen bg-black/50 filter backdrop-blur-xs">
					<Loader class="m-auto h-20 w-20 animate-spin text-white" />
				</div>
			</Show>
		</div>
	);
}
