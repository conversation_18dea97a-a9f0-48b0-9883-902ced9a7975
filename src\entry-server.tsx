import { createHand<PERSON>, StartServer } from "@solidjs/start/server";
import { configDotenv } from "dotenv";

export default createHandler(() => {
	configDotenv();

	return (
		<StartServer
			document={({ assets, children, scripts }) => (
				<html lang="en">
					<head>
						<meta charset="utf-8" />
						<meta
							name="viewport"
							content="width=device-width, initial-scale=1"
						/>

						<title>Multay One</title>

						<link
							rel="shortcut icon"
							href="/images/multay-icon.png"
							type="image/png"
						/>

						<link rel="preconnect" href="https://fonts.googleapis.com" />
						<link
							rel="preconnect"
							href="https://fonts.gstatic.com"
							crossOrigin=""
						/>
						<link
							href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:ital@0;1&display=swap"
							rel="stylesheet"
						/>

						<link
							href="https://fonts.cdnfonts.com/css/myriad-pro"
							rel="stylesheet"
						/>

						{assets}
					</head>
					<body>
						<div id="app">{children}</div>
						{scripts}
					</body>
				</html>
			)}
		/>
	);
});
