import { ProductData } from "~/types/dto";

export interface ProductDataRowProps {
	product: ProductData;
	onClick?: (data: ProductData) => void;
}

export default function ProductDataRow(props: ProductDataRowProps) {
	return (
		<>
			<div
				class="grid cursor-pointer grid-cols-3 border-b px-4 py-2 text-sm text-black/75 hover:bg-gray-50"
				onClick={() => props.onClick?.(props.product)}
			>
				<div class="col-span-2 font-medium">{props.product.name}</div>
				<div class="">{props.product.sku}</div>
			</div>
		</>
	);
}
