import ky from "ky";
import { VendorListResponse } from "~/types/response";

export async function clientFetchVendors(props: {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	keyword?: string;
	filters?: Record<string, any>;
}): Promise<VendorListResponse> {
	let apiUrl = `/api/vendors?includeCount=${props.includeCount ?? false}`;

	if (props.count) {
		apiUrl += `&count=${props.count}`;
	}

	if (props.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props.keyword) {
		apiUrl += `&keyword=${props.keyword}`;
	}

	if (props.filters) {
		apiUrl += `&filters=${JSON.stringify(props.filters)}`;
	}

	try {
		const response = await ky.get(apiUrl).json<VendorListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: "Failed to load more vendors",
		};
	}
}
