import {
	mysqlTable,
	primaryKey,
	bigint,
	varchar,
	datetime,
	json,
	boolean,
	unique,
	index,
	text,
} from "drizzle-orm/mysql-core";
import { sql } from "drizzle-orm";

export const categoriesTable = mysqlTable(
	"categories",
	{
		id: bigint({ mode: "number", unsigned: true }).autoincrement().notNull(),
		categoryId: varchar("category_id", { length: 90 }).notNull(),
		isDefault: boolean("is_default").default(false).notNull(),
		name: varchar({ length: 70 }).notNull(),
		parentCategoryId: varchar("parent_category_id", { length: 90 }),
		timestamp: varchar({ length: 35 }).default("").notNull(),
	},
	(table) => [
		primaryKey({ columns: [table.id], name: "categories_id" }),
		unique("category_id").on(table.categoryId),
	],
);

export const customersTable = mysqlTable(
	"customers",
	{
		id: bigint({ mode: "number", unsigned: true }).autoincrement().notNull(),
		customerId: varchar("customer_id", { length: 90 }).notNull(),
		email: varchar({ length: 255 }).notNull(),
		isActive: boolean("is_active").default(false).notNull(),
		name: varchar({ length: 255 }).notNull(),
		contactName: varchar("contact_name", { length: 100 }).notNull(),
		phone: varchar({ length: 50 }).default("").notNull(),
		website: varchar({ length: 255 }).default("").notNull(),
		defaultBillingAddressId: varchar("default_billing_address_id", {
			length: 90,
		})
			.default("")
			.notNull(),
		defaultCarrier: varchar("default_carrier", { length: 100 })
			.default("")
			.notNull(),
		defaultLocationId: varchar("default_location_id", { length: 90 })
			.default("")
			.notNull(),
		defaultPaymentMethod: varchar("default_payment_method", {
			length: 100,
		})
			.default("")
			.notNull(),
		defaultPaymentTermsId: varchar("default_payment_terms_id", {
			length: 90,
		})
			.default("")
			.notNull(),
		defaultSalesRep: varchar("default_sales_rep", { length: 100 })
			.default("")
			.notNull(),
		defaultSalesRepTeamMemberId: varchar("default_sales_rep_team_member_id", {
			length: 90,
		})
			.default("")
			.notNull(),
		defaultShippingAddressId: varchar("default_shipping_address_id", {
			length: 90,
		})
			.default("")
			.notNull(),
		fax: varchar({ length: 50 }).default("").notNull(),
		pricingSchemeId: varchar("pricing_scheme_id", { length: 90 })
			.default("")
			.notNull(),
		discount: varchar({ length: 100 }).default("").notNull(),
		taxingSchemeId: varchar("taxing_scheme_id", { length: 90 })
			.default("")
			.notNull(),
		lastModifiedById: varchar("last_modified_by_id", { length: 90 })
			.default("")
			.notNull(),
		remarks: varchar({ length: 300 }).default("").notNull(),
		taxExemptNumber: varchar("tax_exempt_number", { length: 100 })
			.default("")
			.notNull(),
		timestamp: varchar({ length: 50 }).default("").notNull(),
		customFields: json("custom_fields"),
		addresses: json(),
		balances: json(),
		credits: json(),
		defaultBillingAddress: json("default_billing_address"),
		defaultLocation: json("default_location"),
		defaultPaymentTerms: json("default_payment_terms"),
		defaultSalesRepTeamMember: json("default_sales_rep_team_member"),
		defaultShippingAddress: json("default_shipping_address"),
		lastModifiedBy: json("last_modified_by"),
		orderHistory: json("order_history"),
		pricingScheme: json("pricing_scheme"),
		taxingScheme: json("taxing_scheme"),
	},
	(table) => [
		index("contact_name").on(table.contactName),
		index("is_active").on(table.isActive),
		index("name").on(table.name),
		primaryKey({ columns: [table.id], name: "customers_id" }),
		unique("customer_id").on(table.customerId),
	],
);

export const logsTable = mysqlTable(
	"logs",
	{
		id: bigint({ mode: "number", unsigned: true }).autoincrement().notNull(),
		date: datetime({ mode: "string" })
			.default(sql`(CURRENT_TIMESTAMP)`)
			.notNull(),
		type: varchar({ length: 7 }).default("log").notNull(),
		content: json(),
	},
	(table) => [
		index("date").on(table.date),
		index("type").on(table.type),
		primaryKey({ columns: [table.id], name: "logs_id" }),
	],
);

export const optionsTable = mysqlTable(
	"options",
	{
		id: bigint({ mode: "number", unsigned: true }).autoincrement().notNull(),
		createdAt: datetime("created_at", { mode: "string" })
			.default(sql`(CURRENT_TIMESTAMP)`)
			.notNull(),
		updatedAt: datetime("updated_at", { mode: "string" }).default(
			sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`,
		),
		name: varchar({ length: 255 }).notNull(),
		value: json(),
	},
	(table) => [primaryKey({ columns: [table.id], name: "options_id" })],
);

export const pricingSchemesTable = mysqlTable(
	"pricing_schemes",
	{
		id: bigint({ mode: "number", unsigned: true }).autoincrement().notNull(),
		pricingSchemeId: varchar("pricing_scheme_id", { length: 90 }).notNull(),
		name: varchar({ length: 255 }).notNull(),
		currencyId: varchar("currency_id", { length: 90 }).default("").notNull(),
		isActive: boolean("is_active").default(false).notNull(),
		isDefault: boolean("is_default").default(false).notNull(),
		isTaxInclusive: boolean("is_tax_inclusive").default(false).notNull(),
		timestamp: varchar({ length: 50 }).default("").notNull(),
		currency: json(),
		productPrices: json("product_prices"),
	},
	(table) => [
		index("is_active").on(table.isActive),
		index("name").on(table.name),
		primaryKey({ columns: [table.id], name: "pricing_schemes_id" }),
		unique("pricing_scheme_id").on(table.pricingSchemeId),
	],
);

export const salesProductsTable = mysqlTable(
	"sales_products",
	{
		id: bigint({ mode: "number", unsigned: true }).autoincrement().notNull(),
		productId: varchar("product_id", { length: 90 }).notNull(),
		sku: varchar({ length: 90 }).notNull(),
		name: varchar({ length: 300 }).notNull(),
		description: varchar({ length: 400 }).notNull(),
		isActive: boolean("is_active").default(false).notNull(),
		width: varchar({ length: 15 }).default("").notNull(),
		height: varchar({ length: 15 }).default("").notNull(),
		length: varchar({ length: 15 }).default("").notNull(),
		weight: varchar({ length: 15 }).default("").notNull(),
		autoAssembler: boolean("auto_assembler").default(false).notNull(),
		barcode: varchar({ length: 30 }).notNull(),
		categoryId: varchar("category_id", { length: 90 }).default("").notNull(),
		defaultImageId: varchar("default_image_id", { length: 90 })
			.default("")
			.notNull(),
		hsTariffNumber: varchar("hs_tariff_number", { length: 150 })
			.default("")
			.notNull(),
		includeQuantityBuildable: boolean("include_quantity_buildable")
			.default(false)
			.notNull(),
		isManufacturable: boolean("is_manufacturable").default(false).notNull(),
		itemType: varchar("item_type", { length: 70 }).default("").notNull(),
		lastModifiedById: varchar("last_modified_by_id", { length: 90 })
			.default("")
			.notNull(),
		lastModifiedDateTime: varchar("last_modified_date_time", { length: 50 })
			.default("")
			.notNull(),
		lastVendorId: varchar("last_vendor_id", { length: 90 })
			.default("")
			.notNull(),
		originCountry: varchar("origin_country", { length: 100 })
			.default("")
			.notNull(),
		remarks: text().notNull(),
		standardUomName: varchar("standard_uom_name", { length: 255 })
			.default("")
			.notNull(),
		timestamp: varchar({ length: 35 }).default("").notNull(),
		totalQuantityOnHand: varchar("total_quantity_on_hand", {
			length: 15,
		}).notNull(),
		trackSerials: boolean("track_serials").default(false).notNull(),
		customFields: json("custom_fields"),
		category: json(),
		cost: json(),
		defaultImage: json("default_image"),
		images: json(),
		defaultPrice: json("default_price"),
		inventoryLines: json("inventory_lines"),
		itemBoms: json("item_boms"),
		lastModifiedBy: json("last_modified_by"),
		lastVendor: json("last_vendor"),
		prices: json(),
		productCustomFieldLabels: json("product_custom_field_labels"),
		productOperations: json("product_operations"),
		purchasingUom: json("purchasing_uom"),
		reorderSettings: json("reorder_settings"),
		salesUom: json("sales_uom"),
		taxCodes: json("tax_codes"),
		vendorItems: json("vendor_items"),
	},
	(table) => [
		index("is_active").on(table.isActive),
		index("name").on(table.name),
		index("sku").on(table.sku),
		primaryKey({ columns: [table.id], name: "sales_products_id" }),
		unique("product_id").on(table.productId),
	],
);
