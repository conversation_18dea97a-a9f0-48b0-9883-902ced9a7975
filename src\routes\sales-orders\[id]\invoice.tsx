import { Show } from "solid-js";
import BankAndAccountDetail from "~/components/BankAndAccountDetail";
import InvoiceHeader from "~/components/heading/InvoiceHeader";
import { findSalesOrder } from "~/services/http-services/sales-order-service";
import InvoiceTable from "~/components/tables/InvoiceTable";
import { findSomeCustomProductDataList } from "~/services/http-services/custom-product-data-service";
import { CustomProductData, SalesOrderOptionData } from "~/types/dto";
import { findPaymentTerm } from "~/services/http-services/payment-terms-service";
import { inflowConfig } from "~/configs/server-config";
import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { findOption } from "~/services/db-services/option-db-service";
import { isSalesOrderOptionData } from "~/utils/object-utils";

const getPageData = query(async (salesOrderId: string) => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const salesOrderResponse = await findSalesOrder(salesOrderId);
	const salesOrder = salesOrderResponse.data;
	const customProductDataCollection: Record<string, CustomProductData> = {};

	if (salesOrderResponse.success && salesOrderResponse.data) {
		const productLines = salesOrderResponse.data.lines ?? [];
		const productIds: string[] = [];

		for (const line of productLines) {
			productIds.push(line.productId);
		}

		const id = salesOrderResponse.data?.customerId ?? "";

		function findSomeCustomProductDataListResponse() {
			if (id === "731e777d-8ef1-4d13-a77c-54a8842dcdc7") {
				return findSomeCustomProductDataList({
					customerId: "1caa0847-b862-44a6-ab7f-a605ea8b67da",
					productIds: productIds,
				});
			} else {
				return findSomeCustomProductDataList({
					customerId: id,
					productIds: productIds,
				});
			}
		}

		const customProductDataListResponse =
			await findSomeCustomProductDataListResponse();

		if (customProductDataListResponse.success) {
			const customProductDataList = customProductDataListResponse.data ?? [];

			for (const customProductData of customProductDataList) {
				customProductDataCollection[customProductData.productId] =
					customProductData;
			}
		}
	}

	const defaultPaymentTermResponse = await findPaymentTerm(
		inflowConfig().defaultInvoicePaymentTermsId,
	);

	const optionResult = await findOption(`sales_order_${salesOrderId}`);

	let salesOrderOptions: SalesOrderOptionData | undefined = undefined;

	if (optionResult && isSalesOrderOptionData(optionResult.value)) {
		const optionValue = optionResult.value;

		salesOrderOptions = {
			pol: optionValue.pol,
			stampedForms: optionValue.stampedForms,
		};
	}

	return {
		salesOrder: salesOrder,
		options: salesOrderOptions,
		customProductDataCollection: customProductDataCollection,
		defaultPaymentTerm: defaultPaymentTermResponse.data,
		incomeCategoryId: inflowConfig().incomeCategoryId,
	};
}, "invoicePageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function InvoicePage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	return (
		<div class="w-[1280px] px-4 py-12 lg:container lg:w-auto print:w-auto print:px-0 print:py-0">
			<Show
				when={
					pageData() &&
					pageData()?.salesOrder &&
					pageData()?.customProductDataCollection
				}
				fallback="Loading data..."
			>
				<InvoiceHeader
					salesOrder={pageData()?.salesOrder!}
					defaultPaymentTerm={pageData()?.defaultPaymentTerm}
				/>
				<InvoiceTable
					salesOrder={pageData()?.salesOrder!}
					customProductDataCollection={pageData()?.customProductDataCollection!}
					defaultPaymentTerm={pageData()?.defaultPaymentTerm}
					incomeCategoryId={pageData()?.incomeCategoryId ?? ""}
					options={pageData()?.options}
				/>
			</Show>
			<BankAndAccountDetail />
		</div>
	);
}
