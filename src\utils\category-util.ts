import {
	CategoryData,
	GroupedProductsByCategoryData,
	ProductData,
	StructuredCategoryData,
} from "~/types/dto";

export function recursivelyFindMatchingCategory(
	searchCategoryId: string,
	categoryStack: StructuredCategoryData,
): StructuredCategoryData | undefined {
	if (categoryStack.categoryId === searchCategoryId) {
		return categoryStack;
	}

	if (categoryStack.children && categoryStack.children.length > 0) {
		for (const childCategory of categoryStack.children) {
			const matchingCategory = recursivelyFindMatchingCategory(
				searchCategoryId,
				childCategory,
			);

			if (matchingCategory) {
				return matchingCategory;
			}
		}
	}

	return undefined;
}

export function groupProductsByCategory(
	category: StructuredCategoryData,
	products: ProductData[],
): GroupedProductsByCategoryData[] {
	const groupedProductsByCategory: GroupedProductsByCategoryData[] = [];

	for (const directChildCategory of category.children ?? []) {
		groupedProductsByCategory.push({
			categoryId: directChildCategory.categoryId,
			name: directChildCategory.name,
			products: products.filter((product) => {
				const matchedCategory = recursivelyFindMatchingCategory(
					product.categoryId,
					directChildCategory,
				);

				return matchedCategory !== undefined;
			}),
		});
	}

	return groupedProductsByCategory;
}

export function structuredCategoryToCategoryData(
	structuredCategory: StructuredCategoryData,
): CategoryData {
	return {
		categoryId: structuredCategory.categoryId,
		isDefault: structuredCategory.isDefault,
		name: structuredCategory.name,
		parentCategory: structuredCategory.parentCategory,
		parentCategoryId: structuredCategory.parentCategoryId,
		timestamp: structuredCategory.timestamp,
	};
}

export function isCategoryData(obj: unknown): obj is CategoryData {
	if (obj && obj instanceof Object && !Array.isArray(obj)) {
		if (
			"categoryId" in obj &&
			typeof obj.categoryId === "string" &&
			"isDefault" in obj &&
			typeof obj.isDefault === "boolean" &&
			"name" in obj &&
			typeof obj.name === "string" &&
			"parentCategoryId" in obj &&
			(typeof obj.parentCategoryId === "string" ||
				obj.parentCategoryId === null) &&
			"timestamp" in obj &&
			typeof obj.timestamp === "string"
		) {
			return true;
		}
	}

	return false;
}

export function isStructuredCategoryData(
	obj: unknown,
): obj is StructuredCategoryData {
	if (isCategoryData(obj)) {
		return true;
	}

	if (obj && obj instanceof Object && !Array.isArray(obj)) {
		if (
			"categoryId" in obj &&
			typeof obj.categoryId === "string" &&
			"isDefault" in obj &&
			typeof obj.isDefault === "boolean" &&
			"name" in obj &&
			typeof obj.name === "string" &&
			"parentCategoryId" in obj &&
			(typeof obj.parentCategoryId === "string" ||
				obj.parentCategoryId === null) &&
			"timestamp" in obj &&
			typeof obj.timestamp === "string" &&
			"children" in obj &&
			(Array.isArray(obj.children) || obj.children === undefined)
		) {
			return true;
		}
	}

	return false;
}
