import { Show } from "solid-js";
import { VendorData } from "~/types/dto";

export default function VendorBankingDetail(props: { vendor?: VendorData }) {
	if (!props.vendor) {
		return <></>;
	}

	const customFields = props.vendor.customFields;

	const bankName = customFields.custom2 ?? "";
	const bankAddress = customFields.custom3 ?? "";
	const accountNumber = customFields.custom1 ?? "";
	const swiftCode = customFields.custom6 ?? "";
	const beneficiaryName = customFields.custom4 ?? "";

	return (
		<section class="pt-36 text-center text-xs font-medium print:mt-10 print:pt-0 print:text-[8px] print:leading-[8px]">
			<div class="mb-2">
				<p class="mb-1 opacity-50">BANK DETAIL :</p>
				<p>{bankName}</p>

				<Show when={bankAddress}>
					<p>{bankAddress}</p>
				</Show>

				<Show when={swiftCode}>
					<p>SWIFT CODE : {swiftCode}</p>
				</Show>
			</div>
			<div>
				<p class="mb-1 opacity-50">ACCOUNT DETAILS :</p>
				<p>{beneficiaryName}</p>
				<p>Account Number : {accountNumber}</p>
			</div>
		</section>
	);
}
