import { Show, createSignal } from "solid-js";
import { isServer } from "solid-js/web";
import { SalesOrderData } from "~/types/dto";
import { Mail, Printer, Stamp } from "lucide-solid";
import SalesEmailForm from "./SalesEmailForm";
interface SalesActionButtonsProps {
	id: string;
	form: string;
	formName: string;
	salesOrder: SalesOrderData;
	onStamp?: () => void;
	onXlsxExport?: (
		formRef?: HTMLFormElement,
		dataFieldRef?: HTMLInputElement,
	) => void;
}

export default function SalesActionButtons(props: SalesActionButtonsProps) {
	const [openEmailForm, setOpenEmailForm] = createSignal(false);

	const buttonClass =
		"text-white group-hover:text-yellow-400 transition-colors duration-200 ease-in-out hover:scale-110 mx-auto";

	function handleStamp() {
		if (isServer) return;
		props.onStamp?.();
	}

	function handlePrint() {
		if (isServer) return;
		window.print();
	}

	function handleEmail() {
		setOpenEmailForm(true);
	}

	let formRef: HTMLFormElement | undefined;
	let dataFieldRef: HTMLInputElement | undefined;

	function hasStamp() {
		return props.form === "invoice" || props.form === "packing-list";
	}

	return (
		<form ref={formRef} method="post" target="_blank">
			<input type="hidden" name="data" ref={dataFieldRef} />
			<div class="fixed right-5 bottom-5 z-10 flex flex-col space-y-2 rounded-full bg-orange-800/75 px-3 py-4 print:hidden">
				<Show when={hasStamp()}>
					<button
						type="button"
						class="group"
						onClick={handleStamp}
						title="Stamp on/off"
					>
						<Stamp class={buttonClass} size={20} />
					</button>
				</Show>
				<button
					type="button"
					class={`group ${hasStamp() ? "border-t border-white/50 pt-1.5" : ""}`}
					onClick={handlePrint}
					title="Print"
				>
					<Printer class={buttonClass} size={20} />
				</button>
				<button
					type="button"
					class="group border-t border-white/50 pt-1.5"
					title="Send Email"
					onClick={handleEmail}
				>
					<Mail class={buttonClass} size={20} />
				</button>
				{/* <button
          type="button"
          class="group border-t border-white/50 pt-1.5"
          title="Export to Excel"
          onClick={() => props.onXlsxExport?.(formRef, dataFieldRef)}
        >
          <SiMicrosoftexcel class={buttonClass} />
        </button> */}
			</div>
			<Show when={openEmailForm()}>
				<div class="fixed inset-0 z-20 flex items-center justify-center bg-black/50">
					<div class="m-auto rounded-sm bg-gray-100">
						<div>
							<SalesEmailForm
								onCancel={() => setOpenEmailForm(false)}
								onSend={() => setOpenEmailForm(false)}
								salesOrder={props.salesOrder}
								form={props.form}
								id={props.id}
								formName={props.formName}
							/>
						</div>
					</div>
				</div>
			</Show>
		</form>
	);
}
