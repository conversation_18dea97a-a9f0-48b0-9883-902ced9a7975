import { StructuredCategoryData } from "~/types/dto";
import CollectionGalleryHeader from "./CollectionGalleryHeader";
import { For } from "solid-js";
import CollectionGalleryGridItemSimple from "./CollectionGalleryGridItemSimple";

export default function CollectionGalleryGrid(props: {
	collectionsRoot: {
		text: string;
		slug: string;
	};
	collection: StructuredCategoryData;
	class?: string;
}) {
	function makeTitle(name: string) {
		const parentCollectionName = props.collection.name.replace(
			"Collection",
			"",
		);

		if (name === "Chair & Bench") {
			return `${name}`;
		}

		return `${name} ${parentCollectionName}`;
	}

	return (
		<div class={`relative container ${props.class ?? ""}`}>
			<CollectionGalleryHeader
				collectionsRoot={props.collectionsRoot}
				category={props.collection}
			/>
			<div class="w-full sm:mt-12 sm:grid sm:grid-cols-2 sm:place-items-center sm:gap-5 sm:px-[1rem] md:grid-cols-3 lg:grid-cols-5 2xl:px-0">
				<For each={props.collection.children}>
					{(childCollection) => {
						return (
							<CollectionGalleryGridItemSimple
								collectionName={makeTitle(childCollection.name)}
								linkUrl={`/${props.collectionsRoot.slug}/${props.collection.categoryId}/${childCollection.categoryId}/`}
							/>
						);
					}}
				</For>
			</div>
		</div>
	);
}
