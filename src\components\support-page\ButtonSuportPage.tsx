import { useLocation } from "@solidjs/router";
import { JSXElement } from "solid-js";

interface ButtonProps {
	text: string;
	icon: JSXElement;
	url: string;
	onNotife?: boolean;
	amount?: number;
	location?: string;
}

export default function ButtonSupportPage(props: ButtonProps) {
	const location = useLocation();

	return (
		<a
			href={props.url}
			// class="mt-2 flex text-sm font-semibold text-black/50 hover:text-black"
			// when location same as props.location text color is black
			class={`mt-2 flex text-sm font-semibold ${
				location.pathname === props.location ? "text-black" : "text-black/50"
			} hover:text-black`}
		>
			{props.icon}
			<span class="mr-auto ml-2">{props.text}</span>
			<span class="ml-auto">
				<span
					//  class="ml-auto justify-center rounded-full bg-blue-500 py-1 px-2 text-center text-xs text-white">
					class={`justify-center rounded-full bg-blue-500 ${
						props.onNotife ? "block" : "hidden"
					} px-1.5 py-0.5 text-center text-xs font-light text-white`}
				>
					{props.amount}
				</span>
			</span>
		</a>
	);
}
