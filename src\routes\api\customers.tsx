import { APIEvent } from "@solidjs/start/server";
import { URL } from "url";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { fetchCustomers } from "~/services/http-services/customer-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function GET({ request, params }: APIEvent) {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to load customers",
			},
			401,
		);
	}

	const urlObject = new URL(request.url);
	const searchParams = urlObject.searchParams;

	const after = searchParams.get("after");
	const before = searchParams.get("before");
	const keyword = searchParams.get("keyword");

	const response = await fetchCustomers({
		after: after ?? undefined,
		before: before ?? undefined,
		sortDesc: false,
		keyword: keyword ?? undefined,
	});

	return makeJsonResponse(response);
}
