import {
	BadgeDollarSign,
	ClipboardList,
	Package,
	Quote,
	ShoppingCart,
} from "lucide-solid";
import ViewFormButton from "./ViewFormButton";

export interface ViewFormButtonListProps {
	salesOrderId?: string;
}

export default function ViewFormButtonList(props: ViewFormButtonListProps) {
	return (
		<div class="grid items-center justify-between gap-3 sm:grid-cols-2 md:grid-cols-3">
			<ViewFormButton
				url={`/sales-orders/${props.salesOrderId}/sales-quote/`}
				labelText="Sales Quote"
				icon={Quote}
			/>

			<ViewFormButton
				url={`/sales-orders/${props.salesOrderId}/sales-order/`}
				labelText="Sales Order"
				icon={ShoppingCart}
			/>

			<ViewFormButton
				url={`/sales-orders/${props.salesOrderId}/packing-list/`}
				labelText="Packing List"
				icon={Package}
			/>

			<div class="grid grid-cols-2 gap-3 sm:col-span-2 md:col-span-3">
				<ViewFormButton
					url={`/sales-orders/${props.salesOrderId}/invoice/`}
					labelText="Invoice"
					icon={BadgeDollarSign}
					class=""
				/>
				<ViewFormButton
					url={`/sales-orders/${props.salesOrderId}/job-order/`}
					labelText="Job Order"
					icon={ClipboardList}
					class=""
				/>
			</div>
		</div>
	);
}
