import { LayoutGrid, Server, Smile, SquareActivity } from "lucide-solid";
import { For } from "solid-js";
import ButtonSupportPage from "~/components/support-page/ButtonSuportPage";
import DashboardSupport from "~/components/support-page/DashboardSupport";

export default function Tickets() {
	const dataLink = [
		{
			text: "Dashboard",
			icon: <LayoutGrid />,
			url: "/support",
		},

		{
			text: "Activites",
			icon: <SquareActivity />,
			url: "/support/activites",
			notif: true,
			amount: 2,
		},
		{
			text: "Tickets",
			icon: <Server />,
			url: "/support/tickets",
			notif: true,
			amount: 10,
		},
		{
			text: "User",
			icon: <Smile />,
			url: "/support/user",
			
		},
	];

	return (
		<div class="relative bg-black/10">
			{/* navigation start */}
			<div class="relative ml-5 h-screen w-48">
				<h1 class="pt-10 text-3xl font-bold">Support</h1>
				<div class="mt-5">
					<For each={dataLink}>
						{(data) => (
							<ButtonSupportPage
								text={data.text}
								icon={data.icon}
								url={data.url}
								onNotife={data.notif}
								amount={data.amount}
								location={data.url}
							/>
						)}
					</For>
				</div>
				<div class="fixed bottom-5 flex items-center">
					<img
						src="https://images.ctfassets.net/h6goo9gw1hh6/2sNZtFAWOdP1lmQ33VwRN3/24e953b920a9cd0ff2e1d587742a2472/1-intro-photo-final.jpg?w=1200&h=992&fl=progressive&q=70&fm=jpg"
						alt=""
						class="h-8 w-8 rounded-full border border-black/50 object-cover"
					/>
					<p class="ml-2 text-sm font-medium">Mulyadi Sanjaya</p>
				</div>
			</div>
			{/* navigation end */}
			<div class="fixed right-0 top-0 h-screen w-9/12 overflow-hidden rounded-tl-3xl border-l-2 border-black/20 bg-white">
				<DashboardSupport />
			</div>
		</div>
	);
}
