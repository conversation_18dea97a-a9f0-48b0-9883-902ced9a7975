import { For, Show } from "solid-js";
import {
	formatCurrency,
	formatDiscount,
	formatQuantity,
	formatWeight,
} from "~/utils/formatting-util";
import SalesActionButtons from "../sales-orders/SalesActionButtons";
import { isServer } from "solid-js/web";
import FootNote from "../sales-orders/FootNote";
import { QuoteAndOrderTableProps } from "./SalesQuoteTable";

export default function SalesOrderTable(props: QuoteAndOrderTableProps) {
	let totalQuantity = 0;
	let totalHeight = 0;
	let totalWidth = 0;
	let totalLength = 0;
	let totalM3 = 0;
	let totalCBM = 0;

	const hasAnyCustomProductData =
		Object.keys(props.customProductDataCollection).length > 0;

	const productLines = props.salesOrder.lines ?? [];

	const hasAnyDiscount = productLines.some((line) => {
		const discount: number = Number(line.discount.value);

		if (discount > 0) {
			return true;
		}

		return false;
	});

	function formatPrice(amount: string | number): string {
		return formatCurrency({
			amount: amount,
			symbol: props.salesOrder.currency?.symbol,
			thousandsSeparator: props.salesOrder.currency?.thousandsSeparator,
			decimalSeparator: props.salesOrder.currency?.decimalSeparator,
			decimalDigits: props.salesOrder.currency?.decimalPlaces,
		});
	}

	const firstThSharedClass =
		"flex items-center flex-wrap justify-center border-x border-black p-1 font-medium";
	const thSharedClass =
		"flex items-center flex-wrap justify-center border-r border-black p-1 font-medium";

	const firstTdSharedClass = "border-x border-black p-1 print:p-0.5 xl:p-2";
	const tdSharedClass = "border-r border-black p-1 print:p-0.5 xl:p-2";

	function handleXlsxExport(
		formRef?: HTMLFormElement,
		dataFieldRef?: HTMLInputElement,
	) {
		if (isServer) return;
		if (!formRef || !dataFieldRef) return;
		formRef.action = "/api/xlsx/sales-order";

		const postData = {
			salesOrder: props.salesOrder,
			customProductDataCollection: props.customProductDataCollection,
		};

		const jsonContent = JSON.stringify(postData);

		dataFieldRef.value = jsonContent;

		setTimeout(() => {
			formRef.submit();
		}, 50);
	}

	return (
		<div class="text-xs print:text-[9px]">
			<table class="mt-5 w-full table-fixed border-y border-black">
				<thead class="text-center">
					<tr class="flex w-full flex-wrap items-stretch border-b border-black bg-gray-100">
						<th class={`w-[21%] ${firstThSharedClass}`}>Multay Product</th>
						<Show when={hasAnyCustomProductData}>
							<th class={`w-[17%] ${thSharedClass}`}>Customer Product</th>
						</Show>

						<th
							class={`${
								hasAnyCustomProductData ? "w-[13%]" : "w-[30%]"
							} ${thSharedClass}`}
						>
							Picture *)
						</th>

						<th class={`${thSharedClass} w-[6%]`}>
							Quantity
							<br />
							(pcs)
						</th>
						<th class={`${thSharedClass} w-[5%]`}>H</th>
						<th class={`${thSharedClass} w-[5%]`}>W</th>
						<th class={`${thSharedClass} w-[5%]`}>D</th>
						<th class={`${thSharedClass} w-[5%]`}>M3</th>
						<th class={`${thSharedClass} w-[7%]`}>
							Total
							<br />
							M3
						</th>

						<th
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : " w-[7%]")
							}
						>
							Unit
							<br />
							Price (US $)
						</th>
						<Show when={hasAnyDiscount}>
							<th class={`${thSharedClass} w-[4%]`}>Disc</th>
						</Show>
						<th
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : " w-[9%]")
							}
						>
							Sub
							<br />
							Total (US $)
						</th>
					</tr>
				</thead>

				<tbody class="text-center">
					<For each={props.salesOrder.lines}>
						{(line) => {
							const isIncomeProduct =
								line.product?.categoryId === props.incomeCategoryId;

							const quantity = line.quantity.standardQuantity;
							const height = line.product?.height;
							const discount = line.discount;
							const width = line.product?.length;
							const length = line.product?.width;
							const customFields = line.product?.customFields;
							const quantityNumber = Number(quantity);
							totalQuantity += isIncomeProduct ? 0 : quantityNumber;
							const unitPriceNumber = Number(line.unitPrice);

							const discountNumber = Number(discount.value);
							const discountAmount = discount.isPercent
								? unitPriceNumber * (discountNumber / 100)
								: discountNumber;
							const priceUnitAfterDiscount = unitPriceNumber - discountAmount;
							const priceAfterDiscount =
								priceUnitAfterDiscount * quantityNumber;

							// These are in cm unit.
							const heightNumber = Number(height);
							totalHeight += heightNumber;

							const widthNumber = Number(width);
							totalWidth += widthNumber;
							const lengthNumber = Number(length);
							totalLength += lengthNumber;

							const m3 = line.product?.customFields?.custom3 ?? "";
							const m3Number = Number(m3);

							const subTotalM3 = m3Number * quantityNumber;
							totalM3 += subTotalM3;
							const subTotal = unitPriceNumber * quantityNumber;

							const customProductData =
								props.customProductDataCollection[line.productId];
							return (
								<tr class="font-base flex w-full flex-wrap items-stretch border-b border-black text-gray-700 print:font-light">
									<td
										class={`${firstTdSharedClass} w-[21%] text-left print:leading-[10px]`}
									>
										<span class="text-left uppercase">{line.product?.sku}</span>
										<br />
										<span class="text-left">{line.product?.name}</span>
									</td>

									<Show when={hasAnyCustomProductData}>
										<td
											class={`
                    ${tdSharedClass}
                    w-[17%] text-left`}
										>
											<Show when={customProductData}>
												<Show when={customProductData.customerProductCode}>
													<>
														<span>{customProductData.customerProductCode}</span>
														<br />
													</>
												</Show>
												<span>{customProductData.customerProductName}</span>
											</Show>
										</td>
									</Show>

									<td
										class={`${
											hasAnyCustomProductData
												? "flex w-[13%] "
												: "flex w-[30%] "
										} ${tdSharedClass}`}
										colspan={customProductData ? undefined : "2"}
									>
										<Show
											when={
												line.product &&
												line.product.images &&
												line.product.images.length > 0
											}
										>
											<img
												src={line.product?.images![0]!.largeUrl}
												alt="Product image"
												class={` print:max-w-3/4 m-auto max-h-40 print:max-h-20`}
											/>
										</Show>
									</td>

									<td class={`${tdSharedClass} w-[6%]`}>
										<Show when={!isIncomeProduct}>
											{formatQuantity(quantity)}
										</Show>
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>
										{formatQuantity(height!)}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>
										{formatQuantity(width!)}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>
										{formatQuantity(length!)}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>{formatWeight(m3)}</td>
									<td class={`${tdSharedClass} w-[7%]`}>
										{formatWeight(subTotalM3)}
									</td>
									<td
										class={
											`${tdSharedClass} ` +
											(hasAnyDiscount ? "w-[6%]" : "w-[7%]")
										}
									>
										{formatPrice(line.unitPrice)}
									</td>

									<Show when={hasAnyDiscount}>
										<td class={`${tdSharedClass} w-[4%]`}>
											<Show when={discount.value !== "0.00000"}>
												{formatDiscount(discount.value ?? "")}

												<Show when={discount.isPercent}>
													<span>%</span>
												</Show>
											</Show>
										</td>
									</Show>

									<td
										class={
											`${tdSharedClass} ` +
											(hasAnyDiscount ? "w-[6%]" : "w-[9%]")
										}
									>
										<p>{formatPrice(priceAfterDiscount)}</p>
									</td>
								</tr>
							);
						}}
					</For>
				</tbody>

				<tbody class="text-center">
					<tr class="flex w-full flex-wrap items-stretch text-xs print:text-[9px]">
						<td
							class={`${firstThSharedClass} w-[51%]`}
							colspan={hasAnyCustomProductData ? "3" : "2"}
						>
							Total
						</td>
						<td class={`${thSharedClass} w-[6%]`}>{totalQuantity}</td>
						<td class={`${thSharedClass} w-[5%]`}></td>
						<td class={`${thSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[7%]`}>{formatWeight(totalM3)}</td>
						<td
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : "w-[7%]")
							}
						></td>
						<td
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? " w-[4%]" : "hidden")
							}
						></td>
						<td
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : "w-[9%]")
							}
						>
							{formatPrice(props.salesOrder.total)}
						</td>
					</tr>
				</tbody>
			</table>

			<FootNote />

			{props.salesOrder.orderRemarks && (
				<div class="mt-4">
					<p class="text-md">Remarks:</p>
					<div class="mt-1 w-1/3 border border-black p-1.5 print:p-0.5">
						<p
							class="text-xs text-gray-700 print:text-[8px] print:leading-[10px]"
							style="white-space: pre-line;"
						>
							{props.salesOrder.orderRemarks}
						</p>
					</div>
				</div>
			)}

			<SalesActionButtons
				onXlsxExport={handleXlsxExport}
				form="sales-order"
				formName="Sales Order"
				id={props.salesOrder.salesOrderId}
				salesOrder={props.salesOrder}
			/>
		</div>
	);
}
