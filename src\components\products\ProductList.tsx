import { For, batch, createSignal } from "solid-js";
import { ProductData } from "~/types/dto";
import ProductDataRow from "./ProductDataRow";

export interface ProductListProps {
	products: ProductData[];
	onViewFormsButtonClick?: (Product: ProductData) => void;
}

export default function ProductList(props: ProductListProps) {
	const [products, setProducts] = createSignal(props.products);
	const [isModalOpen, setIsModalOpen] = createSignal(false);
	const [selectedProduct, setSelectedProduct] = createSignal<ProductData>();

	function openModal(product: ProductData) {
		batch(() => {
			setSelectedProduct(product);
			setIsModalOpen(true);
		});
	}

	return (
		<>
			<div class="text pt-32 text-sm">
				<For each={products()}>
					{(product) => (
						<ProductDataRow
							product={product}
							onViewFormsButtonClick={openModal}
						/>
					)}
				</For>
			</div>
		</>
	);
}
