import ky from "ky";
import { inflowConfig, inflowHttpHeader } from "../../configs/server-config";
import { VendorData } from "../../types/dto";
import { VendorListResponse, VendorResponse } from "~/types/response";
import { getErrorMessage } from "~/utils/http-util";

export async function fetchVendors(props?: {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	keyword?: string;
	filters?: Record<string, any>;
}): Promise<VendorListResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;
	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? inflowConfig().loadMorePerPage;

	// Can be included: balances,addresses,credits,currency,lastModifiedBy,taxingScheme,vendorItems
	const include = props?.include ?? "addresses";

	let apiUrl = `${baseApiUrl}/vendors?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.keyword) {
		apiUrl += `&filter[smart]=${props.keyword}`;
	}

	if (props?.filters) {
		for (const [key, value] of Object.entries(props.filters)) {
			const val = value.toString();
			apiUrl += `&filter[${key}]=${val}`;
		}
	}

	try {
		const vendors = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json<VendorData[]>();

		return {
			success: true,
			message: "Vendors fetched successfully",
			data: vendors,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findVendor(
	id: string,
	args?: {
		include?: string;
	},
): Promise<VendorResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;

	// Can be included: balances,addresses,credits,currency,lastModifiedBy,taxingScheme,vendorItems
	const include = args?.include ?? "addresses";

	try {
		const vendor = await ky
			.get(`${baseApiUrl}/vendors/${id}?include=${include}`, {
				headers: inflowHttpHeader(),
			})
			.json<VendorData>();

		return {
			success: true,
			message: "Vendors fetched successfully",
			data: vendor,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
