import { query } from "@solidjs/router";
import { getRequestURL } from "vinxi/http";
import { AppUrlData } from "~/types/misc";

export const getAppUrlData = query(async (): Promise<AppUrlData> => {
	"use server";

	const urlObject = getRequestURL();
	const baseUrl = urlObject.origin;
	const baseApiUrl = `${baseUrl}/api`;

	return {
		baseUrl,
		baseApiUrl,
		currentUrl: urlObject.href,
	};
}, "appUrlData");
