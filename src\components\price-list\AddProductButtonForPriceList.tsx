import { Show, createSignal } from "solid-js";
import { ProductData } from "~/types/dto";
import LoadingSpinner from "../LoadingSpinner";
import { Plus, X } from "lucide-solid";

export default function AddProductButtonForPriceList(props: {
	product?: ProductData;
	onSelectProductClick?: () => void;
}) {
	const [isModalOpen, setIsModalOpen] = createSignal(false);
	const [isSaving, setIsSaving] = createSignal(false);

	return (
		<>
			<div class="fixed right-4 bottom-4 sm:right-10 sm:bottom-10">
				<button
					type="button"
					class="inline-flex items-center rounded-full bg-[#45827D] px-3 py-3 text-sm text-white active:scale-95 sm:py-2"
					onClick={() => setIsModalOpen(true)}
				>
					<Plus class="h-5 w-5 sm:mr-2" />
					<span class="hidden sm:block">Add a product</span>
				</button>
			</div>
			<Show when={isModalOpen()}>
				<section class="fixed top-0 left-0 z-10 flex h-full w-full items-center justify-center bg-black/50">
					<div class="relative rounded-lg bg-white sm:w-1/2 lg:w-1/3">
						<h3 class="relative rounded-tl-lg rounded-tr-lg bg-gray-100 px-4 py-4 text-lg font-medium text-gray-900">
							New price list product
							<button
								class="absolute right-4 cursor-pointer rounded-full bg-black px-2 py-2 text-sm text-white"
								onClick={() => {
									setIsModalOpen(false);
								}}
							>
								<X />
							</button>
						</h3>
						<div class="px-4 py-4">
							<div class="mb-4">
								<label
									for="product"
									class="block text-sm font-medium text-gray-700"
								>
									Product
								</label>
								<input
									type="text"
									name="product"
									id="product"
									class="mt-1 block w-full cursor-pointer rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
									placeholder="Select a product"
									onClick={props.onSelectProductClick}
									value={props.product?.name ?? ""}
									readOnly
								/>
							</div>
							<div class="flex justify-end">
								<button class="mr-2 inline-flex items-center rounded-md border px-3 py-2 text-sm font-medium text-slate-700/50 active:scale-95 sm:py-2">
									<span class="">Cancel</span>
								</button>
								<button
									class="inline-flex items-center rounded-md bg-[#45827D] px-3 py-2 text-sm text-white active:scale-95 sm:py-2"
									onClick={() => {
										setIsModalOpen(false);
									}}
								>
									<Show when={isSaving()} fallback={<span class="">Add</span>}>
										<LoadingSpinner
											colorClass="text-white"
											iconClass="h-4 w-4"
										/>
									</Show>
								</button>
							</div>
						</div>
					</div>
				</section>
			</Show>
		</>
	);
}
