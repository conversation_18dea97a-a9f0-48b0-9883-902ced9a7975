import { Printer } from "lucide-solid";

export interface ViewFormButtonProps {
	url: string;
	labelText: string;
	icon: typeof Printer;
	class?: string;
}

export default function ViewFormButton(props: ViewFormButtonProps) {
	return (
		<a
			href={props.url}
			target="_blank"
			class={`text-md bg-primary hover:bg-primary/50 inline-flex items-center rounded-sm px-6 py-2 text-sm font-semibold text-white hover:shadow-lg md:px-3 md:text-xs ${props.class}`}
		>
			<props.icon class="ml-auto" size={20} />
			<span class="mr-auto ml-2">{props.labelText}</span>
		</a>
	);
}
