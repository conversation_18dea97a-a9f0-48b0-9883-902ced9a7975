import {
	BaseResponse,
	JobOrderCustomColumnListResponse,
	JobOrderCustomColumnResponse,
	JobOrderProductRemarkListResponse,
	JobOrderProductRemarkResponse,
} from "~/types/response";
import { supabaseConfig } from "~/configs/server-config";
import {
	JobOrderCustomColumnData,
	JobOrderProductRemarksData,
} from "~/types/dto";
import { getSupabaseServer } from "./supabase-service";
import { maybeParseJson } from "~/utils/json-utils";

export interface FetchJobOrderProductRemarksProps {
	salesOrderId?: string;
	productIds?: string[];
}

export async function fetchJobOrderProductRemarks(
	props: FetchJobOrderProductRemarksProps,
): Promise<JobOrderProductRemarkListResponse> {
	"use server";

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.jobOrderProductRemarks)
		.select()
		.eq("sales_order_id", props.salesOrderId)
		.in("product_id", props.productIds ?? []);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const dataList: JobOrderProductRemarksData[] = [];

	for (const item of data ?? []) {
		dataList.push({
			id: item.id,
			createdAt: item.created_at,
			updatedAt: item.updated_at,
			teamMemberId: item.team_member_id,
			salesOrderId: item.sales_order_id,
			// item.sales_order_line_id column was a newly added column, so it may not exist.
			salesOrderLineId: item.sales_order_line_id ?? "",
			productId: item.product_id,
			remarks: item.remarks,
		});
	}

	return {
		success: true,
		message: "Successfully fetched job order product remarks.",
		data: dataList,
	};
}

export async function findJobOrderProductRemark(
	salesOrderId: string,
	productId: string,
): Promise<JobOrderProductRemarkResponse> {
	"use server";

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.jobOrderProductRemarks)
		.select()
		.eq("sales_order_id", salesOrderId)
		.eq("product_id", productId);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const item = data?.[0];

	if (!item) {
		return {
			success: false,
			message: "Job order product remark not found.",
		};
	}

	const dataItem: JobOrderProductRemarksData = {
		id: item.id,
		createdAt: item.created_at,
		updatedAt: item.updated_at,
		teamMemberId: item.team_member_id,
		salesOrderId: item.sales_order_id,
		salesOrderLineId: item.sales_order_line_id ?? "",
		productId: item.product_id,
		remarks: item.remarks,
	};

	return {
		success: true,
		message: "Successfully fetched job order product remark.",
		data: dataItem,
	};
}

export async function findJobOrderProductRemarkById(
	id: number,
): Promise<JobOrderProductRemarkResponse> {
	"use server";

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.jobOrderProductRemarks)
		.select()
		.eq("id", id);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const item = data?.[0];

	if (!item) {
		return {
			success: false,
			message: "Job order product remark not found.",
		};
	}

	const dataItem: JobOrderProductRemarksData = {
		id: item.id,
		createdAt: item.created_at,
		updatedAt: item.updated_at,
		teamMemberId: item.team_member_id,
		salesOrderId: item.sales_order_id,
		salesOrderLineId: item.sales_order_line_id ?? "",
		productId: item.product_id,
		remarks: item.remarks,
	};

	return {
		success: true,
		message: "Successfully fetched job order product remark.",
		data: dataItem,
	};
}

export async function createJobOrderProductRemark(
	props: JobOrderProductRemarksData,
): Promise<JobOrderProductRemarkResponse> {
	"use server";

	const insertData = {
		team_member_id: props.teamMemberId,
		sales_order_id: props.salesOrderId,
		sales_order_line_id: props.salesOrderLineId,
		product_id: props.productId,
		remarks: props.remarks,
	};

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.jobOrderProductRemarks)
		.insert(insertData)
		.select()
		.single();

	if (error) {
		return {
			success: false,
			message: `Failed when create job order: ${error.message}`,
		};
	}

	const dto: JobOrderProductRemarksData = {
		id: data.id,
		createdAt: data.created_at,
		updatedAt: data.updated_at,
		teamMemberId: data.team_member_id,
		salesOrderId: data.sales_order_id,
		salesOrderLineId: data.sales_order_line_id ?? "",
		productId: data.product_id,
		remarks: data.remarks,
	};

	return {
		success: true,
		message: "Successfully created job order product remark.",
		data: dto,
	};
}

export async function updateJobOrderProductRemark(
	props: JobOrderProductRemarksData,
): Promise<JobOrderProductRemarkResponse> {
	"use server";

	const updateData = {
		team_member_id: props.teamMemberId,
		sales_order_id: props.salesOrderId,
		sales_order_line_id: props.salesOrderLineId,
		product_id: props.productId,
		remarks: props.remarks,
	};

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.jobOrderProductRemarks)
		.update(updateData)
		.eq("id", props.id)
		.select()
		.single();

	if (error) {
		return {
			success: false,
			message: `Failed when update job order: ${error.message}`,
		};
	}

	const dto: JobOrderProductRemarksData = {
		id: data.id,
		createdAt: data.created_at,
		updatedAt: data.updated_at,
		teamMemberId: data.team_member_id,
		salesOrderId: data.sales_order_id,
		salesOrderLineId: data.sales_order_line_id ?? "",
		productId: data.product_id,
		remarks: data.remarks,
	};

	return {
		success: true,
		message: "Successfully updated job order product remark.",
		data: dto,
	};
}

export async function deleteJobOrderProductRemark(
	id: number,
): Promise<BaseResponse> {
	"use server";

	const { error } = await getSupabaseServer()
		.from(supabaseConfig().table.jobOrderProductRemarks)
		.delete()
		.eq("id", id);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	return {
		success: true,
		message: "Successfully deleted job order product remark.",
	};
}

export async function fetchJobOrderCustomColumns(props: {
	salesOrderId: string;
}): Promise<JobOrderCustomColumnListResponse> {
	"use server";

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.jobOrderCustomColumns)
		.select()
		.eq("sales_order_id", props.salesOrderId);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const dataList: JobOrderCustomColumnData[] = [];

	for (const item of data ?? []) {
		dataList.push({
			id: item.id,
			createdAt: item.created_at,
			updatedAt: item.updated_at,
			teamMemberId: item.team_member_id,
			salesOrderId: item.sales_order_id,
			name: item.name,
			values: maybeParseJson(item.values),
		});
	}

	return {
		success: true,
		message: "Successfully fetched job order custom columns.",
		data: dataList,
	};
}

export async function createJobOrderCustomColumn(props: {
	salesOrderId: string;
	teamMemberId: string;
	name: string;
	values: Record<string, string>;
}): Promise<JobOrderCustomColumnResponse> {
	"use server";

	const insertData = {
		team_member_id: props.teamMemberId,
		sales_order_id: props.salesOrderId,
		name: props.name,
		values: props.values,
	};

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.jobOrderCustomColumns)
		.insert(insertData)
		.select()
		.single();

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const dto: JobOrderCustomColumnData = {
		id: data.id,
		createdAt: data.created_at,
		updatedAt: data.updated_at,
		teamMemberId: data.team_member_id,
		salesOrderId: data.sales_order_id,
		name: data.name,
		values: maybeParseJson(data.values),
	};

	return {
		success: true,
		message: "Successfully inserted job order custom column.",
		data: dto,
	};
}

export async function updateJobOrderCustomColumn(
	postData: JobOrderCustomColumnData,
): Promise<JobOrderCustomColumnResponse> {
	"use server";

	const updateData = {
		name: postData.name,
		values: postData.values,
	};

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.jobOrderCustomColumns)
		.update(updateData)
		.eq("id", postData.id)
		.select()
		.single();

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const dto: JobOrderCustomColumnData = {
		id: data.id,
		createdAt: data.created_at,
		updatedAt: data.updated_at,
		teamMemberId: data.team_member_id,
		salesOrderId: data.sales_order_id,
		name: data.name,
		values: maybeParseJson(data.values),
	};

	return {
		success: true,
		message: "Successfully updated job order custom column.",
		data: dto,
	};
}

export async function deleteJobOrderCustomColumn(
	id: number,
): Promise<BaseResponse> {
	"use server";

	const { error } = await getSupabaseServer()
		.from(supabaseConfig().table.jobOrderCustomColumns)
		.delete()
		.eq("id", id);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	return {
		success: true,
		message: "Successfully deleted job order custom column.",
	};
}
