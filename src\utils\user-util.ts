import { User } from "@supabase/supabase-js";
import { adminEmails } from "~/configs/app-config";
import {
	ProfileData,
	ProfileUpdateData,
	RegistrationData,
} from "~/types/dto";

export function isAdmin(profile?: ProfileData): boolean {
	return profile && profile.role === "administrator" ? true : false;
}

export function isTeamMember(profile?: ProfileData): boolean {
	return profile && profile.role === "team-member" ? true : false;
}

export function isCustomer(profile?: ProfileData): boolean {
	return profile && profile.role === "customer" ? true : false;
}

export function supabaseUserToProfileData(user: User): ProfileData {
	const role =
		user.email && adminEmails.includes(user.email)
			? "administrator"
			: user.user_metadata?.role ?? "";

	return {
		id: user.id,
		email: user.email ?? "",
		role: role,
		firstName: user.user_metadata?.first_name ?? "",
		lastName: user.user_metadata?.last_name ?? "",
		inflowUserId: user.user_metadata?.inflow_user_id,
		phone: user.phone,
		address: user.user_metadata?.address ?? "",
		avatarUrl: user.user_metadata?.avatar_url ?? "",
	};
}

export function makeRegistrationData(formData: FormData): RegistrationData {
	return {
		email: String(formData.get("email")),
		password: String(formData.get("password")),
		role: String(formData.get("role")),
		firstName: String(formData.get("first_name")),
		lastName: String(formData.get("last_name")),
		inflowUserId: String(formData.get("inflow_user_id")),
		phone: String(formData.get("phone")),
		address: String(formData.get("address")),
		avatarUrl: String(formData.get("avatar_url")),
	};
}

export function makeUserUpdateData(formData: FormData): ProfileUpdateData {
	const data: Record<string, any> = {};

	data.id = formData.get("id") ? formData.get("id") : "";

	if (formData.get("email")) {
		data.email = formData.get("email");
	}

	if (formData.get("password")) {
		data.password = formData.get("password");
	}

	if (formData.get("role")) {
		data.role = formData.get("role");
	}

	if (formData.get("first_name")) {
		data.firstName = formData.get("first_name");
	}

	if (typeof formData.get("last_name") === "string") {
		data.lastName = formData.get("last_name");
	}

	if (formData.get("inflow_user_id")) {
		data.inflowUserId = formData.get("inflow_user_id");
	}

	if (formData.get("phone")) {
		data.phone = formData.get("phone");
	}

	if (formData.get("address")) {
		data.address = formData.get("address");
	}

	if (formData.get("avatar_url")) {
		data.avatarUrl = formData.get("avatar_url");
	}

	return {
		id: data.id,
		...data,
	};
}

export function makeSupabaseUpdateAttributes(
	props: ProfileUpdateData,
): Record<string, string | number> {
	const data: Record<string, any> = {};
	const userMetaData: Record<string, string> = {};

	if (props.email) {
		data.email = props.email;
	}

	if (props.password) {
		data.password = props.password;
	}

	if (props.role) {
		userMetaData.role = props.role;
	}

	if (props.firstName) {
		userMetaData.first_name = props.firstName;
	}

	if (props.lastName !== undefined) {
		userMetaData.last_name = props.lastName;
	}

	if (props.inflowUserId) {
		userMetaData.inflow_user_id = props.inflowUserId;
	}

	if (props.phone) {
		data.phone = props.phone;
	}

	if (props.address !== undefined) {
		userMetaData.address = props.address;
	}

	if (props.avatarUrl) {
		userMetaData.avatar_url = props.avatarUrl;
	}

	data.user_metadata = userMetaData;

	return data;
}
