import { For, Show, batch, createSignal, onCleanup, onMount } from "solid-js";
import {
	CustomProductData,
	CustomerData,
	ProductData,
	ProfileData,
} from "~/types/dto";
import { CustomProductDataListResponse } from "~/types/response";
import LoadMore from "~/components/data-loading/LoadMore";
import LoadingSpinner from "~/components/LoadingSpinner";
import CustomProductDataRow from "./CustomProductDataRow";
import { isServer } from "solid-js/web";
import {
	clientDeleteCustomProductData,
	clientFetchCustomProductData,
} from "~/services/client-services/custom-product-data-client-service";
import ProductSelectorPopup from "./ProductSelectorPopup";
import AddNewProductPopup from "./AddNewProductPopup";
import SearchBehavior from "../data-loading/SearchBehavior";
import EditProductPopup from "./EditProductPopup";
import { isAdmin } from "~/utils/user-util";
import { useSearchParams } from "@solidjs/router";
import { Plus, X } from "lucide-solid";

export interface CustomProductDataListWithLoadMoreProps {
	apiUrl: string;
	baseApiUrl: string;
	profile?: ProfileData;
	customer?: CustomerData;
	dataList: CustomProductData[];
	searchField?: HTMLInputElement;
}

export default function CustomProductDataListWithLoadMore(
	props: CustomProductDataListWithLoadMoreProps,
) {
	const [searchParams, setSearchParams] = useSearchParams();
	const [dataList, setDataList] = createSignal(props.dataList);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = createSignal(false);
	const [dataToDelete, setDataToDelete] = createSignal<
		CustomProductData | undefined
	>();

	const [currentPage, setCurrentPage] = createSignal<number>(
		props.dataList.length ? 1 : 0,
	);
	const [doingLoadMore, setDoingLoadMore] = createSignal<boolean>(false);
	const [loadMoreFinished, setLoadMoreFinished] = createSignal<boolean>(false);
	const [editProductOpen, setEditProductOpen] = createSignal<boolean>(false);
	const [openedCustomData, setOpenedCustomData] =
		createSignal<CustomProductData>();
	const [showAddNewForm, setShowAddNewForm] = createSignal(false);
	const [showProductSelector, setShowProductSelector] = createSignal(false);
	const [selectedProduct, setSelectedProduct] = createSignal<
		ProductData | undefined
	>();

	onMount(() => {
		if (!isServer) {
			document.addEventListener("keydown", closeDialogByEscKey);
			document.addEventListener("keydown", handleEscKeyPressed);
		}
	});

	onCleanup(() => {
		if (!isServer) {
			document.removeEventListener("keydown", closeDialogByEscKey);
			document.removeEventListener("keydown", handleEscKeyPressed);
		}
	});

	function openAddNewForm() {
		setShowAddNewForm(true);
	}

	function openEditProduct(data: CustomProductData) {
		batch(() => {
			setOpenedCustomData(data);
			setEditProductOpen(true);
		});
	}

	function closeEditProduct() {
		setEditProductOpen(false);
	}

	function openProductSelector() {
		setShowProductSelector(true);
	}

	function closeAddNewForm() {
		setShowAddNewForm(false);
	}

	function closeProductSelector() {
		setShowProductSelector(false);
	}

	function handleEscKeyPressed(e: KeyboardEvent) {
		if (e.key !== "Escape" && e.key !== "Esc") return;

		if (showProductSelector()) {
			closeProductSelector();
		} else {
			closeAddNewForm();
		}
	}

	function handleProductSelected(product: ProductData) {
		batch(() => {
			setSelectedProduct(product);
			closeProductSelector();
		});
	}

	async function handleAfterDataSaved(data: CustomProductData) {
		batch(() => {
			setSelectedProduct(undefined);

			const newDataList = [data, ...dataList()];
			setDataList(newDataList);

			closeAddNewForm();
		});
	}

	async function handleAfterDataUpdated(data: CustomProductData) {
		batch(() => {
			const newDataList = dataList().map((d) => {
				if (d.id === data.id) return data;
				return d;
			});

			setDataList(newDataList);
			closeEditProduct();
		});
	}

	function openDialog(data: CustomProductData) {
		batch(() => {
			setDataToDelete(data);
			setIsDeleteDialogOpen(true);
		});
	}

	function closeDialog() {
		batch(() => {
			setDataToDelete(undefined);
			setIsDeleteDialogOpen(false);
		});
	}

	function closeDialogByEscKey(e: KeyboardEvent) {
		if (e.key !== "Escape" && e.key !== "Esc") return;
		closeDialog();
	}

	async function handleDeleteDialogConfirmed(data: CustomProductData) {
		setIsDeleteDialogOpen(false);
		if (!dataToDelete()) return;

		const response = await clientDeleteCustomProductData({
			url: props.apiUrl,
			id: data.id,
		});

		if (!response.success) {
			alert(response.message);
			return;
		}

		const newDataList = dataList().filter((d) => d.id !== data.id);

		setDataList(newDataList);
	}

	async function handleLoadMore() {
		if (doingLoadMore() || loadMoreFinished()) return;

		batch(() => {
			setDoingLoadMore(true);
		});

		const response = await fetchDataList();

		handleFetchComplete(response);
	}

	async function fetchDataList(
		keyword?: string,
	): Promise<CustomProductDataListResponse> {
		return await clientFetchCustomProductData({
			url: props.apiUrl,
			customerId: props.customer?.customerId ?? undefined,
			page: currentPage() + 1,
			keyword: keyword,
		});
	}
	async function handleSearch(keyword: string) {
		batch(() => {
			setLoadMoreFinished(false);
			setDoingLoadMore(true);
			setSearchParams({ keyword: keyword });
			setDataList([]);
			setCurrentPage(0);
		});

		const response = await fetchDataList(keyword);

		handleFetchComplete(response);
	}

	function handleFetchComplete(response: CustomProductDataListResponse): void {
		batch(() => {
			setDoingLoadMore(false);

			if (!response.success) {
				alert(response.message);
				return;
			}

			if (!response?.data?.length) {
				setLoadMoreFinished(true);
				return;
			}

			setCurrentPage(currentPage() + 1);
			setDataList(dataList().concat(response.data));
		});
	}

	let sectionRef: HTMLElement | undefined;

	return (
		<>
			<section class="text py-32 text-sm" ref={sectionRef}>
				<div class="container">
					<SearchBehavior
						searchField={props.searchField}
						onSearch={handleSearch}
					/>

					<LoadMore contentRef={sectionRef} onLoadMore={handleLoadMore}>
						<For each={dataList()}>
							{(data) => (
								<CustomProductDataRow
									data={data}
									profile={props.profile}
									onDeleteButtonClick={openDialog}
									onEditButtonClick={openEditProduct}
								/>
							)}
						</For>
					</LoadMore>

					<Show when={doingLoadMore()}>
						<LoadingSpinner class="mt-14" />
					</Show>

					<Show when={isDeleteDialogOpen() && dataToDelete()}>
						<section class="fixed top-0 left-0 flex h-full w-full items-center justify-center bg-black/50">
							<div class="relative rounded-lg bg-white sm:w-1/2 lg:w-1/3">
								<div class="absolute top-0 right-0 m-4"></div>
								<div class="px-4 py-4">
									<h3 class="relative text-lg font-medium text-gray-900">
										Delete data
										<button
											class="absolute -top-6 -right-6 cursor-pointer rounded-full bg-black px-1 py-1 text-white"
											onClick={closeDialog}
										>
											<X size={20} />
										</button>
									</h3>
									<p class="mt-2 text-sm text-gray-500">
										Are you sure you want to delete "
										<span class="font-semibold">
											{dataToDelete()?.customerProductName}
										</span>
										"?
									</p>
									<div class="mt-3 flex justify-end">
										<button
											type="button"
											class="mt-2 rounded-lg bg-red-500 px-4 py-2 text-white hover:bg-red-600"
											onClick={() => {
												handleDeleteDialogConfirmed(dataToDelete()!);
											}}
										>
											Delete
										</button>
										<button
											type="button"
											class="mt-2 ml-2 rounded-lg bg-gray-200 px-4 py-2 text-white hover:bg-gray-300"
											onClick={() => closeDialog()}
										>
											Cancel
										</button>
									</div>
								</div>
							</div>
						</section>
					</Show>
				</div>
			</section>

			<Show when={editProductOpen() && openedCustomData()} fallback={<></>}>
				<EditProductPopup
					baseApiUrl={props.baseApiUrl}
					customer={props.customer}
					existingData={openedCustomData()!}
					onCloseButtonClick={() => setEditProductOpen(false)}
					onCancelButtonClick={() => setEditProductOpen(false)}
					afterDataUpdated={handleAfterDataUpdated}
				/>
			</Show>

			<Show when={showAddNewForm()}>
				<Show
					when={showProductSelector()}
					fallback={
						<AddNewProductPopup
							apiUrl={props.apiUrl ?? ""}
							customer={props.customer}
							product={selectedProduct()}
							onSelectProductClick={openProductSelector}
							onCloseButtonClick={closeAddNewForm}
							onCancelButtonClick={closeAddNewForm}
							afterDataSaved={handleAfterDataSaved}
						/>
					}
				>
					<ProductSelectorPopup
						apiUrl={`${props.baseApiUrl}/products`}
						onBackButtonClick={closeProductSelector}
						onCloseButtonClick={closeProductSelector}
						onProductSelected={handleProductSelected}
					/>
				</Show>
			</Show>

			<Show when={isAdmin(props.profile)}>
				<div class="fixed right-4 bottom-4 sm:right-10 sm:bottom-10">
					<button
						type="button"
						class="inline-flex items-center rounded-full bg-blue-600 px-3 py-3 text-sm text-white active:scale-95 sm:py-2"
						onClick={() => openAddNewForm()}
					>
						<Plus class="h-5 w-5 sm:mr-2" />
						<span class="hidden sm:block">Add custom data</span>
					</button>
				</div>
			</Show>
		</>
	);
}
