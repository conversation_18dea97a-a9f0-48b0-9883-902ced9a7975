import { eq } from "drizzle-orm";
import { db } from "~/db";
import { optionsTable } from "~/db/schema";

/**
 * Find an option record by name.
 */
export async function findOption(name: string) {
	"use server";

	const result = await db
		.select()
		.from(optionsTable)
		.where(eq(optionsTable.name, name))
		.limit(1);

	if (!Array.isArray(result) || result.length === 0) {
		return null;
	}

	return result[0];
}

/**
 * Get an option value by name.
 */
export async function getOption<T>(name: string) {
	"use server";

	const record = await findOption(name);
	if (!record) return record;

	return record.value as T;
}

/**
 * Update option value. If option does not exist, it will be created.
 */
export async function updateOption(props: {
	name: string;
	value:
		| string
		| number
		| boolean
		| null
		| undefined
		| Record<string, any>
		| Array<any>;
}) {
	"use server";

	const record = await findOption(props.name);

	if (record) {
		const [header] = await db
			.update(optionsTable)
			.set({
				value: props.value,
			})
			.where(eq(optionsTable.name, props.name));

		return header.affectedRows > 0 ? record.id : false;
	}

	const [header] = await db.insert(optionsTable).values({
		name: props.name,
		value: props.value,
	});

	return header.affectedRows > 0 ? header.insertId : false;
}
