export function getErrorMessage(response: any): string {
	if (response.message) {
		return response.message;
	}

	if (response.response && response.response.message) {
		return response.response.message;
	}

	const msg = "An error occurred. Please try again later.";

	if (!response.errors || !response.errors.length) {
		return msg;
	}

	const error = response.errors[0];

	if (error.detail) {
		return error.detail;
	}

	if (error.title) {
		return error.title;
	}

	return msg;
}

export function makeJsonResponse(
	response: any,
	status: number = 200,
): Response {
	"use server";

	return new Response(
		typeof response === "string" ? response : JSON.stringify(response),
		{
			status,
			headers: {
				"Content-Type": "application/json",
			},
		},
	);
}
