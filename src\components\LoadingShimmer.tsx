export interface LoadingShimmerProps {
	class?: string;
	colorClass?: string;
}

export default function LoadingShimmer(props: LoadingShimmerProps) {
	return (
		<div class={`loading-shimmer relative overflow-hidden ${props.class}`}>
			<div class="absolute h-full w-full animate-pulse overflow-hidden">
				<div
					class={`relative h-full w-full ${
						props.colorClass ?? "bg-lightgreycolor"
					}`}
				></div>
			</div>
		</div>
	);
}
