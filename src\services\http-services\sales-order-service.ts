import ky from "ky";
import { inflowConfig, inflowHttpHeader } from "../../configs/server-config";
import { SalesOrderData } from "../../types/dto";
import { SalesOrderListResponse, SalesOrderResponse } from "~/types/response";
import { getErrorMessage } from "~/utils/http-util";

export interface FetchSalesOrdersProps {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	keyword?: string;
	filters?: Record<string, any>;
}

export async function fetchSalesOrders(
	props?: FetchSalesOrdersProps,
): Promise<SalesOrderListResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? inflowConfig().loadMorePerPage;
	const include = props?.include ?? "customer,salesRepTeamMember,currency";

	let apiUrl = `${baseApiUrl}/sales-orders?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.keyword) {
		apiUrl += `&filter[smart]=${props.keyword}`;
	}

	if (props?.filters) {
		for (const [key, value] of Object.entries(props.filters)) {
			const val = value.toString();
			apiUrl += `&filter[${key}]=${val}`;
		}
	}

	try {
		const response = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json<SalesOrderData[]>();

		// console.log(
		//   "response on fetchSalesOrders method in sales-order-service.ts: ",
		//   response,
		// );

		return {
			success: true,
			message: "Sales orders fetched successfully",
			data: response,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findSalesOrder(id: string): Promise<SalesOrderResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;
	const currentTimestamp = new Date().getTime();

	const include =
		"lines,lines.product,lines.product.images,customer,customer.defaultSalesRepTeamMember,salesRepTeamMember,paymentTerms,currency";
	const apiUrl = `${baseApiUrl}/sales-orders/${id}?include=${include}&nocache=${currentTimestamp}`;

	try {
		const salesOrder = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json<SalesOrderData | undefined>();

		return {
			success: true,
			message: "Sales order fetched successfully",
			data: salesOrder,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
