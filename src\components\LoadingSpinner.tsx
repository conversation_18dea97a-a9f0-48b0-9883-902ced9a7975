import { Loader2 } from "lucide-solid";

export interface LoadingSpinnerProps {
	class?: string;
	colorClass?: string;
	iconClass?: string;
}

export default function LoadingSpinner(props: LoadingSpinnerProps) {
	return (
		<div
			class={`w-full text-center ${props.colorClass ?? "text-companycolor"} ${
				props.class
			}`}
		>
			<Loader2 class={`mx-auto ${props.iconClass ?? "h-8 w-8"} animate-spin`} />
		</div>
	);
}
