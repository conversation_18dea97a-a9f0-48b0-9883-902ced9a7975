import { action } from "@solidjs/router";
import { SalesOrderOptionData } from "~/types/dto";
import { updateOption } from "~/services/db-services/option-db-service";

export const savePolAction = action(
	async (props: {
		salesOrderId: string;
		options: SalesOrderOptionData | undefined;
		pol: string | undefined;
	}) => {
		"use server";

		const newOptions = props.options ?? {};
		newOptions.pol = props.pol;

		const result = await updateOption({
			name: `sales_order_${props.salesOrderId}`,
			value: newOptions,
		});

		return result ? props.pol : null;
	},
	"save-pol-action",
);
