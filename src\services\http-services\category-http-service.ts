import ky from "ky";
import { inflowConfig, inflowHttpHeader } from "../../configs/server-config";
import { CategoryData } from "../../types/dto";
import { CategoryListResponse, CategoryResponse } from "~/types/response";
import { getErrorMessage } from "~/utils/http-util";

export async function fetchCategories(props?: {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
}): Promise<CategoryListResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? inflowConfig().loadMorePerPage;

	let apiUrl = `${baseApiUrl}/categories?includeCount=${includeCount}&count=${count}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	try {
		const categories = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json<CategoryData[]>();

		return {
			success: true,
			message: "Categories fetched successfully",
			data: categories,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findCategory(id: string): Promise<CategoryResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;

	try {
		const categories = await ky
			.get(`${baseApiUrl}/categories/${id}`, {
				headers: inflowHttpHeader(),
			})
			.json<CategoryData>();

		return {
			success: true,
			message: "Category fetched successfully",
			data: categories,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
