import { createAsync, query, redirect, useSearchParams } from "@solidjs/router";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { For, Show } from "solid-js";
import CollectionGalleryGrid from "~/components/collections-gallery/CollectionGalleryGrid";
import LoadingSpinner from "~/components/LoadingSpinner";
import CollectionGalleryPageHeader from "~/components/collections-gallery/CollectionGalleryPageHeader";
import MasterCollection from "~/components/collections-gallery/MasterCollection";
import { getSearchKeyword } from "~/utils/search-util";
import { dbFetchCollectionCategories } from "~/services/db-services/collection-db-service";

const getPageData = query(async () => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const collections = await dbFetchCollectionCategories();

	return {
		collections,
	};
}, "collectionListPageData");

export default function CollectionListPage() {
	const pageData = createAsync(() => getPageData(), { deferStream: true });
	let searchFieldRef: HTMLInputElement | undefined;

	const [searchParams, setSearchParams] = useSearchParams();

	return (
		<>
			<CollectionGalleryPageHeader
				collectionsRoot={{
					text: "Collections",
					slug: "collections",
				}}
				searchFieldRef={searchFieldRef}
				keyword={getSearchKeyword(searchParams)}
			/>

			<div class="pt-40 pb-8">
				<Show when={!pageData()}>
					<LoadingSpinner class="mt-14" />
				</Show>

				<Show
					when={pageData()?.collections}
					fallback={
						<div class="text-center">
							Failed to load collections, please try again later.
						</div>
					}
				>
					<MasterCollection collectionName="Price List 2024" />

					<For each={pageData()?.collections ?? []}>
						{(collection, index) => (
							<CollectionGalleryGrid
								collectionsRoot={{
									text: "Collections",
									slug: "collections",
								}}
								collection={collection}
								// class={index() === 0 ? "" : "mt-16"}
								class="mt-6 sm:mt-16"
							/>
						)}
					</For>
				</Show>
			</div>

			<footer class="site-footer pt-5"></footer>
		</>
	);
}
