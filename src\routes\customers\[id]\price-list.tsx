import { Show, createSignal } from "solid-js";
import { findSalesOrder } from "~/services/http-services/sales-order-service";
import { findSomeCustomProductDataList } from "~/services/http-services/custom-product-data-service";
import { CustomProductData } from "~/types/dto";
import { findPaymentTerm } from "~/services/http-services/payment-terms-service";
import { inflowConfig } from "~/configs/server-config";
import ProductSelectorPopup from "~/components/custom-product-data/ProductSelectorPopup";
import { isAuthenticated } from "~/services/http-services/auth-service";
import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { getRequestURL } from "vinxi/http";
import { AppUrlData } from "~/types/misc";

const getPageData = query(async (salesOrderId: string) => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const urlObject = getRequestURL();
	const baseUrl = urlObject.origin;

	const urls: AppUrlData = {
		baseUrl: baseUrl,
		baseApiUrl: `${baseUrl}/api`,
		apiUrl: `${baseUrl}/api/custom-product-data`,
		currentUrl: urlObject.href,
	};

	const salesOrderResponse = await findSalesOrder(salesOrderId);

	const salesOrder = salesOrderResponse.data;
	const customProductDataCollection: Record<string, CustomProductData> = {};

	if (salesOrderResponse.success) {
		const productLines = salesOrderResponse.data?.lines ?? [];
		const productIds: string[] = [];

		for (const line of productLines) {
			productIds.push(line.productId);
		}

		const id = salesOrderResponse.data?.salesOrderId ?? "";

		function findSomeCustomProductDataListResponse() {
			if (id === "731e777d-8ef1-4d13-a77c-54a8842dcdc7") {
				return findSomeCustomProductDataList({
					customerId: "1caa0847-b862-44a6-ab7f-a605ea8b67da",
					productIds: productIds,
				});
			} else {
				return findSomeCustomProductDataList({
					customerId: id,
					productIds: productIds,
				});
			}
		}

		const customProductDataListResponse =
			await findSomeCustomProductDataListResponse();

		if (customProductDataListResponse.success) {
			const customProductDataList = customProductDataListResponse.data ?? [];

			for (const customProductData of customProductDataList) {
				customProductDataCollection[customProductData.productId] =
					customProductData;
			}
		}
	}

	const defaultPaymentTermResponse = await findPaymentTerm(
		inflowConfig().defaultInvoicePaymentTermsId,
	);

	return {
		urls: urls,
		salesOrder: salesOrder,
		customProductDataCollection: customProductDataCollection,
		defaultPaymentTerm: defaultPaymentTermResponse.data,
	};
}, "priceListPageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function PriceListPage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	const [selectPopUp, setSelectPopUp] = createSignal(false);

	return (
		<div class="min-h-screen bg-cover bg-fixed bg-center bg-no-repeat text-stone-800">
			<div
				class="fixed z-[-1] h-screen w-screen bg-cover print:hidden"
				style="background-image: url('/images/CollectionsBG.png');"
			></div>
			<div class="mx-auto w-[1280px] py-10 lg:w-auto print:w-full print:px-0 print:py-0">
				{/* <Show
          when={
            pageData() &&
            pageData()?.salesOrder &&
            pageData()?.customProductDataCollection
          }
          fallback="Loading data..."
        >
          <CustomerPriceListTable
            salesOrder={pageData()?.salesOrder!}
            customProductDataCollection={
              pageData()?.customProductDataCollection!
            }
            onSelectProductClick={() => setSelectPopUp(true)}
          />
        </Show> */}
				<Show when={selectPopUp()}>
					<ProductSelectorPopup
						apiUrl={`${pageData()?.urls.apiUrl}/products`}
						onBackButtonClick={() => setSelectPopUp(false)}
						onCloseButtonClick={() => setSelectPopUp(false)}
						onProductSelected={(product) => {
							// console.log("Product selected", product);
							setSelectPopUp(false);
						}}
					/>
				</Show>
			</div>
			<div
				// bg="/images/bungaKiri.png"
				style="background-image: url('/images/PRICELISTSHW-2024.png');"
				class="fixed top-0 right-0 hidden h-full w-1/3 bg-cover bg-right bg-repeat-y print:block"
			/>
			<div
				// bg="/images/bungaKiri.png"
				style="background-image: url('/images/PRICELISTSHW-2024.png');"
				class="fixed top-0 left-0 hidden h-full w-1/3 bg-cover bg-left bg-repeat-y print:block"
			/>
			{/* <div
        // src="/images/bungaKanan.png"
        style="background-image: url('/images/bungaKanan.png');"
        class="absolute left-0 top-0 hidden  h-full print:block"
      /> */}
		</div>
	);
}
