import { action } from "@solidjs/router";
import { LogOut } from "lucide-solid";
import { ProfileData } from "~/types/dto";
import { logout } from "~/services/http-services/auth-service";
import BlockMenu from "./dashboard/BlockMenu";

export interface DashboardProps {
	profile?: ProfileData;
}

const logoutAction = action(logout);

export default function Dashboard(props: DashboardProps) {
	const useLeafMenu = false;

	return (
		<main
			class={`relative flex min-h-screen flex-wrap items-center justify-center py-6 text-center${useLeafMenu ? "bg-linear-to-r from-lime-50 to-lime-200" : ""}`}
		>
			<section class="mx-auto max-w-6xl items-center px-[1rem]">
				{/* <div class="relative mb-12 mt-10 flex items-center justify-center lg:px-32">
					<img src="/images/multay-logo.png" alt="Multay" class="h-20" />
				</div> */}

				{/* <LeafMenu /> */}
				<BlockMenu />

				<form
					action={logoutAction}
					method="post"
					class="relative bottom-[-90px]"
				>
					<button
						type="submit"
						class="mt-7 inline-flex items-center rounded-full border border-red-500 bg-white px-4 py-1 text-sm font-semibold text-red-500 transition-all hover:bg-red-500 hover:text-white"
					>
						Logout <LogOut class="ml-2" size={14} />
					</button>
				</form>
			</section>
		</main>
	);
}
