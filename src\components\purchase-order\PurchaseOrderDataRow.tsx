import dayjs from "dayjs";
import { PurchaseOrderData } from "~/types/dto";
import { formatCurrency, numericStringToNumber } from "~/utils/formatting-util";
import {
	MobileColon,
	FulfilledTag,
	PriceTag,
	QuoteTag,
	UnFullfiledTag,
} from "../tables/tags";
import { Show } from "solid-js";

export interface PurchaseOrderDataRowProps {
	purchaseOrder: PurchaseOrderData;
	onViewFormsButtonClick?: (purchaseOrder: PurchaseOrderData) => void;
}

export default function PurchaseOrderDataRow(props: PurchaseOrderDataRowProps) {
	function formatDate(date: string): string {
		const dateObj = dayjs(date);
		return dateObj.format("MMM DD, YYYY");
	}

	function formatPrice(amount: string | number): string {
		return formatCurrency({
			amount: amount,
			symbol: props.purchaseOrder.currency?.symbol,
			thousandsSeparator: props.purchaseOrder.currency?.thousandsSeparator,
			decimalSeparator: props.purchaseOrder.currency?.decimalSeparator,
			decimalDigits: props.purchaseOrder.currency?.decimalPlaces,
		});
	}

	function handleViewFormsButtonClick() {
		if (props.onViewFormsButtonClick) {
			props.onViewFormsButtonClick(props.purchaseOrder);
		}
	}

	const columnClassName = "mty-col grid grid-cols-2 sm:grid-cols-3 md:block";
	const mobileLabelClassName = "text-gray-500 text-ellipsis md:hidden";
	const dataValueClassName = "text-gray-800 text-ellipsis sm:col-span-2";

	const balanceStr = props.purchaseOrder.balance;
	const balance = numericStringToNumber(balanceStr);

	const totalPriceStr = props.purchaseOrder.total;
	const totalPrice = numericStringToNumber(totalPriceStr);

	const percentage = (balance / totalPrice) * 100;

	return (
		<>
			<div class="flex w-full border-b px-[1rem] py-2 text-left">
				<a
					class="mty-row container block py-2 text-xs font-semibold text-black/75 md:flex md:items-center"
					href={`/purchase-orders/${props.purchaseOrder.purchaseOrderId}`}
					target="_blank"
				>
					<div class={`${columnClassName} font-bold md:w-[20%]`}>
						<span class={mobileLabelClassName}>Order number</span>
						<div class="flex flex-wrap items-center">
							<MobileColon />

							<div class={dataValueClassName + " text-sm font-bold"}>
								{props.purchaseOrder.orderNumber}
							</div>

							<Show when={props.purchaseOrder.isCompleted}>
								<FulfilledTag />
							</Show>

							<Show
								when={props.purchaseOrder.inventoryStatus === "unfulfilled"}
							>
								<UnFullfiledTag />
							</Show>

							<Show when={props.purchaseOrder.isQuote}>
								<QuoteTag />
							</Show>
						</div>
					</div>
					<div class={`${columnClassName} md:w-[14%]`}>
						<span class={mobileLabelClassName}>Vendor</span>
						<div class="flex">
							<MobileColon />
							<span class={dataValueClassName + " w-4/5 text-sm"}>
								{props.purchaseOrder.vendor?.name ??
									props.purchaseOrder.contactName}
							</span>
						</div>
					</div>
					<div class={`${columnClassName} md:w-[14%]`}>
						<span class={mobileLabelClassName}>Order date</span>
						<div class="flex">
							<MobileColon />
							<span class={dataValueClassName + "text-xs"}>
								{props.purchaseOrder.orderDate
									? formatDate(props.purchaseOrder.orderDate)
									: "-"}
							</span>
						</div>
					</div>
					<div class={`${columnClassName} md:w-[14%]`}>
						<span class={mobileLabelClassName}>Requested ship date</span>
						<div class="flex">
							<MobileColon />
							<span class={dataValueClassName + "text-xs"}>
								{props.purchaseOrder.requestShipDate
									? formatDate(props.purchaseOrder.requestShipDate)
									: "-"}
							</span>
						</div>
					</div>
					<div class={`${columnClassName} md:w-[18%]`}>
						<span class={mobileLabelClassName}>Shipping address</span>
						<div class="flex">
							<MobileColon />
							<span class={dataValueClassName + "text-xs"}>
								{props.purchaseOrder.vendorAddress?.address1}
							</span>
						</div>
					</div>
					<div class={`${columnClassName} pr-1 text-left md:w-[11%]`}>
						<span class={mobileLabelClassName}>Total</span>
						<div class="flex">
							<MobileColon />
							<PriceTag
								amount={totalPrice}
								symbol={props.purchaseOrder.currency?.symbol}
								percentage={percentage}
								thousandsSeparator={
									props.purchaseOrder.currency?.thousandsSeparator
								}
								decimalSeparator={
									props.purchaseOrder.currency?.decimalSeparator
								}
								decimalDigits={props.purchaseOrder.currency?.decimalPlaces}
							/>
						</div>
					</div>
					<div class={`${columnClassName} md:w-[9%]`}>
						<span class={mobileLabelClassName}>Balance</span>
						<div class="flex md:justify-end">
							<MobileColon />
							<span class={dataValueClassName + " font-bold"}>
								{props.purchaseOrder.balance ? formatPrice(balance) : "-"}
							</span>
						</div>
					</div>
				</a>
			</div>
		</>
	);
}
