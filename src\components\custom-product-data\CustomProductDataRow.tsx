import { <PERSON><PERSON><PERSON>, Trash } from "lucide-solid";
import { Show } from "solid-js";
import { CustomProductData, ProfileData } from "~/types/dto";

export interface CustomProductDataRowProps {
	data: CustomProductData;
	profile?: ProfileData;
	onDeleteButtonClick?: (data: CustomProductData) => void;
	onEditButtonClick?: (data: CustomProductData) => void;
}

export default function CustomProductDataRow(props: CustomProductDataRowProps) {
	return (
		<div class="items-center border-b px-[1rem] py-2 text-xs hover:bg-gray-100 sm:flex sm:px-0">
			<div class="flex pr-2 sm:w-4/12">
				<span class="w-1/3 text-black/50 sm:hidden">Product</span>
				<span class="mr-1 sm:hidden">:</span>
				<span class="w-2/3 sm:w-full">
					{props.data.product?.name ?? props.data.productName}
				</span>
			</div>
			<div class="flex px-2 sm:w-2/12">
				<span class="w-1/3 text-black/50 sm:hidden">SKU</span>
				<span class="mr-1 sm:hidden">:</span>
				<span class="w-2/3 font-bold sm:w-full">
					{props.data.product?.sku ?? props.data.productSku}
				</span>
			</div>
			<div class="flex pr-2 sm:w-2/12">
				<span class="w-1/3 text-black/50 sm:hidden">Customer product code</span>
				<span class="mr-1 sm:hidden">:</span>
				<span class="w-2/3 font-bold sm:w-full">
					{props.data.customerProductCode}
				</span>
			</div>
			<div class="flex pr-2 sm:w-3/12">
				<span class="w-1/3 text-black/50 sm:hidden">Customer product name</span>
				<span class="mr-1 sm:hidden">:</span>
				<span class="w-2/3 sm:w-full">{props.data.customerProductName}</span>
			</div>

			<Show when={props.profile}>
				<div class="flex justify-end justify-items-end sm:w-1/12">
					<button
						type="button"
						class="mr-2.5 inline-flex h-7 w-7 items-center justify-center rounded-full bg-red-300 text-white hover:bg-red-500 sm:mt-auto"
						onClick={() => props.onDeleteButtonClick?.(props.data)}
					>
						<Trash size={18} />
					</button>

					<button
						type="button"
						class="inline-flex h-7 w-7 items-center justify-center rounded-full bg-blue-300 text-white hover:bg-blue-500 sm:mt-auto"
						onClick={() => props.onEditButtonClick?.(props.data)}
					>
						<SquarePen size={18} />
					</button>
				</div>
			</Show>
		</div>
	);
}
