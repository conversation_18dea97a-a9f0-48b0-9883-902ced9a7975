import { Search } from "lucide-solid";
import { For } from "solid-js";

function SearchBar() {
	return (
		<div class="flex items-center rounded-md bg-black/10 p-1">
			<input
				type="text"
				class="w-full bg-transparent text-sm font-medium outline-hidden"
				placeholder="Search by anything..."
			/>
			<button>
				<Search size={16} />
			</button>
		</div>
	);
}

function TyketType() {
	return (
		<div class="flex items-center rounded-md bg-black/10 p-1">
			<select class="w-full bg-transparent text-sm font-medium outline-hidden">
				<option value="All">All Tickets</option>
			</select>
			{/* <ChevronDown size={16} /> */}
		</div>
	);
}

function Assignee() {
	return (
		<div class="flex items-center rounded-md bg-black/10 p-1">
			<select class="w-full bg-transparent text-sm font-medium outline-hidden">
				<option value="All">All Assignees</option>
			</select>
			{/* <ChevronDown size={16} /> */}
		</div>
	);
}

function Status() {
	return (
		<div class="flex items-center rounded-md bg-black/10 p-1">
			<select class="w-full bg-transparent text-sm font-medium outline-hidden">
				<option value="All">All Status</option>
			</select>
			{/* <ChevronDown size={16} /> */}
		</div>
	);
}

interface ListItemProps {
	id: string;
	fire?: boolean;
	about: string;
	problem: string;
	name: string;
	new?: boolean;
	newText?: string;
	low?: boolean;
	medium?: boolean;
	urgent?: boolean;
}

function ListItem(props: ListItemProps) {
	function getStatus(status: string) {
		if (status === "Low") {
			return "bg-green-500/25 text-green-600 text-xs px-1 rounded-full font-light";
		} else if (status === "Medium") {
			return "bg-blue-500/25 text-blue-600 text-xs px-1 rounded-full font-base";
		} else if (status === "Urgent") {
			return "bg-red-500/25 text-red-700 text-xs px-1 rounded-full font-medium";
		}
		return "";
	}
	return (
		<div class="flex border-b py-2 pr-5 text-sm">
			<div class="relative flex w-24 items-center pl-5 md:w-28 md:pl-7">
				<h4 class="font-bold">{props.id}</h4>
				<img
					src="https://freepngtransparent.com/wp-content/uploads/2023/03/Fire-Png-299.png"
					alt=""
					// class="absolute left-0 top-2 h-4 w-4 rotate-45"
					class={
						props.fire ? "absolute top-2 left-0 h-4 w-4 rotate-45" : "hidden"
					}
				/>
			</div>
			<div class="">
				<p class="font-medium">{props.about}</p>
				<div class="flex items-center text-xs font-medium text-black/50">
					<p>{props.problem}</p>
					<span>.</span>
					<p>{props.name}</p>
				</div>
			</div>
			<div class="ml-2">
				<div
					class={
						props.new
							? "font-base rounded-md bg-blue-500/90 px-1 text-[10px] text-white"
							: "hidden"
					}
				>
					<p>{props.newText}</p>
				</div>
			</div>
			<div class="ml-2">
				<div class={props.low ? getStatus("Low") : "hidden"}>
					<p>Low</p>
				</div>
				<div class={props.medium ? getStatus("Medium") : "hidden"}>
					<p>Medium</p>
				</div>
				<div class={props.urgent ? getStatus("Urgent") : "hidden"}>
					<p>Urgent</p>
				</div>
			</div>
		</div>
	);
}

export default function TiketsSuport() {
	const data = [
		{
			id: "PA32829",
			fire: true,
			about: "Invoicing problem",
			problem: "payment",
			name: "John",
			new: true,
			newText: "New coment",
			urgent: true,
		},
		{
			id: "JL32829",
			about: "Invoicing problem",
			problem: "payment",
			name: "John",
			medium: true,
		},
		{
			id: "PA32829",
			fire: true,
			about: "Invoicing problem",
			problem: "payment",
			name: "John",
			low: true,
		},
	];

	return (
		<div class="py-5">
			<div class="flex items-center space-x-2 px-5 md:px-7">
				<SearchBar />
				<TyketType />
				<Assignee />
				<Status />
			</div>
			<div>
				<div class="mt-5 flex w-full items-center border-b pb-2 text-xs font-medium">
					<p class="w-24 pl-5 md:w-28 md:pl-7">ID</p>
					<p>Description</p>
				</div>
				<div>
					<For each={data}>{(item) => <ListItem {...item} />}</For>
				</div>
			</div>
		</div>
	);
}
