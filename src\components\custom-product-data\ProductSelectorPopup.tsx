import { For, Show, batch, createSignal } from "solid-js";
import { ProductData } from "~/types/dto";
import SearchBehavior from "../data-loading/SearchBehavior";
import LoadingSpinner from "../LoadingSpinner";
import { ProductListResponse } from "~/types/response";
import { clientFetchProducts } from "~/services/client-services/product-client-service";
import LoadMore from "../data-loading/LoadMore";
import ProductDataRow from "./ProductDataRow";
import { ChevronLeft, X } from "lucide-solid";

export interface ProductSelectorPopupProps {
	apiUrl: string;
	onBackButtonClick?: () => void;
	onCloseButtonClick?: () => void;
	onProductSelected?: (product: ProductData) => void;
}

export default function ProductSelectorPopup(props: ProductSelectorPopupProps) {
	const [products, setProducts] = createSignal<ProductData[]>([]);
	const [doingLoadMore, setDoingLoadMore] = createSignal<boolean>(false);
	const [loadMoreFinished, setLoadMoreFinished] = createSignal<boolean>(false);

	async function handleSearch(keyword: string) {
		batch(() => {
			setLoadMoreFinished(false);
			setDoingLoadMore(true);
			setProducts([]);
		});

		const response = await fetchDataList(keyword);

		handleFetchComplete(response);
	}

	async function handleLoadMore() {
		if (doingLoadMore() || loadMoreFinished()) return;

		batch(() => {
			setDoingLoadMore(true);
		});

		const response = await fetchDataList();

		handleFetchComplete(response);
	}

	async function fetchDataList(keyword?: string): Promise<ProductListResponse> {
		const lastProductItem = products().length
			? products()[products().length - 1]
			: undefined;

		return await clientFetchProducts({
			url: props.apiUrl,
			after: lastProductItem?.productId ?? undefined,
			keyword: keyword,
		});
	}

	function handleFetchComplete(response: ProductListResponse): void {
		batch(() => {
			setDoingLoadMore(false);

			if (!response.success) {
				alert(response.message);
				return;
			}

			if (!response?.data?.length) {
				setLoadMoreFinished(true);
				return;
			}

			setProducts(products().concat(response.data));
		});
	}

	let scrollerRef: HTMLDivElement | undefined;
	let contentRef: HTMLDivElement | undefined;
	let searchFieldRef: HTMLInputElement | undefined;

	return (
		<section class="fixed top-0 left-0 z-10 grid h-screen w-screen bg-black/50">
			<div
				class="m-auto overflow-hidden rounded-lg bg-white sm:w-3/4 lg:w-3/4"
				style="height: 80vh"
			>
				<div class="relative flex items-center rounded-tl-lg rounded-tr-lg bg-gray-100 px-4 py-4 text-lg font-medium text-gray-900">
					<button
						type="button"
						class="mr-2 inline-flex items-center rounded-lg bg-gray-200 px-2 py-1 text-sm"
						onClick={props.onBackButtonClick}
					>
						<ChevronLeft class="mr-2 inline-block" />
						Back
					</button>
					Select a product
					<button
						class="absolute right-4 cursor-pointer rounded-full bg-black px-2 py-2 text-sm text-white"
						onClick={props.onCloseButtonClick}
					>
						<X />
					</button>
				</div>
				<div class="px-4 pt-4">
					<input
						ref={searchFieldRef}
						type="text"
						name="product"
						id="product"
						class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
					/>
				</div>

				<div class="mt-2 grid grid-cols-3 border-b px-4 py-2 text-sm font-semibold text-black/75">
					<div class="col-span-2">Product name</div>
					<div class="">Product SKU</div>
				</div>

				<SearchBehavior searchField={searchFieldRef} onSearch={handleSearch} />

				<div ref={scrollerRef} class="h-4/5 overflow-y-scroll pb-10">
					<div ref={contentRef}>
						<LoadMore
							scrollerRef={scrollerRef}
							contentRef={contentRef}
							onLoadMore={handleLoadMore}
						>
							<For each={products()}>
								{(product) => (
									<ProductDataRow
										product={product}
										onClick={props.onProductSelected}
									/>
								)}
							</For>
						</LoadMore>
					</div>

					<Show when={doingLoadMore()}>
						<LoadingSpinner class="mt-10" />
					</Show>
				</div>
			</div>
		</section>
	);
}
