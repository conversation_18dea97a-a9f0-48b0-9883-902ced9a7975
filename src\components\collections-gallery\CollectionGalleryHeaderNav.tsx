import { A } from "@solidjs/router";
import { ChevronRight } from "lucide-solid";
import { Show } from "solid-js";
import { StructuredCategoryData } from "~/types/dto";

export default function CollectionGalleryHeaderNav(props: {
	collectionsRoot: {
		text: string;
		slug: string;
	};
	collection?: StructuredCategoryData;
	subCollection?: StructuredCategoryData;
	lastChildCollection?: StructuredCategoryData;
	keyword?: string;
}) {
	const linkClassName =
		"inline-flex items-center text-sm text-gray-700 hover:text-secondary dark:text-gray-400 dark:hover:text-white";

	const spanClassName =
		"text-sm text-gray-500 dark:text-gray-600 dark:hover:text-white";

	return (
		<nav
			class="mt-2.5 flex items-center justify-center text-center font-sans text-sm"
			aria-label="Breadcrumb"
		>
			<ul class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
				<li class="inline-flex items-center">
					<A href="/" class={linkClassName}>
						Dashboard
					</A>
				</li>

				<li class="ml-1">
					<ChevronRight class="h-3 w-3" />
				</li>

				<li class="ml-1 inline-flex items-center">
					<A href={`/${props.collectionsRoot.slug}/`} class={linkClassName}>
						{props.collectionsRoot.text}
					</A>
				</li>

				<Show when={props.collection}>
					<li class="ml-1">
						<ChevronRight class="h-3 w-3" />
					</li>

					<li class="ml-1 inline-flex items-center">
						<A
							href={`/${props.collectionsRoot.slug}/${props.collection?.categoryId}/`}
							class={linkClassName}
						>
							{props.collection?.name ?? ""}
						</A>
					</li>
				</Show>

				<Show when={props.collection && props.subCollection}>
					<li class="ml-1">
						<ChevronRight class="h-3 w-3" />
					</li>

					<li class="ml-1 inline-flex items-center">
						<A
							href={`/${props.collectionsRoot.slug}/${props.collection?.categoryId}/${props.subCollection?.categoryId}/`}
							class={linkClassName}
						>
							{props.subCollection?.name ?? ""}
						</A>
					</li>
				</Show>

				<Show when={props.lastChildCollection}>
					<li class="ml-1">
						<ChevronRight class="h-3 w-3" />
					</li>

					<li class="ml-1 inline-flex items-center">
						<span class={spanClassName}>
							{props.lastChildCollection?.name ?? ""}
						</span>
					</li>
				</Show>
			</ul>
		</nav>
	);
}
