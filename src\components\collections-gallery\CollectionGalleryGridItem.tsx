import { A } from "@solidjs/router";
import { Show } from "solid-js";

export default function CollectionGalleryGridItem(props: {
	collectionName: string;
	linkUrl: string;
	imageUrl?: string;
	wrapperClass?: string;
	textClass?: string;
	useNativeLink?: boolean;
}) {
	return (
		<div
			class={`w-full overflow-hidden border-b border-b-slate-200 py-2 sm:rounded-lg sm:border sm:py-3 sm:shadow-stone-400 sm:hover:shadow-lg ${props.wrapperClass ?? ""}`}
			style={
				props.imageUrl
					? `background-image: url(/images/corner-leaf-ornament.png); background-size: 20%; background-position: left bottom; background-repeat: no-repeat;`
					: ""
			}
		>
			<h4
				class={`group sm:font-display px-4 text-left text-sm sm:px-3 sm:text-center ${props.textClass ?? "sm:text-lg"} text-gray-500`}
			>
				<Show
					when={props.useNativeLink}
					fallback={<A href={props.linkUrl}>{props.collectionName}</A>}
				>
					<a href={props.linkUrl}>{props.collectionName}</a>
				</Show>
			</h4>

			<Show
				when={props.useNativeLink}
				fallback={
					<A href={props.linkUrl} class="block sm:h-32 md:h-48 lg:h-56 xl:h-60">
						<Show when={props.imageUrl}>
							<img
								src={props.imageUrl}
								class="h-auto w-full transform transition-transform duration-300 ease-in-out hover:scale-105"
							/>
						</Show>
					</A>
				}
			>
				<a href={props.linkUrl} class="block sm:h-32 md:h-48 lg:h-56 xl:h-60">
					<Show when={props.imageUrl}>
						<img
							src={props.imageUrl}
							class="h-auto w-full transform transition-transform duration-300 ease-in-out hover:scale-105"
						/>
					</Show>
				</a>
			</Show>
		</div>
	);
}
