export default function LeafMenuItem(props: {
	linkUrl: string;
	linkText: string;
	position:
		| "top-left"
		| "top-center"
		| "top-right"
		| "bottom-left"
		| "bottom-center"
		| "bottom-right";
}) {
	return (
		<a
			class={`leaf absolute flex items-center justify-center border border-lime-400 text-center no-underline ${props.position} hover:border-green-700`}
			href={props.linkUrl}
		>
			<span class="inline-block font-semibold text-white">
				{props.linkText}
			</span>
		</a>
	);
}
