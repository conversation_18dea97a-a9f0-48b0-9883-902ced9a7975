import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { Show } from "solid-js";
import PurchaseOrderHeader from "~/components/heading/PurchaseOrderHeader";
import PurchaseOrderTable from "~/components/tables/PurchaseOrderTable";
import VendorBankingDetail from "~/components/VendorBankingDetail";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { findPurchaseOrder } from "~/services/http-services/purchase-order-service";

const getPageData = query(async (purchaseOrderId: string) => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const purchaseOrderResponse = await findPurchaseOrder(purchaseOrderId);

	return {
		purchaseOrder: purchaseOrderResponse.data,
	};
}, "purchaseOrderPageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function PurchaseOrderPage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	return (
		<div class="w-[1280px] px-4 py-12 lg:w-auto xl:px-20 print:w-auto print:px-0 print:py-0">
			<Show when={pageData()} fallback="Loading data...">
				<Show
					when={pageData()?.purchaseOrder}
					fallback="Failed to load purchase order"
				>
					<PurchaseOrderHeader purchaseOrder={pageData()?.purchaseOrder!} />
					<PurchaseOrderTable purchaseOrder={pageData()?.purchaseOrder!} />
				</Show>
			</Show>
			<VendorBankingDetail vendor={pageData()?.purchaseOrder?.vendor} />
		</div>
	);
}
