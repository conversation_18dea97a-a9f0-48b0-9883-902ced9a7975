import { A } from "@solidjs/router";
import { Show } from "solid-js";
import { StructuredCategoryData } from "~/types/dto";

export default function CollectionGalleryHeader(props: {
	collectionsRoot: {
		text: string;
		slug: string;
	};
	category: StructuredCategoryData;
	subCategory?: StructuredCategoryData;
}) {
	function makeLinkUrl() {
		if (props.subCategory) {
			return `/${props.collectionsRoot.slug}/${props.category.categoryId}/${props.subCategory.categoryId}/`;
		} else {
			return `/${props.collectionsRoot.slug}/${props.category.categoryId}/`;
		}
	}

	function removeCollectionSuffix(name: string) {
		return name.replace(" Collection", "");
	}

	return (
		<h3 class="relative mb-2 flex flex-wrap items-center justify-start sm:mb-0 sm:justify-center">
			<span class="absolute mt-[3px] block h-[1px] w-full border-b border-stone-200"></span>
			<A
				href={makeLinkUrl()}
				class="font-display hover:text-primary relative inline-block bg-white px-4 text-xl text-stone-600 md:text-4xl"
			>
				<Show
					when={props.subCategory}
					fallback={removeCollectionSuffix(props.category.name)}
				>
					{removeCollectionSuffix(props.subCategory!.name)}
				</Show>
			</A>
		</h3>
	);
}
