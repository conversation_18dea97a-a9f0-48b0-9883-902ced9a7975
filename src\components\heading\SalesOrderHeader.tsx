import { Show } from "solid-js";
import { SalesOrderData } from "~/types/dto";
import { formatDate } from "~/utils/formatting-util";

export interface SalesOrderHeaderProps {
	salesOrder: SalesOrderData;
}

export default function SalesOrderHeader(props: SalesOrderHeaderProps) {
	function formatShipDate(): string {
		const shipDate = props.salesOrder?.requestedShipDate ?? "";
		if (!shipDate) return "";

		const shipDateSplits = shipDate.split("T");
		const shipDateSimple = shipDateSplits[0];

		return formatDate(shipDateSimple);
	}

	return (
		<div class="w-full items-center justify-center">
			<div class="text-[10px] font-medium print:text-[8px]">
				<section class="flex">
					<div class="flex w-1/3 items-center print:w-auto">
						<img src="/images/multay-logo-compact.png" alt="" class="w-24" />
						<div class="">
							<h4 class="font-semibold">PT. MULTAY INTERNATIONAL INDONESIA</h4>
							<div class="text-slate-600">
								<p>Jl. Raya Demak - Kudus KM7</p>
								<p>Trenggulli, Wonosalam, Demak 59571</p>
								<p>Central Java, Indonesia</p>
								<p>email <EMAIL></p>
								<p>phone +62291 4284 309</p>
								<p>www.multay.com</p>
							</div>
						</div>
					</div>
					{/*  */}
					<img
						src="/images/svlk-logo.jpg"
						alt=""
						class="mx-auto h-20 w-20 pt-3"
					/>
					{/*  */}
					<div class="w-1/3">
						<div class="ml-auto w-1/3 print:w-3/6">
							<p class="text-center text-[13px] font-bold">Sales Order</p>
							<div class="mt-1 border border-black p-3 print:p-2">
								<div class="flex">
									<p class="font-light">Order #</p>
									<p class="ml-auto font-semibold">
										{props.salesOrder.orderNumber}
									</p>
								</div>
								<div class="flex">
									<p class="mr-5 font-light">Date</p>
									<p class="ml-auto font-semibold">
										{formatDate(props.salesOrder.orderDate)}
									</p>
								</div>
							</div>
						</div>
					</div>
				</section>
				<section class="mt-7 flex">
					<div class="font-base flex">
						<p class="font-semibold">Billing Address :</p>
						<div class="ml-5 font-medium text-slate-600">
							<p class="font-bold">{props.salesOrder.customer?.name}</p>
							<p>{props.salesOrder.billingAddress.address1}</p>
							<p>{props.salesOrder.billingAddress.address2}</p>
							{/* <p>{props.salesOrder.billingAddress.addressType.value}</p>
              <p>{props.salesOrder.billingAddress.addressType.hasValue}</p> */}
							<p>{props.salesOrder.billingAddress.city}</p>
							<p>{props.salesOrder.billingAddress.country}</p>
							<p>{props.salesOrder.billingAddress.postalCode}</p>
							<p>{props.salesOrder.billingAddress.remarks}</p>
							<p>{props.salesOrder.billingAddress.state}</p>
						</div>
					</div>
					<div class="ml-auto flex">
						<p class="font-semibold">Shipping Address :</p>
						<div class="ml-5 text-slate-600">
							<p class="font-bold">{props.salesOrder.customer?.name}</p>
							<p>{props.salesOrder.shippingAddress.address1}</p>
							<p>{props.salesOrder.shippingAddress.address2}</p>
							{/* <p>{props.salesOrder.shippingAddress.addressType.value}</p>
              <p>{props.salesOrder.shippingAddress.addressType.hasValue}</p> */}
							<p>{props.salesOrder.shippingAddress.city}</p>
							<p>{props.salesOrder.shippingAddress.country}</p>
							<p>{props.salesOrder.shippingAddress.postalCode}</p>
							<p>{props.salesOrder.shippingAddress.remarks}</p>
							<p>{props.salesOrder.shippingAddress.state}</p>
						</div>
					</div>
				</section>
				<section class="mt-7 grid grid-cols-3">
					<div class="flex">
						<div class="mr-2 font-semibold">
							<p>Contact</p>
							<p>Phone</p>
							<p>Email</p>
						</div>
						<div class="ml-12 text-slate-600">
							<p>{props.salesOrder.contactName}</p>
							<p>{props.salesOrder.phone}</p>
							<p>{props.salesOrder.email}</p>
						</div>
					</div>
				</section>
				<section class="mt-7">
					<div class="flex border-t border-l border-black bg-gray-100 text-sm print:text-[8px]">
						<div class="w-[38%] border-r border-black py-1 text-center print:py-0">
							PO Number
						</div>
						<div class="w-[19%] border-r border-black py-1 text-center print:py-0">
							Sales Rep
						</div>
						<div class="w-[20%] border-r border-black py-1 text-center print:py-0">
							Requested Ship Date
						</div>
						<div class="w-[23%] border-r border-black py-1 text-center print:py-0">
							Shipment Terms
						</div>
					</div>
					<div class="flex border-y border-l border-black text-sm opacity-75 print:text-[9px] print:leading-[10px] print:font-light">
						<div class="flex w-[38%] items-center border-r border-black py-1 text-center">
							<p class="m-auto">{props.salesOrder.poNumber}</p>
						</div>
						<div class="flex w-[19%] items-center border-r border-black py-1 text-center">
							<p class="m-auto">{props.salesOrder.salesRepTeamMember?.name}</p>
						</div>
						<div class="flex w-[20%] items-center border-r border-black py-1 text-center">
							<p class="m-auto" data-ship-date={formatShipDate()}>
								{formatShipDate()}
							</p>
						</div>
						<div class="flex w-[23%] items-center border-r border-black py-1 text-center">
							<Show when={props.salesOrder?.paymentTerms}>
								<p class="m-auto">{props.salesOrder.paymentTerms?.name}</p>
							</Show>
						</div>
					</div>
				</section>
			</div>
		</div>
	);
}
