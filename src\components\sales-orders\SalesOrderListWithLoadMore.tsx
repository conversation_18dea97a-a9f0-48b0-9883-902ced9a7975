import { For, Show, batch, createSignal, onCleanup, onMount } from "solid-js";
import { SalesOrderData } from "~/types/dto";
import SalesOrderDataRow from "./SalesOrderDataRow";
import ViewFormButtonList from "./ViewFormButtonList";
import { isServer } from "solid-js/web";
import { SalesOrderListResponse } from "~/types/response";
import LoadMore from "../data-loading/LoadMore";
import LoadingSpinner from "../LoadingSpinner";
import { clientFetchSalesOrders } from "~/services/client-services/sales-order-client-service";
import SearchBehavior from "../data-loading/SearchBehavior";
import { X } from "lucide-solid";
import { useSearchParams } from "@solidjs/router";
import { getSearchKeyword } from "~/utils/search-util";

export interface SalesOrderListWithLoadMoreProps {
	baseApiUrl: string;
	salesOrders: SalesOrderData[];
	searchField?: HTMLInputElement;
	onViewFormsButtonClick?: (salesOrder: SalesOrderData) => void;
}

export default function SalesOrderListWithLoadMore(
	props: SalesOrderListWithLoadMoreProps,
) {
	const [searchParams, setSearchParams] = useSearchParams();
	const [salesOrders, setSalesOrders] = createSignal(props.salesOrders);
	const [isModalOpen, setIsModalOpen] = createSignal(false);

	const [selectedSalesOrder, setSelectedSalesOrder] = createSignal<
		SalesOrderData | undefined
	>();

	const [doingLoadMore, setDoingLoadMore] = createSignal<boolean>(false);
	const [loadMoreFinished, setLoadMoreFinished] = createSignal<boolean>(false);

	onMount(() => {
		if (!isServer) {
			document.addEventListener("keydown", closeModalByEscKey);
		}
	});

	onCleanup(() => {
		if (!isServer) {
			document.removeEventListener("keydown", closeModalByEscKey);
		}
	});

	async function handleLoadMore() {
		if (doingLoadMore() || loadMoreFinished()) return;

		batch(() => {
			setDoingLoadMore(true);
		});

		const response = await fetchDataList(getSearchKeyword(searchParams));

		handleFetchComplete(response);
	}

	async function fetchDataList(
		keyword?: string,
	): Promise<SalesOrderListResponse> {
		const lastSalesOrderItem = salesOrders().length
			? salesOrders()[salesOrders().length - 1]
			: undefined;

		return await clientFetchSalesOrders({
			url: `${props.baseApiUrl}/sales-orders`,
			after: lastSalesOrderItem?.salesOrderId ?? undefined,
			keyword: keyword,
		});
	}

	async function handleSearch(keyword: string) {
		batch(() => {
			setLoadMoreFinished(false);
			setDoingLoadMore(true);
			setSearchParams({ keyword: keyword });
			setSalesOrders([]);
		});

		const response = await fetchDataList(keyword);

		handleFetchComplete(response);
	}

	function handleFetchComplete(response: SalesOrderListResponse): void {
		batch(() => {
			setDoingLoadMore(false);

			if (!response.success) {
				alert(response.message);
				return;
			}

			if (!response?.data?.length) {
				setLoadMoreFinished(true);
				return;
			}

			setSalesOrders(salesOrders().concat(response.data));
		});
	}

	function openModal(salesOrder: SalesOrderData) {
		batch(() => {
			setSelectedSalesOrder(salesOrder);
			setIsModalOpen(true);
		});
	}

	function closeModal() {
		batch(() => {
			setSelectedSalesOrder(undefined);
			setIsModalOpen(false);
		});
	}

	function closeModalByEscKey(e: KeyboardEvent) {
		if (e.key !== "Escape" && e.key !== "Esc") return;
		closeModal();
	}

	let sectionRef: HTMLElement | undefined;

	return (
		<>
			<section class="text pt-36 text-sm md:pt-32" ref={sectionRef}>
				<SearchBehavior
					searchField={props.searchField}
					onSearch={handleSearch}
				/>
				<LoadMore contentRef={sectionRef} onLoadMore={handleLoadMore}>
					<>
						<For each={salesOrders()}>
							{(order) => (
								<SalesOrderDataRow
									salesOrder={order}
									onViewFormsButtonClick={openModal}
								/>
							)}
						</For>
					</>
				</LoadMore>

				<Show when={doingLoadMore()}>
					<LoadingSpinner class="mt-14" />
				</Show>
			</section>

			<Show when={isModalOpen() && selectedSalesOrder()}>
				<div class="bg-primary/50 fixed inset-0 z-50 h-full w-full">
					<div class="relative container flex h-full items-center justify-center">
						<div class="max-w-xl rounded-lg bg-white px-5 py-4 text-center">
							<h3 class="relative mb-4 font-bold">
								View forms
								<button
									class="absolute -top-6 -right-6 cursor-pointer rounded-full bg-black px-1 py-1 text-white"
									onClick={closeModal}
								>
									<X size={20} />
								</button>
							</h3>
							<ViewFormButtonList
								salesOrderId={selectedSalesOrder()?.salesOrderId}
							/>
						</div>
					</div>
				</div>
			</Show>
		</>
	);
}
