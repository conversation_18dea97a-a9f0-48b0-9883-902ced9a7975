import { capabilitiesRolesRelationships } from "~/configs/server-config";

export function canCreateUser(role: string): boolean {
	"use server";
	return capabilitiesRolesRelationships().createUser.includes(role);
}

export function canEditUser(role: string): boolean {
	"use server";
	return capabilitiesRolesRelationships().editUser.includes(role);
}

export function canDeleteUser(role: string): boolean {
	"use server";
	return capabilitiesRolesRelationships().deleteUser.includes(role);
}

export function canCreateSupportTicket(role: string): boolean {
	"use server";
	return capabilitiesRolesRelationships().createSupportTicket.includes(role);
}

export function canEditSupportTicket(role: string): boolean {
	"use server";
	return capabilitiesRolesRelationships().editSupportTicket.includes(role);
}

export function canDeleteSupportTicket(role: string): boolean {
	"use server";
	return capabilitiesRolesRelationships().deleteSupportTicket.includes(role);
}
