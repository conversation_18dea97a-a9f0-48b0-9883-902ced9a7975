import { For } from "solid-js";
import LoadingShimmer from "../LoadingShimmer";
import PriceListActionButtons from "../collections/PriceListActionButtons";

export default function CustomerPriceListTableLoading() {
	// Create mock rows for skeleton loading
	const mockRows = Array.from({ length: 8 }, (_, i) => i);

	// Simulate having custom product data for layout consistency
	const hasAnyCustomProductData = true;

	const thSharedClass = "p-1 font-bold text-center uppercase";
	const tdSharedClass = "p-1 print:p-0.5 xl:p-1.5";

	return (
		<div class="text-md mt-10 text-stone-800 print:text-[10px]">
			<table class="font-table table w-full table-fixed">
				<thead class="border-b border-stone-200 text-center uppercase">
					<tr class="w-full text-center print:text-sm">
						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[16%]" : "w-[24%]"} px-1`}
							rowSpan={2}
						>
							Legacy Home Code
						</th>
						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[18%]" : "w-[30%]"} px-1`}
							rowSpan={2}
						>
							Legacy Home Description
						</th>

						<th class={`${thSharedClass} w-[16%] px-1`} rowSpan={2}>
							<LoadingShimmer class="h-4 w-20 mx-auto" />
						</th>
						<th class={`${thSharedClass} w-[18%] px-1`} rowSpan={2}>
							<LoadingShimmer class="h-4 w-24 mx-auto" />
						</th>

						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[12%]" : "w-[16%]"} px-3`}
							colspan={3}
						>
							Dimensions (cm)
						</th>

						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[6%]" : "w-[8%]"}`}
							rowSpan={2}
						>
							M3
						</th>

						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[7%]" : "w-[11%]"}`}
							rowSpan={2}
						>
							Legacy Home
							<br /> FOB Price USD
							<br />
							FEB 2024
						</th>

						<th
							class={`${thSharedClass} ${hasAnyCustomProductData ? "w-[7%]" : "w-[11%]"}`}
							rowSpan={2}
						>
							<LoadingShimmer class="h-4 w-16 mx-auto mb-1" />
							<br />
							Price Dated
							<div class="flex items-center justify-center mt-1">
								<LoadingShimmer class="h-4 w-20" />
							</div>
						</th>
					</tr>

					<tr class="w-full font-bold print:text-sm">
						<th class={`${thSharedClass} pl-3`}>H</th>
						<th class={`${thSharedClass} px-1.5`}>W</th>
						<th class={`${thSharedClass} pr-3`}>D</th>
					</tr>
				</thead>

				<tbody class="align-baseline">
					<For each={mockRows}>
						{() => (
							<tr class="group relative">
								<td class={`${tdSharedClass}`}>
									<LoadingShimmer class="h-4 w-full" />
								</td>

								<td class={`${tdSharedClass}`}>
									<LoadingShimmer class="h-4 w-full" />
								</td>

								<td class={`${tdSharedClass}`}>
									<LoadingShimmer class="h-4 w-full" />
								</td>

								<td class={`${tdSharedClass}`}>
									<LoadingShimmer class="h-4 w-full" />
								</td>

								<td class={`${tdSharedClass} text-center`}>
									<LoadingShimmer class="h-4 w-12 mx-auto" />
								</td>

								<td class={`${tdSharedClass} text-center`}>
									<LoadingShimmer class="h-4 w-12 mx-auto" />
								</td>

								<td class={`${tdSharedClass} text-center`}>
									<LoadingShimmer class="h-4 w-12 mx-auto" />
								</td>

								<td class={`${tdSharedClass} text-center`}>
									<LoadingShimmer class="h-4 w-16 mx-auto" />
								</td>

								<td class={`${tdSharedClass} text-center`}>
									<LoadingShimmer class="h-4 w-16 mx-auto" />
								</td>

								<td class={`${tdSharedClass} text-center`}>
									<LoadingShimmer class="h-4 w-16 mx-auto" />
								</td>
							</tr>
						)}
					</For>
				</tbody>
			</table>

			<PriceListActionButtons
				title="Price List"
				url="#"
			/>
		</div>
	);
}
