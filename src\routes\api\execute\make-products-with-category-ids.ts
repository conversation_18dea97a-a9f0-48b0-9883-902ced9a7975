import { APIEvent } from "@solidjs/start/server";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { makeJsonResponse } from "~/utils/http-util";
// import collectionCategoriesWithProducts from "../../../../public/internal-assets/collection-categories-with-products.json";
import { writeFileSync } from "fs";

export async function GET({ request, params }: APIEvent) {
  const isLoggedIn = await isAuthenticated();

  if (!isLoggedIn) {
    return makeJsonResponse(
      {
        success: false,
        message: "You don't have permission to access this resource",
      },
      401,
    );
  }

  // const productsWithCategoryIdsJsonFilePath =
  //   "./public/internal-assets/products-with-collection-category-ids.json";

  // const simplifiedProducts: SimplifiedProductData[] = [];

  // collectionCategoriesWithProducts.forEach(
  //   (categoryWithProducts: RawCategoryDataWithProducts) => {
  //     const products = extractProducts(categoryWithProducts);
  //     simplifiedProducts.push(...products);
  //   },
  // );

  // const uniqueUnifiedProducts: Record<string, FlatProductData> = {};

  // // Convert the simplified products (which already flat) to flat products.
  // simplifiedProducts.forEach((product) => {
  //   if (!uniqueUnifiedProducts[product.productId]) {
  //     uniqueUnifiedProducts[product.productId] = {
  //       productId: product.productId,
  //       sku: product.sku,
  //       categoryIds: [product.categoryId],
  //     };
  //   } else {
  //     uniqueUnifiedProducts[product.productId].categoryIds.push(
  //       product.categoryId,
  //     );
  //   }
  // });

  // // Remove duplicates inside categoryIds in uniqueUnifiedProducts list.
  // for (const productId in uniqueUnifiedProducts) {
  //   const product = uniqueUnifiedProducts[productId];
  //   product.categoryIds = Array.from(new Set(product.categoryIds));
  // }

  // writeFileSync(
  //   productsWithCategoryIdsJsonFilePath,
  //   JSON.stringify(uniqueUnifiedProducts, null, 2),
  // );

  // return makeJsonResponse(
  //   {
  //     success: true,
  //     message: "Products with category ids have been written to file",
  //   },
  //   200,
  // );
}

function extractProducts(
  categoryWithProducts: RawCategoryDataWithProducts,
): SimplifiedProductData[] {
  const products: SimplifiedProductData[] = [];

  if (categoryWithProducts.products) {
    categoryWithProducts.products.forEach((product) => {
      if (!product.productId) {
        return;
      }

      products.push({
        productId: product.productId,
        sku: product.sku,
        name: product.name,
        categoryId: categoryWithProducts.categoryId,
      });
    });
  }

  if (categoryWithProducts.children) {
    categoryWithProducts.children.forEach((childCategory) => {
      const childProducts = extractProducts(childCategory);
      products.push(...childProducts);
    });
  }

  return products;
}

interface RawCategoryData {
  categoryId: string;
  isDefault: boolean;
  name: string;
  timestamp: string;
  children?: RawCategoryData[];
}

interface RawCategoryDataWithProducts extends RawCategoryData {
  products?: SimplifiedProductData[];
}

interface SimplifiedProductData {
  productId: string;
  sku: string;
  name: string;
  categoryId: string;
}

interface FlatProductData {
  productId: string;
  sku: string;
  categoryIds: string[];
}
