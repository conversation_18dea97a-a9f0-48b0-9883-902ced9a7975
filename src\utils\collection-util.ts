import {
	ProductCollectionData,
	NestedProductCollectionData,
} from "~/types/dto";

export function toNestedCollectionList(
	flatCollectionList: ProductCollectionData[],
	parentId?: number,
): NestedProductCollectionData[] {
	const nestedCollectionList: NestedProductCollectionData[] = [];

	for (const flatCollection of flatCollectionList) {
		let parentIdMatched = false;

		if (!parentId && !flatCollection.parentId) {
			parentIdMatched = true;
		} else {
			parentIdMatched = flatCollection.parentId === parentId;
		}

		if (!parentIdMatched) {
			continue;
		}

		const nestedCollection = toNestedCollection(flatCollection);

		nestedCollection.children = toNestedCollectionList(
			flatCollectionList,
			flatCollection.id,
		);

		nestedCollectionList.push(nestedCollection);
	}

	return nestedCollectionList;
}

export function toNestedCollection(
	flatCollection: ProductCollectionData,
): NestedProductCollectionData {
	const collection: NestedProductCollectionData = {
		id: flatCollection.id,
		parentId: flatCollection.parentId,
		name: flatCollection.name,
		slug: flatCollection.slug,
		category: flatCollection.category,
		coverImageUrl: flatCollection.coverImageUrl,
		children: [],
	};

	return collection;
}

export function supabaseToCollectionData(data: any): ProductCollectionData {
	return {
		id: data.id ?? 0,
		parentId: data.parent_id ?? 0,
		name: data.name ?? "",
		slug: data.slug ?? "",
		category: data.category ?? "",
		coverImageUrl: data.cover_image_url ?? "",
	};
}
