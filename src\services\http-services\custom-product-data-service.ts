import { supabaseConfig } from "~/configs/server-config";
import {
	CreateCustomProductDataRespose,
	CustomProductDataListResponse,
	DeleteCustomProductDataResponse,
} from "~/types/response";
import { CustomProductData } from "~/types/dto";
import { getSupabaseServer } from "./supabase-service";

export interface FetchCustomProductDataProps {
	customerId?: string;
	page?: number;
	perPage?: number;
	keyword?: string;
}

export async function fetchCustomProductData(
	props: FetchCustomProductDataProps,
): Promise<CustomProductDataListResponse> {
	"use server";

	let perPage = props.perPage || 50;
	let page = props.page || 1;

	const from = (page - 1) * perPage;
	const to = page + perPage;

	const keyword = props.keyword;

	const table = supabaseConfig().table.productCustomData;

	const { data, error } = props.customerId
		? keyword
			? await getSupabaseServer()
					.from(table)
					.select()
					.eq("customer_id", props.customerId)
					// .ilike("customer_product_name", `%${keyword}%`)
					.or(
						`customer_product_name.ilike.%${keyword}%,customer_product_code.ilike.%${keyword}%,product_name.ilike.%${keyword}%,product_sku.ilike.%${keyword}%`,
					)
					.range(from, to)
					.order("product_name", { ascending: true })
			: await getSupabaseServer()
					.from(table)
					.select()
					.eq("customer_id", props.customerId)
					.range(from, to)
					.order("product_name", { ascending: true })
		: keyword
			? await getSupabaseServer()
					.from(table)
					.select()
					.ilike("customer_product_name", `%${keyword}%`)
					.range(from, to)
					.order("product_name", { ascending: true })
			: await getSupabaseServer().from(table).select().range(from, to);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const dataList: CustomProductData[] = [];

	if (data) {
		data.forEach((item: any) => {
			dataList.push({
				id: item.id,
				customerId: item.customer_id,
				customerName: item.customer_name,
				productId: item.product_id,
				productName: item.product_name,
				productSku: item.product_sku || "",
				customerProductCode: item.customer_product_code,
				customerProductName: item.customer_product_name,
			});
		});
	}

	return {
		success: true,
		message: "Custom product data fetched successfully",
		data: dataList,
	};
}

export async function findCustomProductData(id: number) {
	"use server";

	const table = supabaseConfig().table.productCustomData;
	const { data, error } = await getSupabaseServer()
		.from(table)
		.select()
		.eq("id", id);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	return {
		success: true,
		message: "Custom product data fetched successfully",
		data: data,
	};
}

export interface findSomeCustomProductDataProps {
	customerId: string;
	productIds: string[];
}

export async function findSomeCustomProductDataList(
	props: findSomeCustomProductDataProps,
): Promise<CustomProductDataListResponse> {
	"use server";

	const table = supabaseConfig().table.productCustomData;
	const { data, error } = await getSupabaseServer()
		.from(table)
		.select()
		.eq("customer_id", props.customerId)
		.in("product_id", props.productIds);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const dataList: CustomProductData[] = [];

	if (data) {
		data.forEach((item: any) => {
			dataList.push({
				id: item.id,
				customerId: item.customer_id,
				customerName: item.customer_name,
				productId: item.product_id,
				productName: item.product_name,
				productSku: item.product_sku || "",
				customerProductCode: item.customer_product_code,
				customerProductName: item.customer_product_name,
			});
		});
	}

	return {
		success: true,
		message:
			"Specified custom product data list have been fetched successfully",
		data: dataList,
	};
}

export async function createCustomProductData(
	props: CustomProductData,
): Promise<CreateCustomProductDataRespose> {
	"use server";

	const insertData = {
		customer_id: props.customerId,
		customer_name: props.customerName,
		product_id: props.productId,
		product_name: props.productName,
		product_sku: props.productSku,
		customer_product_code: props.customerProductCode,
		customer_product_name: props.customerProductName,
	};

	// console.log("insertData: ", insertData);

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.productCustomData)
		.insert(insertData)
		.select()
		.single();

	// console.log("data: ", data);
	// console.log("error: ", error);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const dto: CustomProductData = {
		id: data.id,
		customerId: data.customer_id,
		customerName: data.customer_name,
		productId: data.product_id,
		productName: data.product_name,
		productSku: data.product_sku || "",
		customerProductCode: data.customer_product_code,
		customerProductName: data.customer_product_name,
	};

	return {
		success: true,
		message: "Custom product data created successfully",
		data: dto,
	};
}

export async function updateCustomProductData(
	props: CustomProductData,
): Promise<CreateCustomProductDataRespose> {
	"use server";

	const updateData = {
		customer_id: props.customerId,
		customer_name: props.customerName,
		product_id: props.productId,
		product_name: props.productName,
		product_sku: props.productSku,
		customer_product_code: props.customerProductCode,
		customer_product_name: props.customerProductName,
	};

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.productCustomData)
		.update(updateData)
		.eq("id", props.id)
		.select()
		.single();

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const dto: CustomProductData = {
		id: data.id,
		customerId: data.customer_id,
		customerName: data.customer_name,
		productId: data.product_id,
		productName: data.product_name,
		productSku: data.product_sku || "",
		customerProductCode: data.customer_product_code,
		customerProductName: data.customer_product_name,
	};

	return {
		success: true,
		message: "Custom product data created successfully",
		data: dto,
	};
}

export async function deleteCustomProductData(
	id: number,
): Promise<DeleteCustomProductDataResponse> {
	"use server";

	const table = supabaseConfig().table.productCustomData;
	const { error } = await getSupabaseServer().from(table).delete().eq("id", id);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	return {
		success: true,
		message: "Custom product data deleted successfully",
	};
}
