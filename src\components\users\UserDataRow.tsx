import { A } from "@solidjs/router";
import { <PERSON><PERSON><PERSON>, Trash, User } from "lucide-solid";
import { ProfileData } from "~/types/dto";

export interface UserDataRowProps {
	user: ProfileData;
	onClick?: (user: ProfileData) => void;
}

export default function UserDataRow(props: UserDataRowProps) {
	return (
		<div class="group border-b hover:bg-gray-50">
			<div class="container flex items-center justify-between px-[1rem] py-2 text-sm md:justify-normal md:px-0">
				<User class="mr-3 h-4 w-4 text-primary md:hidden" />
				<div class="text-left font-semibold md:w-[25%]">
					{props.user.firstName
						? `${props.user.firstName} ${props.user.lastName}`
						: "-"}
				</div>
				<div class="hidden md:block md:w-[20%]">{props.user.phone ?? "-"}</div>
				<p class="hidden md:block md:w-[25%]">{props.user.email}</p>
				<p class="hidden md:block md:w-[15%]">
					<span class="">{props.user.role}</span>
				</p>
				<div class="ml-4 text-right opacity-25 transition-opacity duration-200 group-hover:opacity-100 md:ml-0 md:w-[15%]">
					<A
						href={`/users/${props.user.id}`}
						class="inline-flex h-7 w-7 items-center justify-center rounded-full bg-blue-300 text-xs text-white transition-all duration-200 hover:bg-blue-600"
						title="Edit user"
					>
						<SquarePen size={16} />
					</A>
					<A
						href="/users/delete"
						class="ml-2 inline-flex h-7 w-7 items-center justify-center rounded-full bg-red-300 text-xs text-white transition-all duration-200 hover:bg-red-600"
						title="Delete user"
					>
						<Trash size={16} />
					</A>
				</div>
			</div>
		</div>
	);
}
