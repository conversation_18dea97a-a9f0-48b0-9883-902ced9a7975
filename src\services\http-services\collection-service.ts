import { supabaseConfig } from "~/configs/server-config";
import { getSupabaseServer } from "./supabase-service";
import {
	ProductCollectionResponse,
	ProductCollectionListResponse,
} from "~/types/response";
import { supabaseToCollectionData } from "~/utils/collection-util";

const columnSelect = "id, parent_id, name, slug, category, cover_image_url";

export async function findCollectionBySlug(
	slug: string,
): Promise<ProductCollectionResponse> {
	"use server";

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.productCollections)
		.select(columnSelect)
		.eq("slug", slug)
		.single();

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const collection = supabaseToCollectionData(data);

	return {
		success: true,
		message: `Collection ${slug} found`,
		data: collection,
	};
}

export async function findCollection(id: number) {
	"use server";

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.productCollections)
		.select(columnSelect)
		.eq("id", id)
		.single();

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const collection = supabaseToCollectionData(data);

	return {
		success: true,
		data: collection,
	};
}

export async function fetchCollectionList(): Promise<ProductCollectionListResponse> {
	"use server";

	const { data, error } = await getSupabaseServer()
		.from(supabaseConfig().table.productCollections)
		.select(columnSelect);

	if (error) {
		return {
			success: false,
			message: error.message,
		};
	}

	const collectionList = data.map(supabaseToCollectionData);

	return {
		success: true,
		message: "Collection list fetched",
		data: collectionList,
	};
}
