import { formatCurrency, formatStringForXlsx } from "~/utils/formatting-util";
import xlsx, { WorkSheetOptions } from "node-xlsx";
import {
  CustomProductData,
  PaymentTermData,
  SalesOrderData,
} from "~/types/dto";
import { ColInfo, Range } from "xlsx";
import { defaultPOLvalue } from "~/configs/app-config";
import { APIEvent } from "@solidjs/start/server";
import { makeJsonResponse } from "~/utils/http-util";

export async function POST({ request, params }: APIEvent) {
  const rawBody = await request.text();
  let body = rawBody.replace("data=", "");
  body = decodeURIComponent(body);

  let data;

  try {
    data = JSON.parse(body);
  } catch (e) {
    // console.error(e);
    return makeJsonResponse("Invalid JSON data.", 400);
  }

  const salesOrder = data.salesOrder as SalesOrderData;
  const defaultPaymentTerm = data.defaultPaymentTerm as PaymentTermData | null;

  const customProductDataCollection =
    data.customProductDataCollection as Record<string, CustomProductData>;

  const salesRepName = salesOrder.salesRepTeamMember?.name ?? "";
  const poNumber = salesOrder.poNumber;
  const paymentTermsName = formatStringForXlsx(
    salesOrder.paymentTerms?.name ?? "",
  );

  const hasAnyCustomProductData =
    Object.keys(customProductDataCollection).length > 0;

  let totalQuantity = 0;
  let subtotalNettWeight = 0;
  let subtotalGrossWeight = 0;
  let totalNettWeight = 0;
  let totalGrossWeight = 0;
  let totalWoodM3 = 0;

  const cbmFromCustomField = salesOrder.customFields?.custom1 ?? "";

  const nettWeightFromCustomField = salesOrder.customFields?.custom6 ?? "";

  const grossWeightFromCustomField = salesOrder.customFields?.custom7 ?? "";

  const containerAndSealID = salesOrder.customFields?.custom5 ?? "";
  const containerAndSealIDSplits = containerAndSealID.split("/");

  const sealID =
    containerAndSealIDSplits[containerAndSealIDSplits.length - 1] ?? "";

  const container = containerAndSealID.replace(`/${sealID}`, "");

  const woodM3FromCustomField = salesOrder.customFields?.custom8 ?? "";

  const blNumber = salesOrder.customFields?.custom2 ?? "";
  const pod = salesOrder.customFields?.custom3 ?? "";
  const pol = defaultPOLvalue;

  const headerCellStyle = {
    font: {
      bold: true,
      sz: 14,
    },
    alignment: {
      horizontal: "center",
      vertical: "center",
    },
    fill: {
      fgColor: {
        rgb: "f3f4f6",
      },
    },
    border: {
      bottom: {
        style: "thin",
        color: {
          rgb: "000000",
        },
      },
    },
  };

  const currencySymbol = salesOrder.currency?.symbol ?? "$";

  const topTableHeaderRow = [];
  const topTableBodyRow = [];

  topTableHeaderRow.push({
    t: "s",
    v: "PO Number",
    s: headerCellStyle,
  });

  topTableBodyRow.push(poNumber);

  if (hasAnyCustomProductData) {
    topTableHeaderRow.push({
      t: "s",
      v: "Sales Rep",
      s: headerCellStyle,
    });
    topTableHeaderRow.push("");

    topTableBodyRow.push(salesRepName);
    topTableBodyRow.push("");
  } else {
    topTableHeaderRow.push([
      {
        t: "s",
        v: "Sales Rep",
        s: headerCellStyle,
      },
    ]);

    topTableBodyRow.push(salesRepName);
  }

  topTableHeaderRow.push({
    t: "s",
    v: "Shipment Terms",
    s: headerCellStyle,
  });

  // topTableBodyRow.push(formatStringForXlsx(defaultPaymentTerm?.name ?? "-"));
  topTableBodyRow.push(
    formatStringForXlsx(salesOrder.paymentTerms?.name ?? "-"),
  );

  const totalTopTableBottomGapRows = 2;
  const topTableBottomGapRows = [];

  for (let i = 0; i < totalTopTableBottomGapRows; i++) {
    if (hasAnyCustomProductData) {
      topTableBottomGapRows.push(["", "", "", "", "", ""]);
    } else {
      topTableBottomGapRows.push(["", "", "", "", ""]);
    }
  }

  const mainTableHeaderRow = [];

  mainTableHeaderRow.push({
    t: "s",
    v: "Multay Product",
    s: headerCellStyle,
  });

  if (hasAnyCustomProductData) {
    mainTableHeaderRow.push({
      t: "s",
      v: "Customer Product",
      s: headerCellStyle,
    });
  }

  mainTableHeaderRow.push({
    t: "s",
    v: "Picture",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "Quantity (pcs)",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "Unit Price (US $)",
    s: headerCellStyle,
  });

  mainTableHeaderRow.push({
    t: "s",
    v: "Sub Total (US $)",
    s: headerCellStyle,
  });

  const mainTableBodyRows = [];

  const salesOrderLines = salesOrder.lines ?? [];

  for (const line of salesOrderLines) {
    const quantity = line.quantity.standardQuantity;
    const quantityNumber = Number(quantity);

    const customFields = line.product?.customFields;

    const nettWeight = customFields?.custom1 ?? "";
    const nettWeightNumber = Number(nettWeight);

    subtotalNettWeight += nettWeightNumber;
    totalNettWeight += subtotalNettWeight;

    const grossWeight = customFields?.custom2 ?? "";
    const grossWeightNumber = Number(grossWeight);

    subtotalGrossWeight += grossWeightNumber;
    totalGrossWeight += subtotalGrossWeight;

    const woodM3 = customFields?.custom7 ?? "";
    const totalWoodM3Number = Number(woodM3);

    const customFieldsLabel = line.product?.productCustomFieldLabels;
    const cbm = customFieldsLabel?.custom1 ?? "";

    const customProductData = customProductDataCollection[line.productId];

    const mainTableBodyRow = [];

    const sku = formatStringForXlsx(line.product?.sku ?? "");
    const productName = formatStringForXlsx(line.product?.name ?? "");

    mainTableBodyRow.push(`${sku}\n${productName}`);

    if (customProductData) {
      const customerProductCode = formatStringForXlsx(
        customProductData.customerProductCode,
      );
      const customerProductName = formatStringForXlsx(
        customProductData.customerProductName,
      );

      mainTableBodyRow.push(`${customerProductCode}\n${customerProductName}`);
    }

    const imageUrl = line.product?.images![0]?.largeUrl ?? "";

    mainTableBodyRow.push({
      t: "s",
      v: imageUrl,
      f: `IMAGE("${imageUrl}")`,
    });

    mainTableBodyRow.push({
      t: "n",
      v: quantityNumber,
      z: "0",
    });

    const unitPriceNumber = Number(line.unitPrice);

    mainTableBodyRow.push({
      t: "n",
      v: unitPriceNumber,
      z: `${currencySymbol}0.00`,
      w: formatCurrency({
        amount: line.unitPrice,
        symbol: currencySymbol,
      }),
    });

    const subTotalNumber = Number(line.subTotal);

    mainTableBodyRow.push({
      t: "n",
      v: subTotalNumber,
      z: `${currencySymbol}0.00`,
      w: formatCurrency({
        amount: line.subTotal,
        symbol: currencySymbol,
      }),
    });

    mainTableBodyRows.push(mainTableBodyRow);
  }

  const totalRow = [
    {
      t: "s",
      v: "Total",
      s: {
        font: {
          bold: true,
          sz: 14,
        },
        alignment: {
          horizontal: "center",
          vertical: "center",
        },
        fill: {
          fgColor: {
            rgb: "f3f4f6",
          },
        },
        border: {
          bottom: {
            style: "thin",
            color: {
              rgb: "000000",
            },
          },
        },
      },
    },
    "",
    "",
    "",
    "",
    {
      t: "n",
      v: Number(salesOrder.total),
      z: `${currencySymbol}0.00`,
      w: formatCurrency({
        amount: salesOrder.total,
        symbol: currencySymbol,
      }),
    },
  ];

  mainTableBodyRows.push(totalRow);

  const mainTableBottomGapRows = 3;

  // Create 3 empty rows as whitespace gap.
  for (let i = 0; i < mainTableBottomGapRows; i++) {
    mainTableBodyRows.push([]);
  }

  // Remarks row.
  const remarksRow = [
    {
      t: "s",
      v: `
        Remarks:\n
        Container: ${container ? container : "-"}
        Seal ID: ${sealID ? formatStringForXlsx(sealID) : "-"}
        BL Number: ${blNumber ? formatStringForXlsx(blNumber) : "-"}
        Nett Weight: ${
          nettWeightFromCustomField
            ? formatStringForXlsx(nettWeightFromCustomField)
            : "-"
        }
        Gross Weight: ${
          grossWeightFromCustomField
            ? formatStringForXlsx(grossWeightFromCustomField)
            : "-"
        }
        Total CBM: ${
          cbmFromCustomField ? formatStringForXlsx(cbmFromCustomField) : "-"
        }
        POL: ${pol ? formatStringForXlsx(pol) : "-"}
        POD: ${pod ? formatStringForXlsx(pod) : "-"}
        Total Wood M3: ${
          woodM3FromCustomField
            ? formatStringForXlsx(woodM3FromCustomField)
            : "-"
        }
      `,
    },
    "",
    "",
    "",
    "",
    "",
  ];

  mainTableBodyRows.push(remarksRow);

  let sheetName = `invoice-${salesOrder.orderNumber}`;

  // The sheetName can't be more than 31 characters.
  sheetName = sheetName.substring(0, 26) + ".xlsx";

  const totalTopTableRows = 2;
  const totalMainTableHeaderRows = 1;
  const totalProductLines = salesOrder.lines?.length ?? 0;
  const totalPriceRowPosIndex =
    totalTopTableRows +
    totalTopTableBottomGapRows +
    totalMainTableHeaderRows +
    totalProductLines;

  const totalPriceColsToMerge = hasAnyCustomProductData ? 4 : 3;

  /**
   * In topTable (before mainTable), we have 2 rows.
   *
   * For its header (row 1)
   * salesRepColsMerge: if hasAnyCustomProductData, cols 2nd and 3rd columns merged. If not, then undefined.
   * paymentTermsColsMerge: if hasAnyCustomProductData, cols 4th, 5th,and 6th merged. If not, then 3rd, 4th, 5th.
   *
   * For its value (row 2)
   * salesRepColsMerge: if hasAnyCustomProductData, cols 2nd and 3rd columns merged. If not, then undefined.
   * paymentTermsColsMerge: if hasAnyCustomProductData, cols 4th, 5th,and 6th merged. If not, then 3rd, 4th, 5th.
   */

  const salesRepHeaderColsMerge: Range | undefined = hasAnyCustomProductData
    ? {
        s: { r: 0, c: 1 },
        e: { r: 0, c: 2 },
      }
    : undefined;

  const salesRepBodyColsMerge: Range | undefined = hasAnyCustomProductData
    ? {
        s: { r: 1, c: 1 },
        e: { r: 1, c: 2 },
      }
    : undefined;

  const paymentTermsHeaderColsMerge: Range = {
    s: { r: 0, c: hasAnyCustomProductData ? 3 : 2 },
    e: { r: 0, c: hasAnyCustomProductData ? 5 : 4 },
  };

  const paymentTermsBodyColsMerge: Range = {
    s: { r: 1, c: hasAnyCustomProductData ? 3 : 2 },
    e: { r: 1, c: hasAnyCustomProductData ? 5 : 4 },
  };

  // In rows #totalPriceRowPosIndex, we have "Total" row. That row should have its 5 first columns merged.
  const totalPriceRowColsMerge = {
    s: { r: totalPriceRowPosIndex, c: 0 },
    e: { r: totalPriceRowPosIndex, c: totalPriceColsToMerge },
  };

  const mergedCols: Range[] = [];

  if (salesRepHeaderColsMerge && salesRepBodyColsMerge) {
    mergedCols.push(salesRepHeaderColsMerge);
    mergedCols.push(salesRepBodyColsMerge);
  }

  mergedCols.push(paymentTermsHeaderColsMerge);
  mergedCols.push(paymentTermsBodyColsMerge);

  mergedCols.push(totalPriceRowColsMerge);

  const colsWidth: ColInfo[] = [];

  if (hasAnyCustomProductData) {
    colsWidth.push({ wch: 80 });
    colsWidth.push({ wch: 50 });
    colsWidth.push({ wch: 20 });
    colsWidth.push({ wch: 8 });
    colsWidth.push({ wch: 12 });
    colsWidth.push({ wch: 15 });
  } else {
    colsWidth.push({ wch: 80 });
    colsWidth.push({ wch: 20 });
    colsWidth.push({ wch: 8 });
    colsWidth.push({ wch: 12 });
    colsWidth.push({ wch: 15 });
  }

  const sheetOptions: WorkSheetOptions = {
    "!cols": colsWidth,
    "!merges": mergedCols,
  };

  const buffer = xlsx.build([
    {
      name: sheetName,
      data: [
        topTableHeaderRow,
        topTableBodyRow,
        ...topTableBottomGapRows,
        mainTableHeaderRow,
        ...mainTableBodyRows,
      ],
      options: sheetOptions,
    },
  ]);

  // return new Response(buffer, {
  //   headers: {
  //     "Content-Type":
  //       "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  //     "Content-Disposition": `attachment; filename=${sheetName}`,
  //   },
  // }); this code work in local but not work in cloudflare worker

  return new Response(buffer, {
    headers: {
      "Content-Type":
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "Content-Disposition": `attachment; filename=${sheetName}`,
      // tambahkan ijin untuk download file xlsx di browser
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET,HEAD,POST,OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
