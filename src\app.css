@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

@font-face {
	font-family: "Myriad Pro";
	src:
		url("/fonts/myriad-pro/myriad_pro_black-webfont.woff2") format("woff2"),
		url("/fonts/myriad-pro/myriad_pro_black-webfont.woff") format("woff");
	font-weight: 900;
	font-style: normal;
}

@font-face {
	font-family: "Myriad Pro";
	src:
		url("/fonts/myriad-pro/myriad_pro_bold-webfont.woff2") format("woff2"),
		url("/fonts/myriad-pro/myriad_pro_bold-webfont.woff") format("woff");
	font-weight: 700;
	font-style: normal;
}

@font-face {
	font-family: "Myriad Pro";
	src:
		url("/fonts/myriad-pro/myriad_pro_light-webfont.woff2") format("woff2"),
		url("/fonts/myriad-pro/myriad_pro_light-webfont.woff") format("woff");
	font-weight: 300;
	font-style: normal;
}

@font-face {
	font-family: "Myriad Pro";
	src:
		url("/fonts/myriad-pro/myriad_pro_regular-webfont.woff2") format("woff2"),
		url("/fonts/myriad-pro/myriad_pro_regular-webfont.woff") format("woff");
	font-weight: normal;
	font-style: normal;
}

@media print {
	html,
	:host,
	body {
		font-family: "Arial", sans-serif !important;
	}

	table {
		page-break-after: auto;
	}

	tr {
		page-break-inside: avoid;
		page-break-after: auto;
	}

	td {
		page-break-inside: avoid;
		page-break-after: auto;
	}

	thead {
		display: table-header-group;
	}

	tfoot {
		display: table-footer-group;
	}
}

/* Start of TailwindCSS v4 config */
@theme {
	/* Custom Colors */
	--color-primary: #45827d;
	--color-secondary: #4270ab;
	--color-titlecolor: #001a3a;
	--color-companycolor: #bc8e56;
	--color-secondarycolor: #1d1d1d;
	--color-lightcolor: #f8fafc;
	--color-lightgreycolor: #e2e8f0;

	/* Custom Fonts */
	--font-sans: Inter, sans-serif;
	--font-display: "Playfair Display", serif;
	--font-table: "Myriad Pro", sans-serif;

	/* Custom Box Shadow */
	--shadow-custom: 0 0 9px rgba(0, 0, 0, 0.12);

	/* Custom Breakpoints */
	--breakpoint-print: print;
}

@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}

	input::placeholder,
	textarea::placeholder {
		color: var(--color-gray-400);
	}

	button:not(:disabled),
	[role="button"]:not(:disabled) {
		cursor: pointer;
	}

	dialog {
		margin: auto;
	}
}

@utility container {
	margin-inline: auto;
	/* padding-inline: 2rem; */
}
/* End of TailwindCSS v4 config */

.leaf {
	width: 180px;
	height: 180px;
	background-image: linear-gradient(
		to right,
		rgb(182, 244, 146),
		rgb(51, 139, 149)
	);
	border-radius: 0 100%;
}

.leaf.top-left {
	bottom: 0;
	margin-left: -200px;
	transform: rotate(-5deg);
}

.leaf.top-left span {
	/* transform: rotate(225deg); */
	transform: rotate(45deg);
}

.leaf.top-center {
	bottom: 44px;
	transform: rotate(45deg);
}

.leaf.top-center span {
	transform: rotate(-135deg);
}

.leaf.top-right {
	bottom: 0;
	margin-right: -200px;
	transform: rotate(95deg);
}

.leaf.top-right span {
	transform: rotate(-135deg);
}

.leaf.bottom-left {
	bottom: -175px;
	margin-left: -163px;
	transform: rotate(-95deg);
}

.leaf.bottom-left span {
	transform: rotate(45deg);
}

.leaf.bottom-right {
	bottom: -175px;
	margin-right: -163px;
	transform: rotate(-175deg);
}

.leaf.bottom-right span {
	transform: rotate(-135deg);
}
