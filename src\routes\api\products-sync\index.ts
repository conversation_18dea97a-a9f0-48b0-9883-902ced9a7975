import { APIEvent } from "@solidjs/start/server";
import { dbCollectSalesProducts } from "~/services/db-services/product-db-service";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { getErrorMessage, makeJsonResponse } from "~/utils/http-util";

export async function POST({ request, params }: APIEvent) {
	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to sync products",
			},
			401,
		);
	}

	let body: Record<string, any> | undefined = undefined;

	try {
		// Get POST data.
		body = await request.json();
	} catch (e: unknown) {
		// console.error("Failed to read request body before syncing products.", e);
	}

	const after = body && body.after ? String(body.after) : undefined;

	try {
		const response = await dbCollectSalesProducts({
			after: after,
		});

		return makeJsonResponse(response, response.success ? 200 : 500);
	} catch (e: unknown) {
		console.error("Faied to sync products.", e);

		return makeJsonResponse(
			{
				success: false,
				message: getErrorMessage(e),
			},
			500,
		);
	}
}
