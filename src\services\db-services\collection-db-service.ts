import { StructuredCategoryData } from "~/types/dto";
import { dbGetSalesProductStructuredCategories } from "./category-db-service";
import { findOption, getOption, updateOption } from "./option-db-service";
import { SimpleResponse } from "~/types/response";

export async function dbCollectCollections(): Promise<SimpleResponse> {
	"use server";

	const salesProductCategory = await dbGetSalesProductStructuredCategories();
	const collectionCategories: StructuredCategoryData[] = [];

	for (const salesProductChildCategory of salesProductCategory?.children ??
		[]) {
		if (salesProductChildCategory.name.toLowerCase().endsWith(" collection")) {
			collectionCategories.push(salesProductChildCategory);
		}
	}

	const result = await updateOption({
		name: "collection_categories",
		value: collectionCategories,
	});

	return {
		success: result ? true : false,
		message: result
			? "Collection categories have been collected to database successfully"
			: "Failed to collect collection categories",
	};
}

export async function dbFetchCollectionCategories(): Promise<
	StructuredCategoryData[]
> {
	"use server";

	const categories = await getOption<StructuredCategoryData[]>(
		"collection_categories",
	);

	if (!categories) return [];

	return categories;
}

export async function dbFindCollectionCategory(
	categoryId: string,
): Promise<StructuredCategoryData | null> {
	"use server";

	const categories = await getOption<StructuredCategoryData[]>(
		"collection_categories",
	);

	if (!categories) return null;

	for (const category of categories) {
		if (category.categoryId === categoryId) {
			return category;
		}
	}

	return null;
}
