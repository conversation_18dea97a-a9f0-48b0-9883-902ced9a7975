import { A } from "@solidjs/router";
import { ChevronLeft, Home, RefreshCcw } from "lucide-solid";
import { Show } from "solid-js";

export default function ErrorMessage(props: {
	title: string;
	message: string | undefined;
	isFullPage?: boolean;
	useButtons?: {
		backButton?: boolean;
		homeButton?: boolean;
		reloadButton?: boolean;
	};
}) {
	return (
		<div
			class={`${props.isFullPage ? "fixed -top-10" : "relative"} flex h-full w-full items-center justify-center`}
		>
			<div class="text-center">
				<h1
					class={`relative text-[200px] leading-tight font-bold text-black/10`}
				>
					{props.title}
				</h1>

				<p class="my-10 text-3xl font-medium text-gray-600">{props.message}</p>

				<div class="flex items-center justify-center">
					<Show when={props.useButtons?.backButton}>
						<button
							type="button"
							class="mr-1 inline-flex items-center rounded-full bg-orange-900 px-4 py-2 text-sm text-white"
							onClick={() => window.history.back()}
						>
							<ChevronLeft class="inline-block h-5 w-5" />
							Back
						</button>
					</Show>

					<Show when={props.useButtons?.homeButton}>
						<A
							href="/"
							class="mr-1 ml-1 inline-flex items-center rounded-full bg-orange-900 px-4 py-2 text-sm text-white"
						>
							<Home class="mr-1 inline-block h-4 w-4" />
							Go home
						</A>
					</Show>

					<Show when={props.useButtons?.reloadButton}>
						<button
							type="button"
							class="ml-1 inline-flex items-center rounded-full bg-orange-900 px-4 py-2 text-sm text-white"
							onClick={() => window.location.reload()}
						>
							<RefreshCcw class="mr-1 inline-block h-4 w-4" />
							Reload
						</button>
					</Show>
				</div>
			</div>
		</div>
	);
}
