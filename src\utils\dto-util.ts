import { CustomerData, PricingSchemeData, ProductData } from "~/types/dto";

export function toProductData(data: unknown): ProductData | undefined {
	"use server";

	if (!data || typeof data !== "object") {
		return undefined;
	}

	const product: ProductData = {
		// Non-nullable props
		productId: String("productId" in data ? data.productId : ""),
		sku: String("sku" in data ? data.sku : ""),
		name: String("name" in data ? data.name : ""),
		description: String("description" in data ? data.description : ""),
		width: String("width" in data ? data.width : ""),
		height: String("height" in data ? data.height : ""),
		length: String("length" in data ? data.length : ""),
		weight: String("weight" in data ? data.weight : ""),
		autoAssembler: Boolean(
			"autoAssembler" in data ? data.autoAssembler : false,
		),
		barcode: String("barcode" in data ? data.barcode : ""),
		categoryId: String("categoryId" in data ? data.categoryId : ""),
		defaultImageId: String("defaultImageId" in data ? data.defaultImageId : ""),
		hsTariffNumber: String("hsTariffNumber" in data ? data.hsTariffNumber : ""),
		includeQuantityBuildable: Boolean(
			"includeQuantityBuildable" in data
				? data.includeQuantityBuildable
				: false,
		),
		isActive: Boolean("isActive" in data ? data.isActive : false),
		isManufacturable: Boolean(
			"isManufacturable" in data ? data.isManufacturable : false,
		),
		itemType: String("itemType" in data ? data.itemType : ""),
		lastModifiedById: String(
			"lastModifiedById" in data ? data.lastModifiedById : "",
		),
		lastModifiedDateTime: String(
			"lastModifiedDateTime" in data ? data.lastModifiedDateTime : "",
		),
		lastVendorId: String("lastVendorId" in data ? data.lastVendorId : ""),
		originCountry: String("originCountry" in data ? data.originCountry : ""),
		remarks: String("remarks" in data ? data.remarks : ""),
		standardUomName: String(
			"standardUomName" in data ? data.standardUomName : "",
		),
		timestamp: String("timestamp" in data ? data.timestamp : ""),
		totalQuantityOnHand: String(
			"totalQuantityOnHand" in data ? data.totalQuantityOnHand : "",
		),
		trackSerials: Boolean("trackSerials" in data ? data.trackSerials : false),

		// Nullable props
		customFields:
			"customFields" in data ? (data.customFields as any) : undefined,
		category: "category" in data ? (data.category as any) : undefined,
		cost: "cost" in data ? (data.cost as any) : undefined,
		defaultImage:
			"defaultImage" in data ? (data.defaultImage as any) : undefined,
		images: "images" in data ? (data.images as any) : undefined,
		defaultPrice:
			"defaultPrice" in data ? (data.defaultPrice as any) : undefined,
		inventoryLines:
			"inventoryLines" in data ? (data.inventoryLines as any) : undefined,
		itemBoms: "itemBoms" in data ? (data.itemBoms as any) : undefined,
		lastModifiedBy:
			"lastModifiedBy" in data ? (data.lastModifiedBy as any) : undefined,
		lastVendor: "lastVendor" in data ? (data.lastVendor as any) : undefined,
		prices: "prices" in data ? (data.prices as any) : undefined,
		productCustomFieldLabels:
			"productCustomFieldLabels" in data
				? (data.productCustomFieldLabels as any)
				: undefined,
		productOperations:
			"productOperations" in data ? (data.productOperations as any) : undefined,
		purchasingUom:
			"purchasingUom" in data ? (data.purchasingUom as any) : undefined,
		reorderSettings:
			"reorderSettings" in data ? (data.reorderSettings as any) : undefined,
		salesUom: "salesUom" in data ? (data.salesUom as any) : undefined,
		taxCodes: "taxCodes" in data ? (data.taxCodes as any) : undefined,
		vendorItems: "vendorItems" in data ? (data.vendorItems as any) : undefined,
	};

	if (product.customFields === undefined) delete product.customFields;
	if (product.category === undefined) delete product.category;
	if (product.cost === undefined) delete product.cost;
	if (product.defaultImage === undefined) delete product.defaultImage;
	if (product.images === undefined) delete product.images;
	if (product.defaultPrice === undefined) delete product.defaultPrice;
	if (product.inventoryLines === undefined) delete product.inventoryLines;
	if (product.itemBoms === undefined) delete product.itemBoms;
	if (product.lastModifiedBy === undefined) delete product.lastModifiedBy;
	if (product.lastVendor === undefined) delete product.lastVendor;
	if (product.prices === undefined) delete product.prices;

	if (product.productCustomFieldLabels === undefined) {
		delete product.productCustomFieldLabels;
	}

	if (product.productOperations === undefined) delete product.productOperations;
	if (product.purchasingUom === undefined) delete product.purchasingUom;
	if (product.reorderSettings === undefined) delete product.reorderSettings;
	if (product.salesUom === undefined) delete product.salesUom;
	if (product.taxCodes === undefined) delete product.taxCodes;
	if (product.vendorItems === undefined) delete product.vendorItems;

	return product;
}

export function toCustomerData(data: unknown): CustomerData | undefined {
	"use server";

	if (!data || typeof data !== "object") {
		return undefined;
	}

	const customer: CustomerData = {
		// Non-nullable props
		customerId: "customerId" in data ? String(data.customerId) : "",
		email: "email" in data ? String(data.email) : "",
		isActive: "isActive" in data ? Boolean(data.isActive) : false,
		name: "name" in data ? String(data.name) : "",
		contactName: "contactName" in data ? String(data.contactName) : "",
		phone: "phone" in data ? String(data.phone) : "",
		website: "website" in data ? String(data.website) : "",
		defaultBillingAddressId:
			"defaultBillingAddressId" in data
				? String(data.defaultBillingAddressId)
				: "",
		defaultCarrier: "defaultCarrier" in data ? String(data.defaultCarrier) : "",
		defaultLocationId:
			"defaultLocationId" in data ? String(data.defaultLocationId) : "",
		defaultPaymentMethod:
			"defaultPaymentMethod" in data ? String(data.defaultPaymentMethod) : "",
		defaultPaymentTermsId:
			"defaultPaymentTermsId" in data ? String(data.defaultPaymentTermsId) : "",
		defaultSalesRep:
			"defaultSalesRep" in data ? String(data.defaultSalesRep) : "",
		defaultSalesRepTeamMemberId:
			"defaultSalesRepTeamMemberId" in data
				? String(data.defaultSalesRepTeamMemberId)
				: "",
		defaultShippingAddressId:
			"defaultShippingAddressId" in data
				? String(data.defaultShippingAddressId)
				: "",
		fax: "fax" in data ? String(data.fax) : "",
		pricingSchemeId:
			"pricingSchemeId" in data ? String(data.pricingSchemeId) : "",
		discount: "discount" in data ? String(data.discount) : "",
		taxingSchemeId: "taxingSchemeId" in data ? String(data.taxingSchemeId) : "",
		lastModifiedById:
			"lastModifiedById" in data ? String(data.lastModifiedById) : "",
		remarks: "remarks" in data ? String(data.remarks) : "",
		taxExemptNumber:
			"taxExemptNumber" in data ? String(data.taxExemptNumber) : "",
		timestamp: "timestamp" in data ? String(data.timestamp) : "",

		// Nullable props
		customFields:
			"customFields" in data ? (data.customFields as any) : undefined,
		addresses: "addresses" in data ? (data.addresses as any) : undefined,
		balances: "balances" in data ? (data.balances as any) : undefined,
		credits: "credits" in data ? (data.credits as any) : undefined,
		defaultBillingAddress:
			"defaultBillingAddress" in data
				? (data.defaultBillingAddress as any)
				: undefined,
		defaultLocation:
			"defaultLocation" in data ? (data.defaultLocation as any) : undefined,
		defaultPaymentTerms:
			"defaultPaymentTerms" in data
				? (data.defaultPaymentTerms as any)
				: undefined,
		defaultSalesRepTeamMember:
			"defaultSalesRepTeamMember" in data
				? (data.defaultSalesRepTeamMember as any)
				: undefined,
		defaultShippingAddress:
			"defaultShippingAddress" in data
				? (data.defaultShippingAddress as any)
				: undefined,
		lastModifiedBy:
			"lastModifiedBy" in data ? (data.lastModifiedBy as any) : undefined,
		orderHistory:
			"orderHistory" in data ? (data.orderHistory as any) : undefined,
		pricingScheme:
			"pricingScheme" in data ? (data.pricingScheme as any) : undefined,
		taxingScheme:
			"taxingScheme" in data ? (data.taxingScheme as any) : undefined,
	};

	if (customer.customFields === undefined) delete customer.customFields;
	if (customer.addresses === undefined) delete customer.addresses;
	if (customer.balances === undefined) delete customer.balances;
	if (customer.credits === undefined) delete customer.credits;

	if (customer.defaultBillingAddress === undefined) {
		delete customer.defaultBillingAddress;
	}

	if (customer.defaultLocation === undefined) delete customer.defaultLocation;

	if (customer.defaultPaymentTerms === undefined) {
		delete customer.defaultPaymentTerms;
	}

	if (customer.defaultSalesRepTeamMember === undefined) {
		delete customer.defaultSalesRepTeamMember;
	}

	if (customer.defaultShippingAddress === undefined) {
		delete customer.defaultShippingAddress;
	}

	if (customer.lastModifiedBy === undefined) delete customer.lastModifiedBy;
	if (customer.orderHistory === undefined) delete customer.orderHistory;
	if (customer.pricingScheme === undefined) delete customer.pricingScheme;
	if (customer.taxingScheme === undefined) delete customer.taxingScheme;

	return customer;
}

export function toPricingSchemeData(
	data: unknown,
): PricingSchemeData | undefined {
	"use server";

	if (!data || typeof data !== "object") {
		return undefined;
	}

	const pricingScheme: PricingSchemeData = {
		// Non-nullable props
		pricingSchemeId:
			"pricingSchemeId" in data ? String(data.pricingSchemeId) : "",
		name: "name" in data ? String(data.name) : "",
		currencyId: "currencyId" in data ? String(data.currencyId) : "",
		isActive: "isActive" in data ? Boolean(data.isActive) : false,
		isDefault: "isDefault" in data ? Boolean(data.isDefault) : false,
		isTaxInclusive:
			"isTaxInclusive" in data ? Boolean(data.isTaxInclusive) : false,
		timestamp: "timestamp" in data ? String(data.timestamp) : "",

		// Nullable props
		currency: "currency" in data ? (data.currency as any) : undefined,
		productPrices:
			"productPrices" in data ? (data.productPrices as any) : undefined,
	};

	if (pricingScheme.currency === undefined) delete pricingScheme.currency;

	if (pricingScheme.productPrices === undefined) {
		delete pricingScheme.productPrices;
	}

	return pricingScheme;
}
