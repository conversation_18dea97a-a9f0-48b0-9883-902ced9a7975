import { LayoutGrid, Smile, SquareActivity } from "lucide-solid";
import { For } from "solid-js";
import ActivitySuport from "~/components/support-page/ActivitySuport";
import ButtonSupportPage from "~/components/support-page/ButtonSuportPage";


export default function Activity() {
	const dataLink = [
		{
			text: "Dashboard",
			icon: <LayoutGrid />,
			url: "/support",
		},

		{
			text: "Activites",
			icon: <SquareActivity />,
			url: "/support/activites",
			notif: true,
			amount: 2,
		},
		{
			text: "User",
			icon: <Smile />,
			url: "/support/user",
			notif: true,
			amount: 10,
		},
	];

	return (
		<div class="relative bg-black/10">
			{/* navigation start */}
			<div class="relative ml-5 h-screen w-48">
				<h1 class="pt-10 text-3xl font-bold">Support</h1>
				<div class="mt-5">
					<For each={dataLink}>
						{(data) => (
							<ButtonSupportPage
								text={data.text}
								icon={data.icon}
								url={data.url}
								onNotife={data.notif}
								amount={data.amount}
								location={data.url}
							/>
						)}
					</For>
				</div>
				<div class="fixed bottom-5 flex items-center">
					<img
						src="https://images.ctfassets.net/h6goo9gw1hh6/2sNZtFAWOdP1lmQ33VwRN3/24e953b920a9cd0ff2e1d587742a2472/1-intro-photo-final.jpg?w=1200&h=992&fl=progressive&q=70&fm=jpg"
						alt=""
						class="rounded-full w-8 h-8 object-cover border border-black/50"
					/>
					<p class="ml-2 text-sm font-medium">
						Mulyadi Sanjaya
					</p>
				</div>
			</div>
			{/* navigation end */}
			<div class="fixed right-0 bg-white top-0 h-screen w-9/12 overflow-hidden rounded-tl-3xl border-l-2 border-black/20">
				<ActivitySuport />
			</div>
		</div>
	);
}
