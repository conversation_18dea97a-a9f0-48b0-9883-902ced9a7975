import { For, Show } from "solid-js";
import { PurchaseOrderData } from "~/types/dto";
import {
	formatCurrency,
	formatDiscount,
	formatQuantity,
	formatWeight,
} from "~/utils/formatting-util";
import { isServer } from "solid-js/web";
import FootNote from "../sales-orders/FootNote";
import PurchaseActionButtons from "../purchase-order/PurchaseActionButtons";

export default function PurchaseOrderTable(props: {
	purchaseOrder: PurchaseOrderData;
	showOnlyUncompletedProducts?: boolean;
	removeActionButtons?: boolean;
}) {
	let totalUomQuantity = 0;
	let totalHeight = 0;
	let totalWidth = 0;
	let totalLength = 0;
	let totalM3 = 0;
	let totalCBM = 0;

	const productLines = props.purchaseOrder.lines ?? [];

	const hasAnyDiscount = productLines.some((line) => {
		const discount: number = Number(line.discount.value);

		if (discount > 0) {
			return true;
		}

		return false;
	});

	function formatPrice(amount: string | number): string {
		return formatCurrency({
			amount: amount,
			symbol: props.purchaseOrder.currency?.symbol,
			thousandsSeparator: props.purchaseOrder.currency?.thousandsSeparator,
			decimalSeparator: props.purchaseOrder.currency?.decimalSeparator,
			decimalDigits: props.purchaseOrder.currency?.decimalPlaces,
		});
	}

	const firstThSharedClass =
		"flex items-center flex-wrap justify-center border-x border-black p-1 font-medium";

	const thSharedClass =
		"flex items-center flex-wrap justify-center border-r border-black p-1 font-medium";

	const firstTdSharedClass = "border-x border-black p-1 print:p-0.5 xl:p-2";
	const tdSharedClass = "border-r border-black p-1 print:p-0.5 xl:p-2";

	function handleXlsxExport(
		formRef?: HTMLFormElement,
		dataFieldRef?: HTMLInputElement,
	) {
		if (isServer) return;
		if (!formRef || !dataFieldRef) return;
		formRef.action = "/api/xlsx/sales-order";

		const postData = {
			salesOrder: props.purchaseOrder,
		};

		const jsonContent = JSON.stringify(postData);

		dataFieldRef.value = jsonContent;

		setTimeout(() => {
			formRef.submit();
		}, 50);
	}

	return (
		<div class="text-xs print:text-[9px]">
			<table class="mt-5 w-full table-fixed border-y border-black">
				<thead class="text-center">
					<tr class="flex w-full flex-wrap items-stretch border-b border-black bg-gray-100">
						<th class={`w-[21%] ${firstThSharedClass}`}>Product</th>

						<th class={`w-[30%] ${thSharedClass}`}>Picture *)</th>

						<th class={`${thSharedClass} w-[6%]`}>Quantity</th>
						<th class={`${thSharedClass} w-[5%]`}>H</th>
						<th class={`${thSharedClass} w-[5%]`}>W</th>
						<th class={`${thSharedClass} w-[5%]`}>D</th>
						<th class={`${thSharedClass} w-[5%]`}>M3</th>
						<th class={`${thSharedClass} w-[7%]`}>
							Total
							<br />
							M3
						</th>

						<th
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : " w-[7%]")
							}
						>
							Unit Price
						</th>
						<Show when={hasAnyDiscount}>
							<th class={`${thSharedClass} w-[4%]`}>Disc</th>
						</Show>
						<th
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : " w-[9%]")
							}
						>
							Sub Total
						</th>
					</tr>
				</thead>

				<tbody class="text-center">
					<For each={props.purchaseOrder.lines}>
						{(line) => {
							if (props.showOnlyUncompletedProducts && line.serviceCompleted) {
								return <></>;
							}

							const height = line.product?.height;
							const discount = line.discount;
							const width = line.product?.length;
							const length = line.product?.width;
							const customFields = line.product?.customFields;

							const quantityObj = line.quantity;
							const uom = quantityObj.uom;

							const uomQty = quantityObj.uomQuantity;
							const uomQtyNumber = Number(uomQty);
							totalUomQuantity += uomQtyNumber;

							const unitPriceNumber = Number(line.unitPrice);

							const discountNumber = Number(discount.value);
							const discountAmount = discount.isPercent
								? unitPriceNumber * (discountNumber / 100)
								: discountNumber;
							const priceUnitAfterDiscount = unitPriceNumber - discountAmount;
							const priceAfterDiscount = priceUnitAfterDiscount * uomQtyNumber;

							// These are in cm unit.
							const heightNumber = Number(height);
							totalHeight += heightNumber;

							const widthNumber = Number(width);
							totalWidth += widthNumber;
							const lengthNumber = Number(length);
							totalLength += lengthNumber;

							const m3 = line.product?.customFields?.custom3 ?? "";
							const m3Number = Number(m3);

							const subTotalM3 = m3Number * uomQtyNumber;
							totalM3 += subTotalM3;
							const subTotal = unitPriceNumber * uomQtyNumber;

							return (
								<tr class="font-base flex w-full flex-wrap items-stretch border-b border-black text-gray-700 print:font-light">
									<td
										class={`${firstTdSharedClass} w-[21%] text-left print:leading-[10px]`}
									>
										<span class="text-left uppercase">{line.product?.sku}</span>
										<br />
										<span class="text-left">{line.product?.name}</span>
									</td>

									<td class={`flex w-[30%] ${tdSharedClass}`} colspan="2">
										<Show
											when={
												line.product &&
												line.product.images &&
												line.product.images.length > 0
											}
										>
											<img
												src={line.product?.images![0]!.largeUrl}
												alt="Product image"
												class={` print:max-w-3/4 m-auto max-h-40 print:max-h-20`}
											/>
										</Show>
									</td>

									<td class={`${tdSharedClass} w-[6%]`}>
										{formatQuantity(uomQty)} {uom}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>
										{formatQuantity(height!)}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>
										{formatQuantity(width!)}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>
										{formatQuantity(length!)}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>{formatWeight(m3)}</td>
									<td class={`${tdSharedClass} w-[7%]`}>
										{formatWeight(subTotalM3)}
									</td>
									<td
										class={
											`${tdSharedClass} ` +
											(hasAnyDiscount ? "w-[6%]" : "w-[7%]")
										}
									>
										{formatPrice(line.unitPrice)}
									</td>

									<Show when={hasAnyDiscount}>
										<td class={`${tdSharedClass} w-[4%]`}>
											<Show when={discount.value !== "0.00000"}>
												{formatDiscount(discount.value ?? "")}

												<Show when={discount.isPercent}>
													<span>%</span>
												</Show>
											</Show>
										</td>
									</Show>

									<td
										class={
											`${tdSharedClass} ` +
											(hasAnyDiscount ? "w-[6%]" : "w-[9%]")
										}
									>
										<p>{formatPrice(priceAfterDiscount)}</p>
									</td>
								</tr>
							);
						}}
					</For>
				</tbody>

				<tbody class="text-center">
					<tr class="flex w-full flex-wrap items-stretch text-xs print:text-[9px]">
						<td class={`${firstThSharedClass} w-[51%]`} colspan="2">
							Total
						</td>
						<td class={`${thSharedClass} w-[6%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[7%]`}>{formatWeight(totalM3)}</td>
						<td
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : "w-[7%]")
							}
						></td>
						<td
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? " w-[4%]" : "hidden")
							}
						>
							&nbsp;
						</td>
						<td
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : "w-[9%]")
							}
						>
							{formatPrice(props.purchaseOrder.total)}
						</td>
					</tr>
				</tbody>
			</table>

			<FootNote />

			{props.purchaseOrder.orderRemarks && (
				<div class="mt-4">
					<p class="text-md">Remarks:</p>
					<div class="mt-1 w-1/3 border border-black p-1.5 print:p-0.5">
						<p
							class="text-xs text-gray-700 print:text-[8px] print:leading-[10px]"
							style="white-space: pre-line;"
						>
							{props.purchaseOrder.orderRemarks}
						</p>
					</div>
				</div>
			)}

			<Show when={!props.removeActionButtons}>
				<PurchaseActionButtons
					onXlsxExport={handleXlsxExport}
					formName="Purchase Order"
					id={props.purchaseOrder.purchaseOrderId}
					purchaseOrder={props.purchaseOrder}
				/>
			</Show>
		</div>
	);
}
