import { <PERSON><PERSON>en, Trash } from "lucide-solid";
import { Show } from "solid-js";
import { CustomProductData, ProfileData } from "~/types/dto";

export interface CustomProductDataRowProps {
	data: CustomProductData & { product: { images: { originalUrl: string }[] } };
	profile?: ProfileData;
	onDeleteButtonClick?: (data: CustomProductData) => void;
	onEditButtonClick?: (data: CustomProductData) => void;
}

export default function OrderHistoryDataRow(props: CustomProductDataRowProps) {
	return (
		<div class="grid grid-cols-3 items-center border-b px-[1rem] py-2 text-xs hover:bg-gray-100 sm:px-0">
			<div>
				<p>{props.data.productName}</p>
			</div>
			<div class="text-center font-semibold">
				<p>{props.data.productSku}</p>
			</div>

			<div class="grid grid-cols-3">
				<div class="col-span-2">
					<img
						src={props.data.product?.images[1].thumbUrl}
						alt={props.data.productName}
					/>
				</div>
				<Show when={props.profile}>
					<div class="flex justify-end justify-items-end">
						<button
							type="button"
							class="mr-2.5 inline-flex h-7 w-7 items-center justify-center rounded-full bg-red-300 text-white hover:bg-red-500 sm:mt-auto"
							onClick={() => props.onDeleteButtonClick?.(props.data)}
						>
							<Trash size={18} />
						</button>

						<button
							type="button"
							class="inline-flex h-7 w-7 items-center justify-center rounded-full bg-blue-300 text-white hover:bg-blue-500 sm:mt-auto"
							onClick={() => props.onEditButtonClick?.(props.data)}
						>
							<SquarePen size={18} />
						</button>
					</div>
				</Show>
			</div>
		</div>
	);
}
