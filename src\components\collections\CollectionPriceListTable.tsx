import { createSignal, For, Show } from "solid-js";
import {
	GroupedProductsByCategoryData,
	PriceData,
	StructuredCategoryData,
} from "~/types/dto";
import {
	formatCurrency,
	formatDimension,
	formatQuantity,
} from "~/utils/formatting-util";
import { sortProductsByCode } from "~/utils/product-util";
import PriceListActionButtons from "./PriceListActionButtons";
import { X } from "lucide-solid";

export default function CollectionPriceListTable(props: {
	category: StructuredCategoryData;
	groupedProducts: GroupedProductsByCategoryData[];
}) {
	const [filterPopupOpen, setFilterPopupOpen] = createSignal(false);
	const [hiddenCategories, setHiddenCategories] = createSignal<string[]>([]);

	function handleFilterIconClick() {
		setFilterPopupOpen(true);
	}

	function handleCategoryChipClick(categoryId: string) {
		if (hiddenCategories().includes(categoryId)) {
			setHiddenCategories(hiddenCategories().filter((id) => id !== categoryId));
		} else {
			setHiddenCategories([...hiddenCategories(), categoryId]);
		}
	}

	function getChipClass(categoryId: string) {
		return hiddenCategories().includes(categoryId)
			? "bg-gray-100 text-gray-700 hover:bg-gray-200"
			: "is-active bg-blue-600 text-white hover:bg-blue-700";
	}

	const thSharedClass = "p-1 font-semibold text-center";
	const tdSharedClass = "p-1 print:p-0.5 xl:p-1.5";

	return (
		<div class="text-md mt-10 text-stone-800 print:text-[10px]">
			<table class="font-table table w-full table-fixed">
				<thead class="text-center uppercase">
					<tr class="w-full text-center font-bold print:text-sm">
						<th class={`${thSharedClass} w-[25%] px-1`} rowSpan={2}>
							Code
						</th>
						<th class={`${thSharedClass} w-[30%] px-1`} rowSpan={2}>
							Description
						</th>
						<th class={`${thSharedClass} w-[20%] px-3`} colspan={3}>
							Dimensions (cm)
						</th>
						<th class={`${thSharedClass} w-[10%]`} rowSpan={2}>
							M3
						</th>
						<th class={`${thSharedClass} w-[15%]`} rowSpan={2}>
							USD
						</th>
					</tr>

					<tr class="w-full font-bold print:text-sm">
						<th class={`${thSharedClass} pl-3`}>H</th>
						<th class={`${thSharedClass} px-1.5`}>W</th>
						<th class={`${thSharedClass} pr-3`}>D</th>
					</tr>
				</thead>

				<tbody class="align-baseline">
					<For each={props.groupedProducts}>
						{(group, groupIndex) => {
							return (
								<Show
									when={
										!hiddenCategories().includes(group.categoryId) &&
										group.products.length > 0
									}
								>
									<Show when={group.categoryId !== props.category.categoryId}>
										<tr class="group relative font-bold">
											<td
												class={`px-1 pb-1 ${groupIndex() === 0 ? "" : "pt-4"} xl:px-1.5 xl:pb-1.5 ${groupIndex() === 0 ? "" : "xl:pt-6"} print:p-0.5`}
												colspan={7}
											>
												{group.name}
											</td>
										</tr>
									</Show>
									<For each={sortProductsByCode(group.products)}>
										{(product) => {
											const height = product.height;
											const width = product.length; // Intentionally swapped by Multay team based on Mr. Jeremy's suggestion.
											const length = product.width; // Intentionally swapped by Multay team based on Mr. Jeremy's suggestion.

											const customFields = product.customFields;
											const cbm = customFields?.custom3 ?? "";

											let price: PriceData | undefined = undefined;

											for (const productPrice of product.prices) {
												if (productPrice.pricingScheme?.isDefault) {
													price = productPrice;
													break;
												}
											}

											return (
												<tr class="group relative">
													<td class={`${tdSharedClass}`}>{product.sku}</td>

													<td class={`${tdSharedClass}`}>
														{product.description}
													</td>

													<td class={`${tdSharedClass} text-center`}>
														{formatQuantity(height!)}
													</td>

													<td class={`${tdSharedClass} text-center`}>
														{formatQuantity(width!)}
													</td>

													<td class={`${tdSharedClass} text-center`}>
														{formatQuantity(length!)}
													</td>

													<td class={`${tdSharedClass} text-center`}>
														{formatDimension(cbm)}
													</td>

													<td class={`${tdSharedClass} text-center`}>
														{formatCurrency({
															amount: price?.unitPrice ?? 0.0,
															symbol:
																price?.pricingScheme?.currency.symbol ?? "$",
														})}
													</td>
												</tr>
											);
										}}
									</For>
								</Show>
							);
						}}
					</For>
				</tbody>
			</table>

			<div
				class={`popup-overlay fixed inset-0 ${filterPopupOpen() ? "visible z-50 opacity-100" : "invisible -z-10 opacity-0"} flex items-center justify-center bg-black/50 backdrop-blur-xs transition-all duration-300 ease-in-out`}
			>
				<div class="popup-container container flex flex-col items-center justify-center">
					<div class="popup relative w-full rounded-lg bg-white shadow-xl md:w-3/4 lg:w-1/2">
						<header class="popup-header flex items-center justify-between p-4">
							<h3 class="text-lg font-bold text-gray-900">Filter</h3>

							<button
								type="button"
								class="text-sm text-gray-500 hover:text-gray-600"
								onClick={() => setFilterPopupOpen(false)}
							>
								<span class="sr-only">Close</span>
								<X class="h-5 w-5" aria-hidden="true" />
							</button>
						</header>

						<div class="popup-body border-t border-gray-200 px-4 pt-6 pb-4">
							<For each={props.groupedProducts}>
								{(group, groupIndex) => {
									return (
										<span
											class={`chip mr-2 mb-2 inline-block cursor-pointer rounded-full px-3 py-1 text-sm transition-all duration-300 ease-in-out ${getChipClass(group.categoryId)}`}
											onClick={() => handleCategoryChipClick(group.categoryId)}
										>
											{group.name}
										</span>
									);
								}}
							</For>
						</div>

						<footer class="popup-footer flex items-center justify-end border-t border-gray-200 p-4">
							<button
								type="button"
								class="ml-4 rounded-full bg-slate-900 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-slate-950 focus:ring-4 focus:ring-blue-300"
								onClick={() => setFilterPopupOpen(false)}
							>
								Close
							</button>
						</footer>
					</div>
				</div>
			</div>

			<PriceListActionButtons
				title="Price List"
				url="/collections-gallery/collection-price-list"
				onFilterIconClick={handleFilterIconClick}
			/>
		</div>
	);
}
