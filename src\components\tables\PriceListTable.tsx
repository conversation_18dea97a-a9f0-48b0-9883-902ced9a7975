import { For, Show } from "solid-js";
import { CustomProductData, SalesOrderData } from "~/types/dto";
import { formatDate, formatQuantity } from "~/utils/formatting-util";
import AddProductButtonForPriceList from "../price-list/AddProductButtonForPriceList";

export default function PriceListTable(props: {
	salesOrder: SalesOrderData;
	customProductDataCollection: Record<string, CustomProductData>;
	onSelectProductClick?: () => void;
}) {
	const firstName = props.salesOrder.customer?.name?.split(" ")[0];

	return (
		<div class="text-md font-table mt-10 print:text-[9px]">
			<div class="w-full">
				<div class="grid grid-cols-8 items-center text-[14px] font-bold">
					<h4 class="px-1 text-center print:px-0">Legacy Home Code</h4>
					<h4 class="px-1 text-center print:px-0">Legacy Home Description</h4>
					<h4 class="px-1 text-center print:px-0">{firstName} Code</h4>
					<h4 class="px-1 text-center print:px-0">{firstName} Descriptions</h4>
					<h4 class="px-1 text-center print:px-0">PHOTO</h4>
					<div class="pr-1 text-center">
						<p>DIMENSION (CM)</p>
						<div class="mt-3 grid grid-cols-3 print:mt-0">
							<h4 class="text-left">H</h4>
							<h4 class="text-center">W</h4>
							<h4 class="text-right">D</h4>
						</div>
					</div>
					<div class="grid grid-cols-3">
						<h4 class="text-center">CBM</h4>
						<h4 class="text-center">NW</h4>
						<h4 class="text-center">GW</h4>
					</div>
					<div class="grid grid-cols-2 items-center text-center">
						<h4 class="px-1 print:px-0">
							Legacy Home <br /> FOB Price <br /> 20 Feb 2024
						</h4>
						<h4>
							{firstName} Price {formatDate(props.salesOrder.orderDate)}
						</h4>
					</div>
				</div>

				<For each={props.salesOrder.lines}>
					{(line) => {
						const customProductData =
							props.customProductDataCollection[line.productId];
						const height = line.product?.height;
						const width = line.product?.width;
						const length = line.product?.length;
						const customFieldsLabel = line.product?.productCustomFieldLabels;
						const CBM = customFieldsLabel?.custom1 ?? "";
						const customFields = line.product?.customFields;
						const nettWeight = customFields?.custom1 ?? "";
						const grossWeight = customFields?.custom2 ?? "";

						return (
							<div class="grid grid-cols-8 items-center font-sans font-medium">
								<div class="text-center font-bold">
									<p class="mx-auto w-4/5 text-center">{line.product?.sku}</p>
								</div>
								<div class="text-center">{line.product?.description}</div>
								<div class="text-center font-bold">
									<p class="mx-auto w-4/5 text-center">{line.product?.sku}</p>
								</div>
								<div class="text-center">{line.product?.name}</div>
								<div class="text-center">
									<Show
										when={
											line.product &&
											line.product.images &&
											line.product.images.length > 0
										}
									>
										<img
											src={line.product?.images![0]!.largeUrl}
											alt="Product image"
											class={`m-auto max-h-40 print:max-h-20 print:max-w-3/4`}
										/>
									</Show>
								</div>
								<div class="text-center">
									<div class="grid grid-cols-3">
										<div class="text-center">{formatQuantity(height!)}</div>
										{/* <div class="text-center">{formatQuantity(width!)}</div> */}
										<div class="text-center">180/280</div>
										<div class="text-center">{formatQuantity(length!)}</div>
									</div>
								</div>
								<div class="grid grid-cols-3">
									<div class="text-center">
										{formatQuantity(line.quantity.standardQuantity)}
									</div>
									<div class="text-center">{formatQuantity(nettWeight)}</div>
									<div class="text-center">{formatQuantity(grossWeight)}</div>
								</div>
								<div class="grid grid-cols-2 items-center text-center">
									<div class="text-center">
										<p class="mx-auto w-4/5 text-center text-[16px] font-bold print:text-[14px]">
											$ {formatQuantity(line.unitPrice)}
										</p>
									</div>
									<div class="text-center">
										<p class="mx-auto w-4/5 text-center text-[16px] font-bold print:text-[14px]">
											$ {formatQuantity(line.unitPrice)}
										</p>
									</div>
								</div>
							</div>
						);
					}}
				</For>
			</div>
			<div class="font-sans print:hidden">
				<AddProductButtonForPriceList
					onSelectProductClick={props.onSelectProductClick}
				/>
			</div>
		</div>
	);
}
