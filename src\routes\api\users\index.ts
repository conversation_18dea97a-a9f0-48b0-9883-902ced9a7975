import { APIEvent } from "@solidjs/start/server";
import { URL } from "url";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { fetchUsers } from "~/services/http-services/user-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function GET({ request, params }: APIEvent) {
	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to load users",
			},
			401,
		);
	}

	const urlObject = new URL(request.url);
	const searchParams = urlObject.searchParams;

	const page = searchParams.get("page");
	const perPage = searchParams.get("per_page");
	const keyword = searchParams.get("keyword");

	const response = await fetchUsers({
		page: page ? Number(page) : undefined,
		perPage: perPage ? Number(perPage) : undefined,
		keyword: keyword ?? undefined,
	});

	return makeJsonResponse(response);
}
