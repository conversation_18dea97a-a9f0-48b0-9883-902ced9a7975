import { fetchCustomers } from "~/services/http-services/customer-service";
import LoadingSpinner from "~/components/LoadingSpinner";
import { Show } from "solid-js";
import CustomerListWithLoadMore from "~/components/customers/CustomerListWithLoadMore";
import { getRequestURL } from "vinxi/http";
import {
	A,
	RouteDefinition,
	createAsync,
	query,
	redirect,
	useSearchParams,
} from "@solidjs/router";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { Search } from "lucide-solid";
import { getAppUrl } from "~/utils/url-util";

const getPageData = query(async () => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const urls = getAppUrl(getRequestURL());

	const customerListResponse = await fetchCustomers({
		sortDesc: false,
		keyword: urls.keyword ?? undefined,
		filters: {
			isActive: true,
		},
	});

	return {
		urls: urls,
		customerListResponse: customerListResponse,
	};
}, "customerListPageData");

export const route = {
	preload: () => getPageData(),
} satisfies RouteDefinition;

export default function CustomerListPage() {
	const pageData = createAsync(() => getPageData(), { deferStream: true });
	let searchFieldRef: HTMLInputElement | undefined;

	const [searchParams, setSearchParams] = useSearchParams();
	return (
		<>
			<header class="fixed w-full bg-white px-[1rem] pt-[1rem] pb-[1rem] shadow-md md:px-0 md:pb-0">
				<div class="md:container md:flex">
					<div>
						<h4 class="text-secondary text-2xl font-bold">Customers</h4>
						<nav class="mt-1 mb-1 flex items-center" aria-label="Breadcrumb">
							<ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
								<li class="inline-flex items-center">
									<A
										href="/"
										class="hover:text-secondary inline-flex items-center text-sm text-gray-700 dark:text-gray-400 dark:hover:text-white"
									>
										Dashboard
									</A>
								</li>
								<li>
									<div class="flex items-center">
										<svg
											class="h-2.5 w-2.5 text-gray-400 rtl:rotate-180"
											aria-hidden="true"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 6 10"
										>
											<path
												stroke="currentColor"
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="m1 9 4-4-4-4"
											/>
										</svg>
										<span class="ms-1 text-sm text-gray-700 md:ms-2 dark:text-gray-400 dark:hover:text-white">
											Customers
										</span>
									</div>
								</li>
							</ol>
						</nav>
					</div>

					<div class="my-auto ml-auto md:w-1/3">
						<div class="flex items-center rounded-md bg-gray-200 p-2">
							<Search class="text-gray-500" size={16} />
							<input
								ref={searchFieldRef}
								type="text"
								value={searchParams.keyword ?? ""}
								class="ml-2 w-full bg-transparent text-sm font-light outline-hidden"
								placeholder="Search customer"
							/>
						</div>
					</div>
				</div>
				<div class="container hidden py-2 text-xs font-bold text-black/75 md:flex">
					<div class="w-3/12 text-left">Name and contact</div>
					<div class="w-2/12 text-left">Phone</div>
					<div class="w-2/12 text-left">Email</div>
					<div class="w-2/12 text-left">City and state</div>
					<div class="w-1/12 text-right">Country</div>
					<div class="w-2/12 text-right">Default location</div>
				</div>
			</header>

			<Show
				when={pageData()?.urls && pageData()?.customerListResponse}
				fallback={<LoadingSpinner class="pt-32" />}
			>
				<CustomerListWithLoadMore
					baseApiUrl={pageData()?.urls?.baseApiUrl ?? ""}
					customers={pageData()?.customerListResponse?.data ?? []}
					searchField={searchFieldRef}
				/>
			</Show>

			<footer class="site-footer pt-12"></footer>
		</>
	);
}
