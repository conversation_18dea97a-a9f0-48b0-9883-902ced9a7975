import { Show, createSignal } from "solid-js";
import { clientUpdateCustomProductData } from "~/services/client-services/custom-product-data-client-service";
import { CustomProductData, CustomerData, ProductData } from "~/types/dto";
import LoadingSpinner from "../LoadingSpinner";
import { clientFindProduct } from "~/services/client-services/product-client-service";
import { X } from "lucide-solid";

export interface EditProductFormProps {
	baseApiUrl: string;
	customer?: CustomerData;
	existingData: CustomProductData;
	onSelectProductClick?: () => void;
	onCloseButtonClick?: () => void;
	onCancelButtonClick?: () => void;
	afterDataUpdated?: (product: CustomProductData) => void;
}

export default function EditProductPopup(props: EditProductFormProps) {
	const [isSaving, setIsSaving] = createSignal<boolean>(false);
	const [selectedCustomerProductCode, setSelectedCustomerProductCode] =
		createSignal<string>(props.existingData.customerProductCode);
	const [selectedCustomerProductName, setSelectedCustomerProductName] =
		createSignal<string>(props.existingData.customerProductName);

	const [existingProduct, setExistingProduct] = createSignal<
		ProductData | undefined
	>();

	function handleCustomerProductCodeChange(value: string) {
		setSelectedCustomerProductCode(value);
	}

	function handleCustomerProductNameChange(value: string) {
		setSelectedCustomerProductName(value);
	}

	async function findData() {
		const response = await clientFindProduct({
			url: `${props.baseApiUrl}/products`,
			id: props.existingData.productId,
		});

		if (!response.success) {
			alert(response.message);
			return;
		}

		if (response.data) setExistingProduct(response.data);
	}

	async function handleDataUpdate() {
		if (!existingProduct()) return;

		setIsSaving(true);

		const updateCustomProductData = {
			id: props.existingData.id,
			productId: existingProduct()?.productId ?? "",
			productName: existingProduct()?.name ?? "",
			productSku: existingProduct()?.sku ?? "",
			customerId: props.customer?.customerId ?? props.existingData.customerId,
			customerName: props.customer?.name ?? props.existingData.customerName,
			customerProductCode: selectedCustomerProductCode(),
			customerProductName: selectedCustomerProductName(),
		};

		const response = await clientUpdateCustomProductData({
			url: props.baseApiUrl + "/custom-product-data",
			data: updateCustomProductData,
		});

		setIsSaving(false);

		if (!response.success) {
			alert(response.message);
			return;
		}

		if (response.data) props.afterDataUpdated?.(response.data);
	}

	findData();

	return (
		<section class="fixed top-0 left-0 z-10 flex h-full w-full items-center justify-center bg-black/50">
			<div class="relative rounded-lg bg-white sm:w-1/2 lg:w-1/3">
				<h3 class="relative rounded-tl-lg rounded-tr-lg bg-gray-100 px-4 py-4 text-lg font-medium text-gray-900">
					Edit Custom Data
					<button
						class="absolute right-4 cursor-pointer rounded-full bg-black p-1 text-sm text-white"
						onClick={props.onCloseButtonClick}
					>
						<X size={20} />
					</button>
				</h3>
				<div class="px-4 py-4">
					<div class="mb-4">
						<label
							for="product_name"
							class="block text-sm font-medium text-gray-700"
						>
							Product
						</label>
						<input
							type="text"
							name="product_name"
							id="product_name"
							class="mt-1 block w-full cursor-pointer rounded-md border border-gray-300 bg-gray-100 px-3 py-2 text-black/50 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
							placeholder="Select a product"
							onClick={props.onSelectProductClick}
							value={existingProduct() ? existingProduct()?.name : ""}
							readOnly
						/>
					</div>
					<div class="mb-4">
						<label
							for="product_sku"
							class="block text-sm font-medium text-gray-700"
						>
							SKU
						</label>
						<input
							type="text"
							name="product_sku"
							id="product_sku"
							class="mt-1 block w-full cursor-pointer rounded-md border border-gray-300 bg-gray-100 px-3 py-2 text-black/50 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
							placeholder="product SKU"
							value={existingProduct() ? existingProduct()?.sku : ""}
							readOnly
						/>
					</div>

					<div class="mb-4">
						<label
							for="custom-product-code"
							class="block text-sm font-medium text-gray-700"
						>
							Customer product code
						</label>
						<input
							type="text"
							name="custom-product-code"
							id="custom-product-code"
							class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
							placeholder="Insert customer product code"
							value={
								existingProduct() ? props.existingData.customerProductCode : ""
							}
							onChange={(e) => {
								handleCustomerProductCodeChange(e.currentTarget.value);
							}}
						/>
					</div>
					<div class="mb-4">
						<label
							for="custom-product-name"
							class="block text-sm font-medium text-gray-700"
						>
							Customer product name
						</label>
						<input
							type="text"
							name="custom-product-name"
							id="custom-product-name"
							class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
							placeholder="Insert customer product name"
							value={
								existingProduct() ? props.existingData.customerProductName : ""
							}
							onChange={(e) => {
								handleCustomerProductNameChange(e.currentTarget.value);
							}}
						/>
					</div>
					<div class="flex justify-end">
						<button
							class="mr-2 inline-flex items-center rounded-md border px-3 py-2 text-sm font-medium text-slate-700/50 active:scale-95 sm:py-2"
							onClick={props.onCancelButtonClick}
							disabled={isSaving()}
						>
							<span class="">Cancel</span>
						</button>
						<button
							class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm text-white active:scale-95 sm:py-2"
							onClick={handleDataUpdate}
							disabled={isSaving()}
						>
							<Show when={isSaving()} fallback={<span class="">Update</span>}>
								<LoadingSpinner colorClass="text-white" iconClass="h-4 w-4" />
							</Show>
						</button>
					</div>
				</div>
			</div>
		</section>
	);
}
