import { For, Show, batch, createSignal } from "solid-js";
import { PurchaseOrderData } from "~/types/dto";
import PurchaseOrderDataRow from "./PurchaseOrderDataRow";
import { PurchaseOrderListResponse } from "~/types/response";
import LoadMore from "../data-loading/LoadMore";
import LoadingSpinner from "../LoadingSpinner";
import { clientFetchPurchaseOrders } from "~/services/client-services/purchase-order-client-service";
import SearchBehavior from "../data-loading/SearchBehavior";
import { useSearchParams } from "@solidjs/router";
import { getSearchKeyword } from "~/utils/search-util";

export interface PurchaseOrderListWithLoadMoreProps {
	baseApiUrl: string;
	purchaseOrders: PurchaseOrderData[];
	searchField?: HTMLInputElement;
	onViewFormsButtonClick?: (purchaseOrder: PurchaseOrderData) => void;
}

export default function PurchaseOrderListWithLoadMore(
	props: PurchaseOrderListWithLoadMoreProps,
) {
	const [searchParams, setSearchParams] = useSearchParams();
	const [purchaseOrders, setPurchaseOrders] = createSignal(
		props.purchaseOrders,
	);

	const [doingLoadMore, setDoingLoadMore] = createSignal<boolean>(false);
	const [loadMoreFinished, setLoadMoreFinished] = createSignal<boolean>(false);

	async function handleLoadMore() {
		if (doingLoadMore() || loadMoreFinished()) return;

		batch(() => {
			setDoingLoadMore(true);
		});

		const response = await fetchDataList(getSearchKeyword(searchParams));
		// console.log("response: ", response);

		handleFetchComplete(response);
	}

	async function fetchDataList(
		keyword?: string,
	): Promise<PurchaseOrderListResponse> {
		const lastPurchaseOrderItem = purchaseOrders().length
			? purchaseOrders()[purchaseOrders().length - 1]
			: undefined;

		return await clientFetchPurchaseOrders({
			url: `${props.baseApiUrl}/purchase-orders`,
			after: lastPurchaseOrderItem?.purchaseOrderId,
			keyword: keyword,
		});
	}

	async function handleSearch(keyword: string) {
		batch(() => {
			setLoadMoreFinished(false);
			setDoingLoadMore(true);
			setSearchParams({ keyword: keyword });
			setPurchaseOrders([]);
		});

		const response = await fetchDataList(keyword);

		handleFetchComplete(response);
	}

	function handleFetchComplete(response: PurchaseOrderListResponse): void {
		batch(() => {
			setDoingLoadMore(false);

			if (!response.success) {
				alert(response.message);
				return;
			}

			if (!response?.data?.length) {
				setLoadMoreFinished(true);
				return;
			}

			setPurchaseOrders(purchaseOrders().concat(response.data));
		});
	}

	let sectionRef: HTMLElement | undefined;

	return (
		<>
			<section class="text pt-36 text-sm md:pt-32" ref={sectionRef}>
				<SearchBehavior
					searchField={props.searchField}
					onSearch={handleSearch}
				/>
				<LoadMore contentRef={sectionRef} onLoadMore={handleLoadMore}>
					<>
						<For each={purchaseOrders()}>
							{(order) => <PurchaseOrderDataRow purchaseOrder={order} />}
						</For>
					</>
				</LoadMore>

				<Show when={doingLoadMore()}>
					<LoadingSpinner class="mt-14" />
				</Show>
			</section>
		</>
	);
}
