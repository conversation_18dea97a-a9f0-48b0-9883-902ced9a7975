import { For, Show, createSignal } from "solid-js";
import {
	CustomProductData,
	JobOrderCustomColumnData,
	JobOrderProductRemarksData,
	ProfileData,
	SalesOrderData,
} from "~/types/dto";
import { formatQuantity, formatWeight } from "~/utils/formatting-util";
import { isServer } from "solid-js/web";
import RemarksAction from "../job-orders-data/RemarksAction";
import { Plus, Printer, Trash } from "lucide-solid";
import FootNote from "../sales-orders/FootNote";
import { clientDeleteJobOrderCustomColumn } from "~/services/client-services/job-order-client-service";
import CustomColumnNameAction from "../job-orders-data/CustomColumnNameAction";
import CustomColumnValueAction from "../job-orders-data/CustomColumnValueAction";
import LoadingSpinner from "../LoadingSpinner";

export default function JobOrderTable(props: {
	salesOrder: SalesOrderData;
	remarksList: JobOrderProductRemarksData[];
	customColumn: JobOrderCustomColumnData | undefined;
	customProductDataCollection: Record<string, CustomProductData>;
	baseApiUrl: string;
	currentUser?: ProfileData;
}) {
	// The `sales_order_line_id` was a newly added column, so it may not exist.
	function findRemarksByLineIdOrProductId(
		lineId: string,
		productId: string,
	): JobOrderProductRemarksData | undefined {
		// First, find remarks by sales_order_line_id because it will be more accurate.
		for (const remarks of props.remarksList) {
			if (!remarks.salesOrderLineId) continue;

			if (remarks.salesOrderLineId === lineId) {
				return remarks;
			}
		}

		const limitDate = new Date("2024-11-18 09:46:46.008122+00");

		// If remarks by sales_order_line_id doesn't exist, find it by productId.
		for (const remarks of props.remarksList) {
			const updatedDateStr = remarks.updatedAt;

			if (updatedDateStr) {
				/**
				 * The updatedDateStr returned from Supabase is formed like this: 2024-11-18 09:46:46.008122+00
				 * Now check if the updatedDate is after (higher) than 2024-11-18 09:46:46.008122+00.
				 *
				 * Date 2024-11-18 09:46:46.008122+00 is when the `sales_order_line_id` column was added.
				 * The day is accurate, but the time is just hard coded).
				 */
				const updatedDate = new Date(updatedDateStr);

				/**
				 * If the updatedDate is after (higher) than 2024-11-18 09:46:46.008122+00,
				 * then we should not check remarks by productId.
				 */
				if (updatedDate > limitDate) {
					continue;
				}
			}

			if (remarks.productId === productId) {
				return remarks;
			}
		}

		return undefined;
	}

	let totalQuantity = 0;
	let totalHeight = 0;
	let totalWidth = 0;
	let totalLength = 0;
	let totalM3 = 0;
	let totalCBM = 0;

	const hasAnyCustomProductData =
		Object.keys(props.customProductDataCollection).length > 0;

	function widthWhenHasAnyCustomProductData() {
		if (hasAnyCustomProductData) {
			return "w-[10%]";
		} else {
			return "w-[0%] hidden";
		}
	}

	function pictureWidthClassWhenThereIsAnyCustomProductData() {
		if (hasAnyCustomProductData) {
			return " w-[10%] ";
		} else {
			return " w-[20%] ";
		}
	}

	const firstThSharedClass =
		"flex items-center flex-wrap justify-center border-x border-black p-1 font-medium";

	const thSharedClass =
		"flex items-center flex-wrap justify-center border-r border-black p-1 font-medium";

	const firstTdSharedClass = "border-x border-black p-1 print:p-0.5 xl:p-2";
	const tdSharedClass = "border-r border-black p-1 print:p-0.5 xl:p-2";

	function handleXlsxExport(
		formRef?: HTMLFormElement,
		dataFieldRef?: HTMLInputElement,
	) {
		if (isServer) return;
		if (!formRef || !dataFieldRef) return;
		formRef.action = "/api/xlsx/sales-order";

		const postData = {
			salesOrder: props.salesOrder,
			customProductDataCollection: props.customProductDataCollection,
		};

		const jsonContent = JSON.stringify(postData);

		dataFieldRef.value = jsonContent;

		setTimeout(() => {
			formRef.submit();
		}, 50);
	}

	const [customColumn, setCustomColumn] = createSignal<
		JobOrderCustomColumnData | undefined
	>(props.customColumn);

	function createEmptyCustomColumn() {
		const emptyCustomColumn: JobOrderCustomColumnData = {
			id: 0,
			createdAt: "",
			updatedAt: "",
			teamMemberId: "",
			salesOrderId: props.salesOrder.salesOrderId,
			name: "Custom Column",
			values: {},
		};

		setCustomColumn(emptyCustomColumn);
	}

	const [isDeleting, setIsDeleting] = createSignal(false);

	async function removeCustomColumn(id: number) {
		setIsDeleting(true);
		const response = await clientDeleteJobOrderCustomColumn(id);

		if (response.success) {
			setCustomColumn(undefined);
		} else {
			alert(response.message);
		}

		setIsDeleting(false);
	}

	return (
		<div class="text-xs print:text-[9px]">
			<table class="mt-5 w-full table-fixed border-y border-black">
				<thead class="text-center">
					<tr class="flex w-full flex-wrap items-stretch border-b border-black bg-gray-100">
						<th class={`w-[21%] ${firstThSharedClass}`}>Multay Product</th>
						<th
							class={`${widthWhenHasAnyCustomProductData()} ${thSharedClass}`}
						>
							Custom Product
						</th>
						<th
							class={` ${pictureWidthClassWhenThereIsAnyCustomProductData()} ${thSharedClass}`}
						>
							Picture *)
						</th>

						<th class={`${thSharedClass} w-[6%]`}>
							Quantity
							<br />
							(pcs)
						</th>
						<th class={`${thSharedClass} w-[5%]`}>H</th>
						<th class={`${thSharedClass} w-[5%]`}>W</th>
						<th class={`${thSharedClass} w-[5%]`}>D</th>
						<th class={`${thSharedClass} w-[5%]`}>M3</th>
						<th class={`${thSharedClass} w-[7%]`}>
							Total
							<br />
							m3
						</th>
						<th
							class={`${thSharedClass} ${customColumn() ? "w-[13%]" : "w-[26%]"}`}
						>
							Remarks
						</th>

						<Show when={customColumn()}>
							<th class={`${thSharedClass} w-[13%]`}>
								<CustomColumnNameAction
									currentUser={props.currentUser}
									customColumn={customColumn()!}
									afterDataSaved={(data) => {
										setCustomColumn(data);
									}}
								/>
							</th>
						</Show>
					</tr>
				</thead>

				<tbody class="text-center">
					<For each={props.salesOrder.lines}>
						{(line) => {
							const quantity = line.quantity.standardQuantity;
							const height = line.product?.height;

							/**
							 * Dari Multay memang sudah terlanjut terbalik.
							 * Di inFlow, walau dia width, tapi dianggap length.
							 *
							 * Jadi tim Multay sendiri kalau di inFlow ngisi width, itu maksudnya ngisi length.
							 * Karena kalau mau dikoreksi, sudah terlalu banyak untuk diubah.
							 */
							const width = line.product?.length;

							/**
							 * Dari Multay memang sudah terlanjut terbalik.
							 * Di inFlow, walau dia length, tapi dianggap width.
							 *
							 * Jadi tim Multay sendiri kalau di inFlow ngisi length, itu maksudnya ngisi width.
							 * Karena kalau mau dikoreksi, sudah terlalu banyak untuk diubah.
							 */
							const length = line.product?.width;

							const customFields = line.product?.customFields;

							const quantityNumber = Number(quantity);
							totalQuantity += quantityNumber;

							const unitPriceNumber = Number(line.unitPrice);

							// These are in cm unit.
							const heightNumber = Number(height);
							totalHeight += heightNumber;

							const widthNumber = Number(width);
							totalWidth += widthNumber;
							const lengthNumber = Number(length);
							totalLength += lengthNumber;

							// And convert to m3.
							const m3 = line.product?.customFields?.custom3 ?? "";
							const m3Number = Number(m3);

							const subTotalM3 = m3Number * quantityNumber;
							totalM3 += subTotalM3;
							const subTotal = unitPriceNumber * quantityNumber;

							const customProductData =
								props.customProductDataCollection[line.productId];

							return (
								<tr class="font-base flex w-full flex-wrap items-stretch border-b border-black text-gray-700 print:font-light">
									<td
										class={`${firstTdSharedClass} w-[21%] text-left print:leading-[10px]`}
									>
										<span class="text-left font-semibold uppercase">
											{line.product?.sku}
										</span>
										<br />
										<span class="text-left">{line.product?.name}</span>
									</td>
									<td
										class={`${widthWhenHasAnyCustomProductData()} ${tdSharedClass} text-left`}
									>
										<Show when={customProductData}>
											<span class="font-semibold">
												{customProductData?.customerProductCode}
											</span>
											<span>{customProductData?.customerProductName}</span>
										</Show>
									</td>
									<td
										class={` ${pictureWidthClassWhenThereIsAnyCustomProductData()} ${tdSharedClass}`}
									>
										<Show
											when={
												line.product &&
												line.product.images &&
												line.product.images.length > 0
											}
										>
											<img
												src={line.product?.images![0]!.largeUrl}
												alt="Product image"
												class={`m-auto max-h-40 print:max-h-20 print:max-w-3/4`}
											/>
										</Show>
									</td>

									<td class={`${tdSharedClass} w-[6%]`}>
										{formatQuantity(quantity)}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>
										{formatQuantity(height!)}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>
										{formatQuantity(width!)}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>
										{formatQuantity(length!)}
									</td>
									<td class={`${tdSharedClass} w-[5%]`}>{formatWeight(m3)}</td>
									<td class={`${tdSharedClass} w-[7%]`}>
										{formatWeight(subTotalM3)}
									</td>
									<td
										class={`${tdSharedClass} ${customColumn() ? "w-[13%]" : "w-[26%]"}`}
									>
										<RemarksAction
											currentUser={props.currentUser}
											apiUrl={`${props.baseApiUrl}/job-order-product-remarks`}
											salesOrderId={props.salesOrder.salesOrderId}
											salesOrderLineId={line.salesOrderLineId}
											productId={line.productId}
											remarks={findRemarksByLineIdOrProductId(
												line.salesOrderLineId,
												line.productId,
											)}
										/>
									</td>

									<Show when={customColumn()}>
										<td class={`${tdSharedClass} w-[13%]`}>
											<CustomColumnValueAction
												currentUser={props.currentUser}
												productId={line.productId}
												customColumn={customColumn()!}
												afterDataSaved={(data) => {
													setCustomColumn(data);
												}}
											/>
										</td>
									</Show>
								</tr>
							);
						}}
					</For>
				</tbody>

				<tbody class="text-center">
					<tr class="flex w-full flex-wrap items-stretch text-xs print:text-[9px]">
						<td class={`${firstTdSharedClass} w-[21%] text-left`}>
							<span class="text-left uppercase">Total</span>
							{}
						</td>
						<td class={`${tdSharedClass} w-[20%]`} colspan="2"></td>

						<td class={`${tdSharedClass} w-[6%]`}>
							{formatQuantity(totalQuantity)}
						</td>
						<td class={`${tdSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${tdSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${tdSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${tdSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${tdSharedClass} w-[7%]`}>{formatWeight(totalM3)}</td>
						<td
							colspan={customColumn() ? 2 : undefined}
							class={`${tdSharedClass} w-[26%]`}
						></td>
					</tr>
				</tbody>
			</table>

			<FootNote />

			{props.salesOrder.orderRemarks && (
				<div class="mt-4">
					<p class="text-md">Remarks:</p>
					<div class="mt-1 w-1/3 border border-black p-1.5 print:p-0.5">
						<p
							class="print:tet-[8px] text-xs text-gray-700 print:leading-[10px]"
							style="white-space: pre-line;"
						>
							{props.salesOrder.orderRemarks}
						</p>
					</div>
				</div>
			)}

			<div class="fixed right-5 bottom-5 z-10 flex flex-col space-y-1 rounded-full bg-orange-800/75 px-1.5 py-1.5 print:hidden">
				<Show
					when={customColumn()}
					fallback={
						<button
							type="button"
							title="Add custom column"
							class="rounded-full px-1.5 py-1.5 transition-all hover:bg-black"
							onClick={createEmptyCustomColumn}
						>
							<Plus
								class="text-white transition-colors duration-200 ease-in-out hover:text-yellow-400"
								size={20}
							/>
						</button>
					}
				>
					<button
						type="button"
						title="Delete custom column"
						class="rounded-full px-1.5 py-1.5 transition-all hover:bg-black"
						onClick={() => {
							if (isDeleting()) return;

							if (
								confirm("Are you sure you want to delete this custom column?")
							) {
								removeCustomColumn(customColumn()!.id);
							}
						}}
					>
						<Show
							when={isDeleting()}
							fallback={
								<Trash
									class="text-white transition-colors duration-200 ease-in-out hover:text-red-400"
									size={20}
								/>
							}
						>
							<LoadingSpinner colorClass="text-white" iconClass="h-5 w-5" />
						</Show>
					</button>
				</Show>

				<button
					type="button"
					title="Print this page"
					class="rounded-full px-1.5 py-1.5 transition-all hover:bg-black"
					onClick={() => window.print()}
				>
					<Printer
						class="text-white transition-colors duration-200 ease-in-out hover:text-yellow-400"
						size={20}
					/>
				</button>
			</div>
		</div>
	);
}
