import { clientUploadImage } from "~/services/client-services/job-order-client-service";

export default function UploadImageAction(props: {
	apiUrl: string;
	salesOrderId: string;
	productId: string;
	afterDataSaved?: (data: string) => void;
}) {
	let imageRef: HTMLInputElement | undefined;

	async function handleDataSave() {
		const file = imageRef?.files?.[0];
		if (!file) {
			return;
		}

		const response = await clientUploadImage({
			url: props.apiUrl,
			file: file,
		});

		if (props.afterDataSaved) {
			props.afterDataSaved(response);
		}
	}

	return (
		<div class="flex h-full justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5">
			<div class="space-y-1 text-center">
				<div class="justify-center text-sm text-gray-600">
					<label
						for="file-upload"
						class="hover:text-cyan-5000 relative cursor-pointer bg-white font-medium text-[#077C52]"
					>
						<span>Upload a file</span>
						<input
							id="file-upload"
							name="file-upload"
							type="file"
							class="sr-only"
						/>
					</label>
					<p class="mt-2">or drag and drop</p>
				</div>
				<p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
			</div>
		</div>
	);
}
