import {
	<PERSON>ie<PERSON>ethodsServer,
	<PERSON>ieOptions,
	parseCookieHeader,
} from "@supabase/ssr";
import { getRequestEvent } from "solid-js/web";
import { setCookie, useSession } from "vinxi/http";
import { cookieConfig, sessionConfig } from "~/configs/server-config";

export function getSessionManager(): CookieMethodsServer {
	"use server";

	return {
		getAll: () => {
			const requestEvent = getRequestEvent();
			if (!requestEvent) return null;

			const cookieStr = requestEvent.request.headers.get("cookie");
			if (!cookieStr) return null;

			const cookiePairs = parseCookieHeader(cookieStr);
			if (!cookiePairs.length) return [];

			return cookiePairs.map((cookiePair) => {
				return {
					name: cookiePair.name,
					value: cookiePair.value ?? "",
				};
			});
		},
		setAll: (cookiesToSet) => {
			const requestEvent = getRequestEvent();
			if (!requestEvent) return;

			for (const { name, value, options } of cookiesToSet) {
				setCookie(requestEvent.nativeEvent, name, value, options);
			}
		},
	};
}

function getAppSession() {
	"use server";

	return useSession({
		password: sessionConfig().sessionSecret,
		name: cookieConfig().cookieName,
		// Expire in 30 days.
		maxAge: 60 * 60 * 24 * 30,
	});
}

async function getAllSessionData() {
	"use server";

	try {
		const session = await getAppSession();
		return session.data;
	} catch (e: unknown) {
		console.error("Error when getting session data in getAllSessionData:", e);
		return null;
	}
}

async function getSessionData(key: string) {
	"use server";

	try {
		const session = await getAppSession();
		const value = session.data[key] ? String(session.data[key]) : null;

		return value;
	} catch (e: unknown) {
		console.error("Error when getting session data in getSessionData:", e);
		return null;
	}
}

async function setSessionData(
	key: string,
	value: string,
	options?: CookieOptions,
) {
	"use server";

	try {
		const session = await getAppSession();

		const sessionData = { ...session.data };
		sessionData[key] = value;

		await session.update((d) => (d = sessionData));
	} catch (e: unknown) {
		console.error("Error when updating session data in setSessionData:", e);
	}
}

async function removeSessionData(key: string, options?: CookieOptions) {
	"use server";

	try {
		const session = await getAppSession();

		const sessionData = { ...session.data };
		delete sessionData[key];

		await session.update((d) => (d = sessionData));
	} catch (e: unknown) {
		console.error("Error when removing session data in removeSessionData:", e);
	}
}
