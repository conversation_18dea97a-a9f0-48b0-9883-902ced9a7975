import { AuthTokenResponsePassword, Session } from "@supabase/supabase-js";

export interface TeamMemberData {
	accessAllLocations: boolean;
	accessLocationIds: string[];
	accessRights: string[];
	canBeSalesRep: boolean;
	email: string;
	isActive: boolean;
	isInternal: boolean;
	name: string;
	teamMemberId: string;
}

export interface AddressTypeData {
	hasValue: boolean;
	value: string;
}

export interface CostOfGoodsSoldData {
	costOfGoodsSold: string;
	salesOrder?: SalesOrderData;
	salesOrderCostOfGoodsSoldId: string;
	salesOrderId: string;
}

export interface CurrencyConversionData {
	currency?: CurrencyData;
	currencyConversionId: string;
	currencyId: string;
	exchangeRate: string;
	timestamp: string;
}

export interface CurrencyData {
	currencyConversions?: CurrencyConversionData[];
	currencyId: string;
	decimalPlaces: number;
	decimalSeparator: string;
	isSymbolFirst: boolean;
	isoCode: string;
	name: string;
	negativeType: string;
	symbol: string;
	thousandsSeparator: string;
	timestamp: string;
}

export interface AddressTypeData {
	hasValue: boolean;
	value: string;
}

export interface AddressData {
	address1: string;
	address2: string;
	addressType: AddressTypeData;
	city: string;
	country: string;
	postalCode: string;
	remarks: string;
	state: string;
}

export interface CustomerAddressData {
	address: AddressData;
	customer?: CustomerData;
	customerAddressId: string;
	customerId: string;
	name: string;
	timestamp: string;
}

export interface VendorAddressData {
	address: AddressData;
	vendor?: VendorData;
	vendorAddressId: string;
	vendorId: string;
	name: string;
	timestamp: string;
}

export interface LocationData {
	address: AddressData;
	isActive: boolean;
	isDefault: boolean;
	locationId: string;
	name: string;
	timestamp: string;
}

export interface CustomerBalanceData {
	balance: string;
	currency: CurrencyData;
	currencyId: string;
	customer?: CustomerData;
	customerBalanceId: string;
	customerId: string;
}

export interface VendorBalanceData {
	balance: string;
	currency: CurrencyData;
	currencyId: string;
	vendor?: VendorData;
	vendorBalanceId: string;
	vendorId: string;
}

export interface CustomerCreditData {
	balance: string;
	currency: CurrencyData;
	currencyId: string;
	customer?: CustomerData;
	customerCreditId: string;
	customerId: string;
}

export interface VendorCreditData {
	balance: string;
	currency: CurrencyData;
	currencyId: string;
	vendor?: VendorData;
	vendorCreditId: string;
	vendorId: string;
}

export interface PaymentTermData {
	daysDue: number;
	isActive: boolean;
	name: string;
	paymentTermsId: string;
	timestamp: string;
}

export interface OrderHistoryData {
	id: string;
	lastOrderDate: string;
}

export interface ImageData {
	imageId: string;
	largeUrl: string;
	mediumUrl: string;
	originalUrl: string;
	smallUrl: string;
	thumbUrl: string;
}

export interface CategoryData {
	categoryId: string;
	isDefault: boolean;
	name: string;
	parentCategory?: CategoryData | null;
	parentCategoryId: string | null;
	timestamp: string;
}

export interface StructuredCategoryData extends CategoryData {
	children?: StructuredCategoryData[];
}

export interface GroupedProductsByCategoryData {
	categoryId: string;
	name: string;
	products: ProductData[];
}

export interface ProductCostData {
	cost: string;
	product?: ProductData;
	productCostId: string;
	productId: string;
}

export interface PriceData {
	fixedMarkup: string;
	priceType: string;
	pricingScheme?: PricingSchemeData;
	pricingSchemeId: string;
	product?: ProductData;
	productId: string;
	productPriceId: string;
	timestamp: string;
	unitPrice: string;
}

export interface IventoryLineData {
	inventoryLineId: string;
	location: LocationData;
	locationId: string;
	product?: ProductData;
	productId: string;
	quantityOnHand: string;
	serial: string;
	sublocation: string;
	timestamp: string;
}

export interface ShipLineData {
	carrier: string;
	containers: string[];
	easyPostConfirmationEmailAddress: string;
	easyPostShipmentId: string;
	easyPostShipmentStatus: string;
	salesOrderShipLineId: string;
	shippedDate: string;
	timestamp: string;
	trackingNumber: string;
}

export interface QuantityData {
	serialNumbers: string[];
	standardQuantity: string;
	uom: string;
	uomQuantity: string;
}

export interface ItemBomData {
	childProduct?: ProductData;
	childProductId: string;
	itemBomId: string;
	product?: ProductData;
	productId: string;
	quantity: QuantityData;
	timestamp: string;
}

export interface TaxCodeData {
	isActive: boolean;
	name: string;
	tax1Rate: string;
	tax2Rate: string;
	taxCodeId: string;
	taxingScheme?: TaxingSchemeData;
	taxingSchemeId: string;
	timestamp: string;
}

export interface TaxingSchemeData {
	calculateTax2OnTax1: boolean;
	defaultTaxCode: TaxCodeData;
	defaultTaxCodeId: string;
	isActive: boolean;
	isDefault: boolean;
	name: string;
	tax1Name: string;
	tax1OnShipping: boolean;
	tax2Name: string;
	tax2OnShipping: boolean;
	taxCodes: TaxCodeData[];
	taxingSchemeId: string;
	timestamp: string;
}

export interface TaxCodeGroupData {
	product?: ProductData;
	productId: string;
	productTaxCodeId: string;
	taxCode: TaxCodeData;
	taxCodeId: string;
	taxingScheme: TaxingSchemeData;
	taxingSchemeId: string;
	timestamp: string;
}

export interface VendorItemData {
	cost: string;
	lineNum: string;
	product?: ProductData;
	productId: string;
	timestamp: string;
	vendor?: VendorData;
	vendorId: string;
	vendorItemCode: string;
	vendorItemId: string;
}

export interface VendorData {
	addresses: VendorAddressData[];
	balances: VendorBalanceData[];
	contactName: string;
	credits: VendorCreditData[];
	currency: CurrencyData;
	currencyId: string;
	customFields: Record<string, string>;
	defaultAddress: VendorAddressData;
	defaultAddressId: string;
	defaultCarrier: string;
	defaultPaymentMethod: string;
	defaultPaymentTerms: PaymentTermData;
	defaultPaymentTermsId: string;
	discount: string;
	email: string;
	fax: string;
	isActive: boolean;
	isTaxInclusivePricing: boolean;
	lastModifiedBy: TeamMemberData;
	lastModifiedById: string;
	leadTimeDays: number;
	name: string;
	phone: string;
	remarks: string;
	taxingScheme: TaxingSchemeData;
	taxingSchemeId: string;
	timestamp: string;
	vendorId: string;
	vendorItems: VendorItemData[];
	website: string;
}

export interface ProductOperationTypeData {
	isActive: boolean;
	isDefault: boolean;
	name: string;
	operationTypeId: string;
	timestamp: string;
}

export interface ProductOperationData {
	cost: string;
	estimatedMinutes: string;
	estimatedSeconds: string;
	instructions: string;
	lineNum: string;
	operationType: ProductOperationTypeData;
	operationTypeId: string;
	perHourCost: string;
	product?: ProductData;
	productId: string;
	productOperationId: string;
	timestamp: string;
}

export interface ConversionRatioData {
	standardQuantity: string;
	uomQuantity: string;
}

export interface UomData {
	conversionRatio: ConversionRatioData;
	name: string;
}

export interface ReorderSettingData {
	defaultSublocation: string;
	enableReordering: boolean;
	fromLocation: LocationData;
	fromLocationId: string;
	location: LocationData;
	locationId: string;
	product?: ProductData;
	productId: string;
	reorderMethod: string;
	reorderPoint: string;
	reorderQuantity: string;
	reorderSettingsId: string;
	timestamp: string;
	vendor: VendorData;
	vendorId: string;
}

export interface ProductData {
	// Non-null-able props
	productId: string;
	sku: string;
	name: string;
	description: string;
	width: string;
	height: string;
	length: string;
	weight: string;
	autoAssembler: boolean;
	barcode: string;
	categoryId: string;
	defaultImageId: string;
	hsTariffNumber: string;
	includeQuantityBuildable: boolean;
	isActive: boolean;
	isManufacturable: boolean;
	itemType: string;
	lastModifiedById: string;
	lastModifiedDateTime: string;
	lastVendorId: string;
	originCountry: string;
	remarks: string;
	standardUomName: string;
	timestamp: string;
	totalQuantityOnHand: string;
	trackSerials: boolean;

	// null-able props.
	customFields?: CustomFieldRecord | null;
	category?: CategoryData | null;
	cost?: ProductCostData | null;
	defaultImage?: ImageData | null;
	images?: ImageData[];
	defaultPrice?: PriceData | null;
	inventoryLines?: IventoryLineData[];
	itemBoms?: ItemBomData[] | null;
	lastModifiedBy?: TeamMemberData | null;
	lastVendor?: VendorData | null;
	prices?: PriceData[];
	productCustomFieldLabels?: Record<string, string> | null;
	productOperations?: ProductOperationData[];
	purchasingUom?: UomData | null;
	reorderSettings?: ReorderSettingData[];
	salesUom?: UomData | null;
	taxCodes?: TaxCodeGroupData[];
	vendorItems?: VendorItemData[];
}

export interface CustomFieldRecord {
	custom1: string;
	custom2: string;
	custom3: string;
	custom4: string;
	custom5: string;
	custom6: string;
	custom7: string;
	custom8: string;
	custom9: string;
	custom10: string;
}

export interface CustomFieldPrintLabelsData {
	custom1Print: boolean;
	custom2Print: boolean;
	custom3Print: boolean;
}

export interface CustomFieldsData {
	customFieldsId: string;
	customerCustomFieldLabels: CustomFieldRecord;
	productCustomFieldLabels: CustomFieldRecord;
	purchaseOrderCustomFieldLabels: CustomFieldRecord;
	purchaseOrderCustomFieldPrintLabels: CustomFieldPrintLabelsData;
	salesOrderCustomFieldLabels: CustomFieldRecord;
	salesOrderCustomFieldPrintLabels: CustomFieldPrintLabelsData;
	stockAdjustmentCustomFieldLabels: CustomFieldRecord;
	stockAdjustmentCustomFieldPrintLabels: CustomFieldPrintLabelsData;
	stockTransferCustomFieldLabels: CustomFieldRecord;
	stockTransferCustomFieldPrintLabels: CustomFieldPrintLabelsData;
	vendorCustomFieldLabels: CustomFieldRecord;
	workOrderCustomFieldLabels: CustomFieldRecord;
	workOrderCustomFieldPrintLabels: CustomFieldPrintLabelsData;
}

export interface PricingSchemeData {
	// Non-nullable properties.
	pricingSchemeId: string;
	name: string;
	currencyId: string;
	isActive: boolean;
	isDefault: boolean;
	isTaxInclusive: boolean;
	timestamp: string;

	// Nullable properties.
	currency?: CurrencyData | null;
	productPrices?: PriceData[] | null;
}

export interface PriceUnitData {
	isPercent: boolean;
	value: string;
}

export interface CustomerData {
	// Non-nullable props
	customerId: string;
	email: string;
	isActive: boolean;
	name: string;
	contactName: string;
	phone: string;
	website: string;
	defaultBillingAddressId: string;
	defaultCarrier: string;
	defaultLocationId: string;
	defaultPaymentMethod: string;
	defaultPaymentTermsId: string;
	defaultSalesRep: string;
	defaultSalesRepTeamMemberId: string;
	defaultShippingAddressId: string;
	fax: string;
	pricingSchemeId: string;
	discount: string;
	taxingSchemeId: string;
	lastModifiedById: string;
	remarks: string;
	taxExemptNumber: string;
	timestamp: string;

	// Nullable props
	customFields?: Record<string, string> | null;
	addresses?: CustomerAddressData[];
	balances?: CustomerBalanceData[];
	credits?: CustomerCreditData[];
	defaultBillingAddress?: CustomerAddressData | null;
	defaultLocation?: LocationData | null;
	defaultPaymentTerms?: PaymentTermData | null;
	defaultSalesRepTeamMember?: TeamMemberData | null;
	defaultShippingAddress?: CustomerAddressData | null;
	lastModifiedBy?: TeamMemberData | null;
	orderHistory?: OrderHistoryData | null;
	pricingScheme?: PricingSchemeData | null;
	taxingScheme?: TaxingSchemeData | null;
}

export interface SalesOrderLineData {
	description: string;
	discount: PriceUnitData;
	isDiscarded: boolean;
	product?: ProductData;
	productId: string;
	quantity: QuantityData;
	returnDate: string;
	salesOrderLineId: string;
	serviceCompleted: boolean;
	subTotal: string;
	tax1Rate: string;
	tax2Rate: string;
	taxCode: TaxCodeData;
	taxCodeId: string;
	timestamp: string;
	unitPrice: string;
}

export interface PackLineData {
	containerNumber: string;
	description: string;
	product: ProductData;
	productId: string;
	quantity: QuantityData;
	salesOrderPackLineId: string;
	timestamp: string;
}

export interface PaymentLineData {
	amount: string;
	datePaid: string;
	lineNum: number;
	paymentMethod: string;
	paymentType: string;
	referenceNumber: string;
	remarks: string;
	salesOrderPaymentHistoryLineId: string;
	timestamp: string;
}

export interface PickLineData {
	description: string;
	location: LocationData;
	locationId: string;
	pickDate: string;
	product: ProductData;
	productId: string;
	quantity: QuantityData;
	salesOrderPickLineId: string;
	sublocation: string;
	timestamp: string;
}

export interface RestockLineData {
	description: string;
	location: LocationData;
	locationId: string;
	product: ProductData;
	productId: string;
	quantity: QuantityData;
	restockDate: string;
	salesOrderRestockLineId: string;
	sublocation: string;
	timestamp: string;
}

export interface SalesOrderOptionData {
	pol?: string;
	stampedForms?: Array<
		"sales-quote" | "sales-order" | "packing-list" | "invoice" | "job-order"
	>;
}

export interface SalesOrderData {
	amountPaid: string;
	assignedToTeamMember?: TeamMemberData;
	assignedToTeamMemberId: string;
	balance: string;
	billingAddress: AddressData;
	calculateTax2OnTax1: boolean;
	confirmerTeamMember?: TeamMemberData;
	confirmerTeamMemberId: string;
	contactName: string;
	costOfGoodsSold?: CostOfGoodsSoldData;
	currency?: CurrencyData;
	currencyId: string;
	customFields: Record<string, string>;
	customer?: CustomerData;
	customerId: string;
	dueDate: string;
	email: string;
	exchangeRate: string;
	externalId: string;
	inventoryStatus: string;
	invoicedDate: string;
	isCancelled: boolean;
	isCompleted: boolean;
	isInvoiced: boolean;
	isPrioritized: boolean;
	isQuote: boolean;
	isTaxInclusive: boolean;
	lastModifiedBy?: TeamMemberData;
	lastModifiedById: string;
	lines?: SalesOrderLineData[];
	location?: LocationData;
	locationId: string;
	needsConfirmation: boolean;
	nonCustomerCost: PriceUnitData;
	orderDate: string;
	orderFreight: string;
	orderNumber: string;
	orderRemarks: string;
	packLines?: PackLineData[];
	packRemarks: string;
	paymentLines?: PaymentLineData[];
	paymentStatus: string;
	paymentTerms?: PaymentTermData;
	paymentTermsId: string;
	phone: string;
	pickLines?: PickLineData[];
	pickRemarks: string;
	poNumber: string;
	pricingScheme?: PricingSchemeData;
	pricingSchemeId: string;
	requestedShipDate: string;
	restockLines?: RestockLineData[];
	restockRemarks: string;
	returnFee: string;
	returnFreight: string;
	returnRemarks: string;
	salesOrderId: string;
	salesRep: string;
	salesRepTeamMember?: TeamMemberData;
	salesRepTeamMemberId: string;
	sameBillingAndShipping: boolean;
	shipLines?: ShipLineData[];
	shipRemarks: string;
	shipToCompanyName: string;
	shippingAddress: AddressData;
	showShipping: boolean;
	source: string;
	subTotal: string;
	tax1: string;
	tax1Name: string;
	tax1OnShipping: boolean;
	tax1Rate: string;
	tax2: string;
	tax2Name: string;
	tax2OnShipping: boolean;
	tax2Rate: string;
	taxingScheme?: TaxingSchemeData;
	taxingSchemeId: string;
	timestamp: string;
	total: string;
}

export interface CustomProductData {
	id: number;
	customerId: string;
	customerName: string;
	customer?: CustomerData;
	productId: string;
	productName: string;
	productSku: string;
	product?: ProductData;
	customerProductCode: string;
	customerProductName: string;
}

export interface JobOrderProductRemarksData {
	id: number;
	createdAt?: string;
	updatedAt?: string;
	teamMemberId?: string;
	salesOrderId: string;
	salesOrderLineId: string;
	productId: string;
	remarks?: string;
	images?: string;
}

export interface JobOrderCustomColumnData {
	id: number;
	createdAt?: string;
	updatedAt?: string;
	teamMemberId?: string;
	salesOrderId: string;
	name: string;
	values: Record<string, string>;
}

export interface JobOrderProductImagesData {
	id: number;
	createdAt?: string;
	updatedAt?: string;
	teamMemberId?: string;
	salesOrderId: string;
	productId: string;
	images?: string;
}

export interface ProductAttributeData {
	id: number;
	createdAt?: string;
	updatedAt?: string;
	productId: string;
	packing: string;
	moq: number;
}

export interface ProductMetaData {
	id: number;
	createdAt?: string;
	updatedAt?: string;
	productId: string;
	metaKey: string;
	metaValue: string;
}

export interface ProductCollectionData {
	id: number;
	parentId?: number;
	name: string;
	slug: string;
	category?: string;
	coverImageUrl?: string;
}

export interface NestedProductCollectionData extends ProductCollectionData {
	children?: NestedProductCollectionData[];
}

export interface ProductCollectionRelationshipData {
	id: number;
	collectionId: number;
	productId: string;
}

export interface EmailFromOrToData {
	email: string;
	name?: string;
}

export type SupabaseSuccessAuthResult = AuthTokenResponsePassword["data"];

export type SessionData = Session;

export interface ProfileData {
	id: string;
	email: string;
	role: string;
	firstName: string;
	lastName: string;
	inflowUserId?: string;
	phone?: string;
	address?: string;
	avatarUrl?: string;
}

export interface RegistrationData {
	email: string;
	password: string;
	role: string;
	firstName: string;
	lastName: string;
	inflowUserId: string;
	phone: string;
	address: string;
	avatarUrl: string;
}

export interface ProfileUpdateData {
	id: string;
	email?: string;
	password?: string;
	role?: string;
	firstName?: string;
	lastName?: string;
	inflowUserId?: string;
	phone?: string;
	address?: string;
	avatarUrl?: string;
}

export interface NestedCollectionWithProductsData {
	name: string;
	slug?: string;
	children?: NestedCollectionWithProductsData[];
	products?: {
		sku?: string;
		description?: string;
		productId?: string;
	};
}

export type CustomerPricingSchemeData = {
	pricingSchemeId: string;
	pricingSchemeSlug: string;
	pricingSchemeName: string;
	customerId: string;
	customerName: string;
};

export type CustomerProductData = {
	productId: string;
	name: string;
	sku: string;
	collapsedWidth?: number;
	width: number;
	height: number;
	depth: number;
	nettWeight: number;
	grossWeight: number;
	m3: number;
	price: number;
	wood: string;
	packing: string;
	moq: number;
	imageSmallUrl: string;
	imageMediumUrl: string;
	imageLargeUrl: string;
	customerId: string;
	customerPriceId: string;
	customerPrice: number;
};

export type CustomerPriceListOptionData = {
	issuedDate: string;
};

export type CustomerPriceListPageData = {
	pricingScheme: PricingSchemeData;
	fobPricingScheme: PricingSchemeData;
	customerPricingScheme: CustomerPricingSchemeData;
	prices: PriceData[];
	productsFobPrice: Record<string, string>;
	issuedDate: string;
	customProductDataCollection: Record<string, CustomProductData>;
};

export interface ReceiveLineData {
	description: string;
	location: LocationData;
	locationId: string;
	product?: ProductData;
	productId: string;
	purchaseOrderReceiveLineId: string;
	quantity: QuantityData;
	receiveDate: string;
	subLocation: string;
	timestamp: string;
	vendorItemCode: string;
}

export interface PurchaseOrderLineData {
	description: string;
	discount: PriceUnitData;
	product?: ProductData;
	productId: string;
	purchaseOrderLineId: string;
	quantity: QuantityData;
	returnDate: string;
	serviceCompleted: boolean;
	subTotal: string;
	tax1Rate: string;
	tax2Rate: string;
	taxCode: TaxCodeData;
	taxCodeId: string;
	timestamp: string;
	unitPrice: string;
	vendorItemCode: string;
}

export interface PurchaseOrderData {
	amountPaid: string;
	approverTeamMember?: TeamMemberData;
	approverTeamMemberId: string;
	assignedToTeamMember?: TeamMemberData;
	assignedToTeamMemberId: string;
	balance: string;
	calculateTax2OnTax1: boolean;
	carrier: string;
	contactName: string;
	currency?: CurrencyData;
	currencyId: string;
	customFields: CustomFieldRecord;
	dueDate: string;
	email: string;
	exchangeRate: string;
	exchangeRateAutoPulled: string;
	freight: string;
	inventoryStatus: string;
	isCancelled: boolean;
	isCompleted: boolean;
	isQuote: boolean;
	isTaxInclusive: boolean;
	lastModifiedBy?: TeamMemberData;
	lines?: PurchaseOrderLineData[];
	location?: LocationData;
	locationId: string;
	nonVendorCosts?: PriceUnitData;
	orderDate: string;
	orderNumber: string;
	orderRemarks: string;
	paymentLines?: PaymentLineData;
	paymentStatus: string;
	paymentTerms?: PaymentTermData;
	paymentTermsId: string;
	phone: string;
	purchaseOrderId: string;
	receiveLines?: ReceiveLineData[];
	receiveRemarks: string;
	requestShipDate: string;
	returnExtra: string;
	returnFee: string;
	returnRemarks: string;
	shipToAddress?: AddressData;
	shipToCompanyName: string;
	showShipping: boolean;
	subTotal: string;
	tax1: string;
	tax1Name: string;
	tax1OnShipping: boolean;
	tax1Rate: string;
	tax2: string;
	tax2Name: string;
	tax2OnShipping: boolean;
	tax2Rate: string;
	taxingScheme?: TaxingSchemeData;
	taxingSchemeId: string;
	timestamp: string;
	total: string;
	unstockLines?: UnstockLineData[];
	unstockRemarks: string;
	vendor?: VendorData;
	vendorAddress?: AddressData;
	vendorId: string;
	vendorOrderNumber: string;
}

export interface UnstockLineData {
	description: string;
	location: LocationData;
	locationId: string;
	product?: ProductData;
	productId: string;
	purchaseOrderReceiveLineId: string;
	quantity: QuantityData;
	restockDate: string;
	subLocation: string;
	timestamp: string;
	vendorItemCode: string;
}
