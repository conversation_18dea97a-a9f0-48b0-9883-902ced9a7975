import ky from "ky";
import { inflowConfig, inflowHttpHeader } from "../../configs/server-config";
import { CustomerData } from "../../types/dto";
import { CustomerListResponse, CustomerResponse } from "~/types/response";
import { getErrorMessage } from "~/utils/http-util";
import { toCustomerData } from "~/utils/dto-util";

export async function fetchCustomers(props?: {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	keyword?: string;
	filters?: Record<string, any>;
}): Promise<CustomerListResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? inflowConfig().loadMorePerPage;
	const include =
		props?.include ?? "addresses,defaultLocation,balances,credits";

	let apiUrl = `${baseApiUrl}/customers?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.keyword) {
		apiUrl += `&filter[smart]=${props.keyword}`;
	}

	if (props?.filters) {
		for (const [key, value] of Object.entries(props.filters)) {
			const val = value.toString();
			apiUrl += `&filter[${key}]=${val}`;
		}
	}

	try {
		const rawCustomers = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json();

		const customers: CustomerData[] = [];

		if (Array.isArray(rawCustomers)) {
			for (const customer of rawCustomers) {
				const customerData = toCustomerData(customer);
				if (customerData) customers.push(customerData);
			}
		}

		return {
			success: true,
			message: "Customers fetched successfully",
			data: customers,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findCustomer(id: string): Promise<CustomerResponse> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;
	const include = "addresses,defaultLocation,balances,credits";

	try {
		const rawCustomer = await ky
			.get(`${baseApiUrl}/customers/${id}?include=${include}`, {
				headers: inflowHttpHeader(),
			})
			.json();

		return {
			success: true,
			message: "Customer fetched successfully",
			data: toCustomerData(rawCustomer),
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
