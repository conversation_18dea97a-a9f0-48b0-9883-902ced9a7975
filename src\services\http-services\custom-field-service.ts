import ky from "ky";
import { inflowConfig, inflowHttpHeader } from "../../configs/server-config";
import { CustomFieldsData } from "../../types/dto";

export interface FetchCustomFieldsProps {
	includeCount?: boolean;
}

export async function fetchCustomFields(
	props?: FetchCustomFieldsProps,
): Promise<CustomFieldsData | null> {
	"use server";

	const baseApiUrl = inflowConfig().baseInflowApiUrl;
	const includeCount = props?.includeCount ?? false;

	let apiUrl = `${baseApiUrl}/custom-fields?includeCount=${includeCount}`;

	try {
		const customFields = await ky
			.get(apiUrl, {
				headers: inflowHttpHeader(),
			})
			.json<CustomFieldsData>();

		return customFields;
	} catch (e: unknown) {
		return null;
	}
}
