import { For, Show } from "solid-js";
import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
	useSearchParams,
} from "@solidjs/router";
import { isAuthenticated } from "~/services/http-services/auth-service";
import { getRequestURL } from "vinxi/http";
import { AppUrlData } from "~/types/misc";
import LoadingSpinner from "~/components/LoadingSpinner";
import CollectionGalleryHeader from "~/components/collections-gallery/CollectionGalleryHeader";
import CollectionGalleryPageHeader from "~/components/collections-gallery/CollectionGalleryPageHeader";
import CollectionGalleryGridItemSimple from "~/components/collections-gallery/CollectionGalleryGridItemSimple";
import { getSearchKeyword } from "~/utils/search-util";
import { dbFindCollectionCategory } from "~/services/db-services/collection-db-service";

const getPageData = query(async (collectionId: string) => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const urlObject = getRequestURL();
	const baseUrl = urlObject.origin;

	const urls: AppUrlData = {
		baseUrl: baseUrl,
		baseApiUrl: `${baseUrl}/api`,
		apiUrl: `${baseUrl}/api/custom-product-data`,
		currentUrl: urlObject.href,
	};

	const collection = await dbFindCollectionCategory(collectionId);

	return {
		urls,
		collection,
	};
}, "collectionGalleryIdPageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function CollectionGalleryIdPage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	let searchFieldRef: HTMLInputElement | undefined;

	const [searchParams, setSearchParams] = useSearchParams();

	return (
		<>
			<Show when={!pageData()}>
				<div class="relative flex h-full w-full items-center justify-center">
					<LoadingSpinner class="mt-14" />
				</div>
			</Show>

			<Show when={pageData()}>
				<CollectionGalleryPageHeader
					collectionsRoot={{
						text: "Collections Gallery",
						slug: "collections-gallery",
					}}
					collection={pageData()?.collection ?? undefined}
					searchFieldRef={searchFieldRef}
					keyword={getSearchKeyword(searchParams)}
				/>

				<Show when={!pageData()?.collection}>
					{"Failed to load collections, please try again later."}
				</Show>

				<Show
					when={
						pageData()?.collection &&
						pageData()?.collection?.children &&
						pageData()?.collection?.children?.length
					}
				>
					<div class="pt-40 pb-8">
						<div class="relative container">
							<CollectionGalleryHeader
								collectionsRoot={{
									text: "Collections Gallery",
									slug: "collections-gallery",
								}}
								category={pageData()?.collection!}
							/>

							<div class="mt-12 grid w-full grid-cols-1 place-items-center gap-5 px-[1rem] sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 2xl:px-0">
								<For each={pageData()?.collection?.children ?? []}>
									{(collection, index) => (
										<CollectionGalleryGridItemSimple
											collectionName={collection.name}
											linkUrl={`/collections-gallery/${props.params.id}/${collection.categoryId}/`}
										/>
									)}
								</For>
							</div>
						</div>
					</div>
				</Show>
			</Show>
		</>
	);
}
