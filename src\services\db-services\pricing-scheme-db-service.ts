import { db } from "~/db";
import { eq, inArray } from "drizzle-orm";
import { SetupDataResult } from "~/types/response";
import { pricingSchemesTable } from "~/db/schema";
import { PricingSchemeData } from "~/types/dto";
import { fetchPricingSchemes } from "../http-services/pricing-scheme-service";
import { slugToText } from "~/utils/formatting-util";
import { inflowConfig } from "~/configs/server-config";
import { toPricingSchemeData } from "~/utils/dto-util";

export async function dbFindPricingScheme(pricingSchemeId: string) {
	"use server";

	const results = await db
		.select()
		.from(pricingSchemesTable)
		.where(eq(pricingSchemesTable.pricingSchemeId, pricingSchemeId))
		.limit(1);

	if (!Array.isArray(results) || results.length === 0) {
		return null;
	}

	const pricingScheme = toPricingSchemeData(results[0]);

	if (!pricingScheme) return null;

	return pricingScheme;
}

export async function dbFindPricingSchemeByName(pricingSchemeName: string) {
	"use server";

	const results = await db
		.select()
		.from(pricingSchemesTable)
		.where(eq(pricingSchemesTable.name, pricingSchemeName))
		.limit(1);

	if (!Array.isArray(results) || results.length === 0) {
		return null;
	}

	const pricingScheme = toPricingSchemeData(results[0]);

	if (!pricingScheme) return null;

	return pricingScheme;
}

export async function dbFindPricingSchemeBySlug(pricingSchemeSlug: string) {
	("use server");

	// Convert to name (case-insensitive), because the table doesn't have slug column.
	const pricingSchemeName = slugToText(pricingSchemeSlug);

	// Suffix with " $" to match the name format.
	const pricingSchemeNameWithSuffix = pricingSchemeName + " $";

	let record = await dbFindPricingSchemeByName(pricingSchemeNameWithSuffix);

	if (!record) {
		record = await dbFindPricingSchemeByName(pricingSchemeName);
	}

	return record;
}

export async function dbFindDefaultPricingScheme() {
	"use server";

	return await dbFindPricingScheme(inflowConfig().defaultPricingSchemeId);
}

export async function dbFetchPricingSchemes(props?: {
	pricingSchemeIds?: string[];
}) {
	"use server";

	const pricingSchemeList: PricingSchemeData[] = [];

	if (props) {
		if (props.pricingSchemeIds && props.pricingSchemeIds.length) {
			const records = await db
				.select()
				.from(pricingSchemesTable)
				.where(
					inArray(pricingSchemesTable.pricingSchemeId, props.pricingSchemeIds),
				);

			for (const record of records) {
				const pricingScheme = toPricingSchemeData(record);
				if (!pricingScheme) continue;

				pricingSchemeList.push(pricingScheme);
			}

			return pricingSchemeList;
		}
	}

	const records = await db.select().from(pricingSchemesTable);

	for (const record of records) {
		const pricingScheme = toPricingSchemeData(record);
		if (!pricingScheme) continue;

		pricingSchemeList.push(pricingScheme);
	}

	return pricingSchemeList;
}

/**
 * Insert a pricing scheme to the database.
 */
export async function dbInsertPricingScheme(pricingScheme: PricingSchemeData) {
	"use server";

	const [header] = await db.insert(pricingSchemesTable).values(pricingScheme);

	return header.affectedRows > 0 ? pricingScheme.pricingSchemeId : false;
}

/**
 * Update a pricing scheme in the database.
 */
export async function dbUpdatePricingScheme(
	pricingSchemeId: string,
	data: Partial<Omit<PricingSchemeData, "id" | "pricingSchemeId">>,
) {
	"use server";

	const [header] = await db
		.update(pricingSchemesTable)
		.set(data)
		.where(eq(pricingSchemesTable.pricingSchemeId, pricingSchemeId));

	return header.affectedRows > 0 ? pricingSchemeId : false;
}

/**
 * Insert pricing schemes to the database.
 */
export async function dbInsertPricingSchemes(
	pricingSchemes: PricingSchemeData[],
) {
	"use server";

	const [header] = await db.insert(pricingSchemesTable).values(pricingSchemes);

	return header.affectedRows > 0 ? true : false;
}

/**
 * Update pricing schemes in the database.
 */
export async function dbUpdatePricingSchemes(
	list: {
		pricingSchemeId: string;
		data: Partial<Omit<PricingSchemeData, "id" | "pricingSchemeId">>;
	}[],
) {
	"use server";

	const results = await Promise.all(
		list.map(async (item) => {
			const [header] = await db
				.update(pricingSchemesTable)
				.set(item.data)
				.where(eq(pricingSchemesTable.pricingSchemeId, item.pricingSchemeId));

			return {
				pricingSchemeId: item.pricingSchemeId,
				status: header.affectedRows > 0,
			};
		}),
	);

	return results.some((result) => result.status);
}

export async function dbCollectPricingSchemes(props?: {
	after?: string;
}): Promise<SetupDataResult> {
	"use server";

	const count = 100;

	const remoteResponse = await fetchPricingSchemes({
		count: count,
		include: "currency",
		after: props?.after,
	});

	if (!remoteResponse.success || !remoteResponse.data) {
		return {
			success: false,
			message: remoteResponse.message,
			meta: {
				args: {
					after: props?.after,
				},
				errorCode: "remote_fetch_error",
			},
		};
	}

	if (remoteResponse.data.length > 0) {
		const pricingSchemeIds = remoteResponse.data.map(
			(pricingScheme) => pricingScheme.pricingSchemeId,
		);

		const existingRecords = await dbFetchPricingSchemes({
			pricingSchemeIds: pricingSchemeIds,
		});

		const existingRecordIds: string[] = [];

		for (const existingRecord of existingRecords) {
			existingRecordIds.push(existingRecord.pricingSchemeId);
		}

		const newInsertList = remoteResponse.data.filter(
			(pricingScheme) =>
				!existingRecordIds.includes(pricingScheme.pricingSchemeId),
		);
		const newUpdateList = remoteResponse.data.filter((pricingScheme) =>
			existingRecordIds.includes(pricingScheme.pricingSchemeId),
		);

		let anySuccess = false;

		if (newInsertList.length) {
			const [header] = await db
				.insert(pricingSchemesTable)
				.values(newInsertList);

			if (header.affectedRows) anySuccess = true;
		}

		if (newUpdateList.length) {
			const updateResult = await dbUpdatePricingSchemes(
				newUpdateList.map((pricingScheme) => {
					// The `pricingSchemeProps` here should be `PricingSchemeData` but without `id` and `pricingSchemeId` properties.
					const pricingSchemeProps: Record<string, any> = { ...pricingScheme };

					// Now remove `id` (if exists) and `pricingSchemeId` from `pricingSchemeProps` object.
					if ("id" in pricingSchemeProps) delete pricingSchemeProps.id;
					delete pricingSchemeProps.pricingSchemeId;

					return {
						pricingSchemeId: pricingScheme.pricingSchemeId,
						data: pricingSchemeProps,
					};
				}),
			);

			if (updateResult) anySuccess = true;
		}

		if (!anySuccess) {
			return {
				success: false,
				message: "Failed to insert or update pricing schemes",
				meta: {
					args: {
						after: props?.after,
					},
					hasMore: remoteResponse.data.length < count ? false : true,
					errorCode: "db_insert_error",
					lastId:
						remoteResponse.data[remoteResponse.data.length - 1].pricingSchemeId,
				},
			};
		}
	}

	const isFinished = remoteResponse.data.length < count;

	return {
		success: true,
		message: isFinished
			? "Finished collecting pricing shcemes"
			: "Customers collected successfully",
		meta: {
			args: {
				after: props?.after,
			},
			hasMore: isFinished ? false : true,
			lastId: remoteResponse.data.length
				? remoteResponse.data[remoteResponse.data.length - 1].pricingSchemeId
				: undefined,
		},
	};
}
